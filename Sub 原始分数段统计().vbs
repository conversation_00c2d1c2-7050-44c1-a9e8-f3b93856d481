
Sub 原始分数段统计()
    ' Main procedure to orchestrate the entire process
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual

    GenerateStatistics

    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic

    MsgBox "处理完成！", vbInformation, "成功"
End Sub

Sub GenerateStatistics()
    ' Generate statistics in "原始分数段统计" based on score ranges
    Dim wsScores As Worksheet
    Dim wsStats As Worksheet
    Dim lastRow As Long, lastRowStats As Long
    Dim i As Long, j As Long
    Dim schoolDict As Object
    Dim schoolList() As String
    Dim schoolCount As Integer
    Dim schoolRow As Long
    Dim targetGrade As String

    ' Create a dictionary to store school information
    Set schoolDict = CreateObject("Scripting.Dictionary")

    ' Get the worksheets
    On Error Resume Next
    Set wsScores = ThisWorkbook.Worksheets("成绩总表")
    Set wsStats = ThisWorkbook.Worksheets("原始分数段统计")
    On Error GoTo 0

    If wsScores Is Nothing Then
        MsgBox "找不到'成绩总表'工作表！", vbExclamation, "错误"
        Exit Sub
    End If

    If wsStats Is Nothing Then
        MsgBox "找不到'原始分数段统计'工作表！", vbExclamation, "错误"
        Exit Sub
    End If

 

    ' Delete all rows below row 4 in the statistics sheet
    If wsStats.Rows.Count > 4 Then
        wsStats.Rows("5:" & wsStats.Rows.Count).Delete
        ' Clear formatting only for rows 5 and below, not the entire sheet
        If wsStats.UsedRange.Rows.Count >= 5 Then
            wsStats.Range("A5:" & wsStats.Cells(wsStats.Rows.Count, wsStats.Columns.Count).Address).ClearFormats
        End If
    End If

    ' Find the last row with data in both sheets
    lastRow = wsScores.Cells(wsScores.Rows.Count, 1).End(xlUp).Row
    lastRowStats = 5 ' Statistics start from row 5

    ' Get target grade from cell D2 in the statistics sheet
    targetGrade = wsStats.Range("D2").value
    If Trim(targetGrade) = "" Then
        MsgBox "请在D2单元格输入要查询的年级！", vbExclamation, "错误"
        Exit Sub
    End If

    ' Find column indices in the scores sheet
    Dim colGrade As Integer, colSchoolId As Integer, colSchool As Integer, colClass As Integer, colWeighted As Integer, colName As Integer, colTotal As Integer
    colGrade = 0: colSchoolId = 0: colSchool = 0: colClass = 0: colWeighted = 0: colName = 0: colTotal = 0

    ' Find column indices in the scores sheet (assuming headers are in row 1)
    For i = 1 To wsScores.Cells(1, wsScores.Columns.Count).End(xlToLeft).Column
        Select Case wsScores.Cells(1, i).value
            Case "年级": colGrade = i
            Case "学校序号": colSchoolId = i
            Case "学校": colSchool = i
            Case "班级": colClass = i
            Case "折分": colWeighted = i
            Case "姓名": colName = i
            Case "总分": colTotal = i
        End Select
    Next i

    ' Rows below row 4 have already been deleted at the beginning of the procedure

    ' Collect unique school and class combinations for the target grade
    Dim schoolId As String, school As String, class As String, grade As String
    Dim key As String

    For i = 2 To lastRow ' Assuming data starts from row 2
        grade = wsScores.Cells(i, colGrade).value

        ' Only process rows with matching grade
        If grade = targetGrade Then
            schoolId = wsScores.Cells(i, colSchoolId).value
            school = wsScores.Cells(i, colSchool).value
            class = wsScores.Cells(i, colClass).value

            key = schoolId & "|" & school & "|" & class

            If Not schoolDict.Exists(key) Then
                schoolDict.Add key, lastRowStats
                lastRowStats = lastRowStats + 1
            End If
        End If
    Next i

    ' Prepare the statistics sheet
    ReDim schoolList(1 To schoolDict.Count)
    j = 1
    Dim dictKey As Variant
    Dim parts As Variant
    For Each dictKey In schoolDict.Keys
        parts = Split(dictKey, "|")

        wsStats.Cells(schoolDict(dictKey), 1).value = parts(0) ' School ID
        wsStats.Cells(schoolDict(dictKey), 2).value = parts(1) ' School
        wsStats.Cells(schoolDict(dictKey), 3).value = parts(2) ' Class

        ' Format data cells (A-U) with center alignment and borders
        With wsStats.Cells(schoolDict(dictKey), 1).Resize(1, 21)
            .HorizontalAlignment = xlCenter ' Center text
            .Borders.LineStyle = xlContinuous ' Add borders
            .Borders.Color = RGB(0, 0, 0) ' Black borders
        End With

        schoolList(j) = parts(1) ' Store school names for summary rows
        j = j + 1
    Next dictKey

    ' First pass: Count total students (with names) for each class
    Dim studentKey As String
    Dim rowIndex As Long

    ' Initialize the actual student count (column 4) for each class
    For Each dictKey In schoolDict.Keys
        rowIndex = schoolDict(dictKey)
        wsStats.Cells(rowIndex, 4).value = 0 ' Initialize actual student count
        wsStats.Cells(rowIndex, 5).value = 0 ' Initialize participating student count

        ' Initialize average score and rank columns
        wsStats.Cells(rowIndex, 20).value = 0 ' Initialize total average score column
        wsStats.Cells(rowIndex, 21).value = 0 ' Initialize rank column
    Next dictKey

    ' Count students with names for actual student count
    For i = 2 To lastRow
        grade = wsScores.Cells(i, colGrade).value
        If grade = targetGrade And Not IsEmpty(wsScores.Cells(i, colName).value) Then
            studentKey = wsScores.Cells(i, colSchoolId).value & "|" & wsScores.Cells(i, colSchool).value & "|" & wsScores.Cells(i, colClass).value
            If schoolDict.Exists(studentKey) Then
                rowIndex = schoolDict(studentKey)
                wsStats.Cells(rowIndex, 4).value = wsStats.Cells(rowIndex, 4).value + 1 ' Increment actual student count
            End If
        End If
    Next i

    ' Count students in each score range
    Dim weightedScore As Double, actualScore As Double
    Dim rangeCol As Integer



    For i = 2 To lastRow ' Assuming data starts from row 2
        grade = wsScores.Cells(i, colGrade).value
        If grade = targetGrade And Not IsEmpty(wsScores.Cells(i, colTotal).value) Then

            weightedScore = wsScores.Cells(i, colWeighted).value
            actualScore = IIf(IsEmpty(wsScores.Cells(i, colTotal).value), 0, wsScores.Cells(i, colTotal).value)
            studentKey = wsScores.Cells(i, colSchoolId).value & "|" & wsScores.Cells(i, colSchool).value & "|" & wsScores.Cells(i, colClass).value
            rowIndex = schoolDict(studentKey)

            ' Increment participating student count (with non-zero total scores)
            wsStats.Cells(rowIndex, 5).value = wsStats.Cells(rowIndex, 5).value + 1



            ' Increment score range count based on actual score (instead of weighted score)
            rangeCol = 0

            If actualScore < 470 Then
                rangeCol = 6 ' 470以下
            ElseIf actualScore <= 510 Then
                rangeCol = 7 ' 470-510
            ElseIf actualScore <= 550 Then
                rangeCol = 8 ' 510-550
            ElseIf actualScore <= 590 Then
                rangeCol = 9 ' 550-590
            ElseIf actualScore <= 630 Then
                rangeCol = 10 ' 590-630
            ElseIf actualScore <= 670 Then
                rangeCol = 11 ' 630-670
            ElseIf actualScore <= 710 Then
                rangeCol = 12 ' 670-710
            ElseIf actualScore <= 750 Then
                rangeCol = 13 ' 710-750
            ElseIf actualScore <= 790 Then
                rangeCol = 14 ' 750-790
            ElseIf actualScore <= 830 Then
                rangeCol = 15 ' 790-830
            ElseIf actualScore <= 870 Then
                rangeCol = 16 ' 830-870
            ElseIf actualScore <= 910 Then
                rangeCol = 17 ' 870-910
            ElseIf actualScore <= 950 Then
                rangeCol = 18 ' 910-950
            ElseIf actualScore <= 990 Then
                rangeCol = 19 ' 950-990
            Else
                rangeCol = 20 ' 990以上
            End If

            If IsEmpty(wsStats.Cells(rowIndex, rangeCol).value) Then
                wsStats.Cells(rowIndex, rangeCol).value = 1
            Else
                wsStats.Cells(rowIndex, rangeCol).value = wsStats.Cells(rowIndex, rangeCol).value + 1
            End If
        End If
    Next i

    ' Dictionary to store total scores for each class
    Dim classTotalScores As Object
    Set classTotalScores = CreateObject("Scripting.Dictionary")

    ' Initialize the total scores dictionary
    For Each dictKey In schoolDict.Keys
        classTotalScores.Add dictKey, 0
    Next dictKey

    ' Collect total scores for each class
    For i = 2 To lastRow ' Assuming data starts from row 2
        grade = wsScores.Cells(i, colGrade).value
        If grade = targetGrade And Not IsEmpty(wsScores.Cells(i, colTotal).value) Then
            actualScore = wsScores.Cells(i, colTotal).value
            studentKey = wsScores.Cells(i, colSchoolId).value & "|" & wsScores.Cells(i, colSchool).value & "|" & wsScores.Cells(i, colClass).value

            ' Add total score to the class total
            classTotalScores(studentKey) = classTotalScores(studentKey) + actualScore
        End If
    Next i

    ' Calculate average scores for each class
    For i = 5 To lastRowStats - 1
        If wsStats.Cells(i, 5).value > 0 Then ' Only calculate if there are participating students
            ' Calculate total average score
            Dim classKey As String
            classKey = wsStats.Cells(i, 1).value & "|" & wsStats.Cells(i, 2).value & "|" & wsStats.Cells(i, 3).value

            If classTotalScores.Exists(classKey) Then
                wsStats.Cells(i, 20).value = Round(classTotalScores(classKey) / wsStats.Cells(i, 5).value, 2)
            End If


        End If
    Next i

    ' Rank classes within each school
    Dim uniqueSchools As Object
    Set uniqueSchools = CreateObject("Scripting.Dictionary")

    ' Get unique schools
    For i = 5 To lastRowStats - 1
        Dim schoolName As String
        schoolName = wsStats.Cells(i, 2).value
        If Not uniqueSchools.Exists(schoolName) Then
            uniqueSchools.Add schoolName, Nothing
        End If
    Next i

    ' Rank all classes district-wide
    Dim totalClassCount As Integer
    totalClassCount = 0

    ' Count all classes (non-summary rows)
    For i = 5 To lastRowStats - 1
        If Not wsStats.Cells(i, 3).MergeCells Then ' Only count non-summary rows
            totalClassCount = totalClassCount + 1
        End If
    Next i

    If totalClassCount > 0 Then
        ' Create arrays to store all class indices and scores
        Dim allClassIndices() As Integer
        Dim allClassScores() As Double
        ReDim allClassIndices(1 To totalClassCount)
        ReDim allClassScores(1 To totalClassCount)

        ' Fill arrays with all class indices and scores
        Dim allIdx As Integer
        allIdx = 1
        For i = 5 To lastRowStats - 1
            If Not wsStats.Cells(i, 3).MergeCells Then ' Only include non-summary rows
                allClassIndices(allIdx) = i
                allClassScores(allIdx) = wsStats.Cells(i, 20).value
                allIdx = allIdx + 1
            End If
        Next i

        ' Sort all classes by average score (bubble sort)
        Dim k As Integer
        For j = 1 To totalClassCount - 1
            For k = 1 To totalClassCount - j
                If allClassScores(k) < allClassScores(k + 1) Then
                    ' Swap scores
                    Dim tempScore As Double
                    tempScore = allClassScores(k)
                    allClassScores(k) = allClassScores(k + 1)
                    allClassScores(k + 1) = tempScore

                    ' Swap indices
                    Dim tempIndex As Integer
                    tempIndex = allClassIndices(k)
                    allClassIndices(k) = allClassIndices(k + 1)
                    allClassIndices(k + 1) = tempIndex
                End If
            Next k
        Next j

        ' Assign district-wide ranks with proper handling of ties
        Dim currentRank As Integer
        currentRank = 1

        ' Assign first rank
        wsStats.Cells(allClassIndices(1), 21).value = currentRank

        ' Assign remaining ranks with tie handling
        For j = 2 To totalClassCount
            If allClassScores(j) < allClassScores(j - 1) Then
                ' Different score, increment rank
                currentRank = j
            End If
            ' Assign the rank (same rank for tied scores)
            wsStats.Cells(allClassIndices(j), 21).value = currentRank
        Next j
    End If

    ' Add summary rows for each school
    Dim lastSchoolRow As Long
    Dim currentSchool As String
    Dim m As Long, n As Long ' Additional loop variables
    currentSchool = ""

    ' Sort the data by school to ensure school summary rows are placed at the end of each school's data
    For m = 5 To lastRowStats - 1
        ' Check if this is a new school and not a summary row
        If wsStats.Cells(m, 2).value <> currentSchool And Not wsStats.Cells(m, 3).MergeCells Then
            ' Get the pure school name (without any "汇总" text)
            currentSchool = wsStats.Cells(m, 2).value
            ' Remove any "汇总" text if present
            If InStr(currentSchool, "汇总") > 0 Then
                currentSchool = Left(currentSchool, InStr(currentSchool, "汇总") - 1)
            End If
            lastSchoolRow = m

            ' Find the last row for this school
            For n = m + 1 To lastRowStats - 1
                ' Only consider rows that are not summary rows and match the current school
                If Not wsStats.Cells(n, 3).MergeCells Then
                    Dim rowSchool As String
                    rowSchool = wsStats.Cells(n, 2).value
                    ' Remove any "汇总" text if present
                    If InStr(rowSchool, "汇总") > 0 Then
                        rowSchool = Left(rowSchool, InStr(rowSchool, "汇总") - 1)
                    End If

                    If rowSchool = currentSchool Then
                        lastSchoolRow = n
                    End If
                End If
            Next n

            ' Insert a summary row after the last row for this school
            wsStats.Rows(lastSchoolRow + 1).Insert

            ' Merge cells for school name (only B and C columns)
            wsStats.Range(wsStats.Cells(lastSchoolRow + 1, 2), wsStats.Cells(lastSchoolRow + 1, 3)).Merge
            ' Find the school ID for this school from the first class row
            Dim summarySchoolId As String
            summarySchoolId = ""
            For k = 5 To lastRowStats - 1
                If wsStats.Cells(k, 2).value = currentSchool Then
                    summarySchoolId = wsStats.Cells(k, 1).value
                    Exit For
                End If
            Next k
            wsStats.Cells(lastSchoolRow + 1, 1).value = summarySchoolId ' Keep school ID in column A
            wsStats.Cells(lastSchoolRow + 1, 2).value = currentSchool & "汇总" ' School name + summary text

            ' Calculate totals for this school
            Dim l As Long ' Loop variable for rows
            Dim totalWeightedScoreForSchool As Double
            totalWeightedScoreForSchool = 0

            For j = 4 To 19 ' Columns for counts (including actual and participating student counts)
                Dim schoolTotal As Long
                schoolTotal = 0

                For l = 5 To lastRowStats - 1
                    ' Only count rows that are not summary rows and match the current school
                    If Not wsStats.Cells(l, 3).MergeCells Then
                        Dim rowSchoolForTotal As String
                        rowSchoolForTotal = wsStats.Cells(l, 2).value
                        ' Remove any "汇总" text if present
                        If InStr(rowSchoolForTotal, "汇总") > 0 Then
                            rowSchoolForTotal = Left(rowSchoolForTotal, InStr(rowSchoolForTotal, "汇总") - 1)
                        End If

                        If rowSchoolForTotal = currentSchool Then
                            If Not IsEmpty(wsStats.Cells(l, j).value) Then
                                schoolTotal = schoolTotal + wsStats.Cells(l, j).value
                            End If
                        End If
                    End If
                Next l

                wsStats.Cells(lastSchoolRow + 1, j).value = schoolTotal
            Next j

            ' Calculate school average scores
            If wsStats.Cells(lastSchoolRow + 1, 5).value > 0 Then ' If there are participating students
                ' Calculate total average score from class total averages
                Dim totalStudentScores As Double, totalStudents As Long
                totalStudentScores = 0
                totalStudents = wsStats.Cells(lastSchoolRow + 1, 5).value

                For l = 5 To lastRowStats - 1
                    ' Only count rows that are not summary rows and match the current school
                    If Not wsStats.Cells(l, 3).MergeCells Then
                        rowSchoolForTotal = wsStats.Cells(l, 2).value
                        ' Remove any "汇总" text if present
                        If InStr(rowSchoolForTotal, "汇总") > 0 Then
                            rowSchoolForTotal = Left(rowSchoolForTotal, InStr(rowSchoolForTotal, "汇总") - 1)
                        End If

                        If rowSchoolForTotal = currentSchool And Not IsEmpty(wsStats.Cells(l, 20).value) Then
                            Dim classStudentsTotal As Long
                            classStudentsTotal = wsStats.Cells(l, 5).value
                            If classStudentsTotal > 0 Then
                                totalStudentScores = totalStudentScores + (wsStats.Cells(l, 20).value * classStudentsTotal)
                            End If
                        End If
                    End If
                Next l

                ' Total average score
                wsStats.Cells(lastSchoolRow + 1, 20).value = Round(totalStudentScores / totalStudents, 2)


            Else
                wsStats.Cells(lastSchoolRow + 1, 20).value = 0
            End If

            ' Format the summary row
            With wsStats.Rows(lastSchoolRow + 1)
                .Font.Bold = True
                .Font.Color = RGB(255, 0, 0) ' Red text
                ' Only apply formatting to data cells (columns A-U)
                With .Cells(1, 1).Resize(1, 21)
                    .Interior.Color = RGB(255, 255, 0) ' Yellow background
                    .Borders.LineStyle = xlContinuous ' Black border
                    .Borders.Color = RGB(0, 0, 0) ' Black color
                    .HorizontalAlignment = xlCenter ' Center text
                End With
            End With

            lastRowStats = lastRowStats + 1
        End If
    Next m

    ' Rank schools by average score
    Dim schoolSummaryRows() As Long
    Dim schoolAvgScores() As Double
    schoolCount = 0

    ' Count school summary rows
    For i = 5 To lastRowStats - 1
        If wsStats.Cells(i, 3).MergeCells Then
            schoolCount = schoolCount + 1
        End If
    Next i

    If schoolCount > 0 Then
        ReDim schoolSummaryRows(1 To schoolCount)
        ReDim schoolAvgScores(1 To schoolCount)

        ' Collect school summary rows and average scores
        Dim schoolIdx As Integer
        schoolIdx = 1
        For i = 5 To lastRowStats - 1
            If wsStats.Cells(i, 3).MergeCells Then
                schoolSummaryRows(schoolIdx) = i
                schoolAvgScores(schoolIdx) = wsStats.Cells(i, 20).value
                schoolIdx = schoolIdx + 1
            End If
        Next i

        ' Sort schools by average score (bubble sort)
        For i = 1 To schoolCount - 1
            For j = 1 To schoolCount - i
                If schoolAvgScores(j) < schoolAvgScores(j + 1) Then
                    ' Swap scores
                    tempScore = schoolAvgScores(j)
                    schoolAvgScores(j) = schoolAvgScores(j + 1)
                    schoolAvgScores(j + 1) = tempScore

                    ' Swap row indices
                    Dim tempRow As Long
                    tempRow = schoolSummaryRows(j)
                    schoolSummaryRows(j) = schoolSummaryRows(j + 1)
                    schoolSummaryRows(j + 1) = tempRow
                End If
            Next j
        Next i

        ' Assign ranks to schools
        For i = 1 To schoolCount
            wsStats.Cells(schoolSummaryRows(i), 21).value = i
        Next i
    End If

    ' Add district summary row
    wsStats.Rows(lastRowStats).Insert

    ' Merge cells for district summary (only B and C columns)
    wsStats.Range(wsStats.Cells(lastRowStats, 2), wsStats.Cells(lastRowStats, 3)).Merge
    wsStats.Cells(lastRowStats, 1).value = "" ' Leave school ID empty for district summary
    wsStats.Cells(lastRowStats, 2).value = "全区汇总"

    ' Calculate district totals and average score
    For j = 4 To 19 ' Columns for counts (including actual and participating student counts)
        Dim districtTotal As Long
        districtTotal = 0

        For i = 5 To lastRowStats - 1
            ' Only count rows that are not summary rows (don't have merged cells in B-C)
            If Not IsEmpty(wsStats.Cells(i, j).value) And Not wsStats.Cells(i, 3).MergeCells Then
                districtTotal = districtTotal + wsStats.Cells(i, j).value
            End If
        Next i

        wsStats.Cells(lastRowStats, j).value = districtTotal
    Next j

    ' Calculate district average scores
    If wsStats.Cells(lastRowStats, 5).value > 0 Then ' If there are participating students
        ' Calculate total average score from class total averages
        Dim totalDistrictScore As Double, totalDistrictStudents As Long
        totalDistrictScore = 0
        totalDistrictStudents = wsStats.Cells(lastRowStats, 5).value

        For i = 5 To lastRowStats - 1
            ' Only count rows that are not summary rows and not school summary rows
            If Not wsStats.Cells(i, 3).MergeCells Then
                If Not IsEmpty(wsStats.Cells(i, 20).value) Then
                    Dim classStudentsForDistrict As Long
                    classStudentsForDistrict = wsStats.Cells(i, 5).value
                    If classStudentsForDistrict > 0 Then
                        totalDistrictScore = totalDistrictScore + (wsStats.Cells(i, 20).value * classStudentsForDistrict)
                    End If
                End If
            End If
        Next i

        ' Total average score
        wsStats.Cells(lastRowStats, 20).value = Round(totalDistrictScore / totalDistrictStudents, 2)


    Else
        wsStats.Cells(lastRowStats, 20).value = 0
    End If

    ' Format the district summary row
    With wsStats.Rows(lastRowStats)
        .Font.Bold = True
        .Font.Color = RGB(255, 0, 0) ' Red text
        ' Only apply formatting to data cells (columns A-U)
        With .Cells(1, 1).Resize(1, 21)
            .Interior.Color = RGB(255, 255, 0) ' Yellow background
            .Borders.LineStyle = xlContinuous ' Black border
            .Borders.Color = RGB(0, 0, 0) ' Black color
            .HorizontalAlignment = xlCenter ' Center text
        End With
    End With
End Sub
