Sub 校级成绩汇总统计()
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    
    Dim ws As Worksheet, resultWs As Worksheet
    Dim lastRow As <PERSON>, lastCol As Long
    Dim i As <PERSON>, j <PERSON>, k <PERSON>
    Dim dataArr As Variant, resultArr As Variant
    Dim schoolDict As Object, gradeDict As Object, campusDict As Object
    Dim schoolArr() As String, gradeArr() As String
    Dim schoolCount As <PERSON>, gradeCount As Long
    Dim totalScoreCol As <PERSON>, chineseCol <PERSON>, mathCol <PERSON>, englishCol <PERSON>, scienceCol <PERSON>, moralCol As Long
    Dim gradeCol As Long, schoolCol As Long, nameCol <PERSON>, campusCol As Long
    Dim resultRow As Long
    
    ' 创建字典对象用于收集唯一值
    Set schoolDict = CreateObject("Scripting.Dictionary")
    Set gradeDict = CreateObject("Scripting.Dictionary")
    Set campusDict = CreateObject("Scripting.Dictionary")
    
    ' 设置工作表
    Set ws = ThisWorkbook.Sheets("成绩汇总表")
    Set resultWs = Sheets("校点统计") ' 修改为新的目标表
    
    ' 删除第5行以后的所有行
    If resultWs.Cells(resultWs.Rows.Count, 1).End(xlUp).Row > 4 Then
        resultWs.Rows("5:" & resultWs.Rows.Count).Delete
    End If
    
    ' 找到最后一行和最后一列
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    lastCol = ws.Cells(1, ws.Columns.Count).End(xlToLeft).Column
    
    ' 一次性读取所有数据到数组
    dataArr = ws.Range(ws.Cells(1, 1), ws.Cells(lastRow, lastCol)).Value
    
    ' 找到关键列的位置
    For i = 1 To lastCol
        Select Case dataArr(1, i)
            Case "年级": gradeCol = i
            Case "学校": schoolCol = i
            Case "姓名": nameCol = i
            Case "校点", "校区": campusCol = i ' 添加校点列识别
            Case "语文": chineseCol = i
            Case "数学": mathCol = i
            Case "英语": englishCol = i
            Case "科学": scienceCol = i
            Case "道德与法治": moralCol = i
            Case "总分": totalScoreCol = i
        End Select
    Next i
    
    ' 收集唯一的学校、年级和校点
    For i = 2 To UBound(dataArr, 1)
        If Len(Trim(CStr(dataArr(i, schoolCol)))) > 0 Then
            schoolDict(dataArr(i, schoolCol)) = 1
        End If
        
        If Len(Trim(CStr(dataArr(i, gradeCol)))) > 0 Then
            gradeDict(dataArr(i, gradeCol)) = 1
        End If
        
        ' 收集校点信息
        If campusCol > 0 And Len(Trim(CStr(dataArr(i, campusCol)))) > 0 Then
            campusDict(dataArr(i, campusCol)) = 1
        End If
    Next i
    
    ' 转换字典键为数组
    schoolCount = schoolDict.Count
    gradeCount = gradeDict.Count
    
    ReDim schoolArr(1 To schoolCount)
    ReDim gradeArr(1 To gradeCount)
    
    i = 1
    For Each school In schoolDict.Keys
        schoolArr(i) = school
        i = i + 1
    Next
    
    i = 1
    For Each grade In gradeDict.Keys
        gradeArr(i) = grade
        i = i + 1
    Next
    
    ' 排序年级数组
    Call QuickSortArray(gradeArr, 1, gradeCount)
    
    ' 预分配结果数组 - 增加一列用于校点
    Dim maxResultRows As Long
    maxResultRows = gradeCount * (schoolCount + 2) ' 每个年级的学校数+全区+空行
    ReDim resultArr(1 To maxResultRows, 1 To 25) ' 增加一列用于校点
    resultRow = 1
    
    ' 对每个年级进行统计
    For i = 1 To gradeCount
        Dim currentGrade As String
        currentGrade = gradeArr(i)
        
        ' 创建统计数组
        Dim stats() As Double
        ReDim stats(1 To schoolCount + 1, 1 To 20)
        
        ' 初始化统计数组
        For j = 1 To schoolCount + 1
            stats(j, 5) = 9999 ' 最低分初始化为很大的值
        Next j
        
        ' 一次性统计所有数据
        For j = 2 To UBound(dataArr, 1)
            If dataArr(j, gradeCol) = currentGrade Then
                ' 找到当前学校的索引
                Dim schoolIndex As Long
                schoolIndex = 0
                
                For k = 1 To schoolCount
                    If dataArr(j, schoolCol) = schoolArr(k) Then
                        schoolIndex = k
                        Exit For
                    End If
                Next k
                
                If schoolIndex > 0 Then
                    ' 处理总分
                    If Len(Trim(CStr(dataArr(j, totalScoreCol)))) > 0 And IsNumeric(dataArr(j, totalScoreCol)) Then
                        Dim score As Double
                        score = CDbl(dataArr(j, totalScoreCol))
                        
                        ' 学校统计
                        stats(schoolIndex, 1) = stats(schoolIndex, 1) + 1 ' 总人数
                        stats(schoolIndex, 2) = stats(schoolIndex, 2) + score ' 总分
                        
                        ' 全区统计
                        stats(schoolCount + 1, 1) = stats(schoolCount + 1, 1) + 1
                        stats(schoolCount + 1, 2) = stats(schoolCount + 1, 2) + score
                        
                        ' 优秀、合格、低分统计
                        If score >= 425 Then
                            stats(schoolIndex, 6) = stats(schoolIndex, 6) + 1 ' 优秀人数
                            stats(schoolCount + 1, 6) = stats(schoolCount + 1, 6) + 1
                        End If
                        
                        If score >= 300 Then
                            stats(schoolIndex, 7) = stats(schoolIndex, 7) + 1 ' 合格人数
                            stats(schoolCount + 1, 7) = stats(schoolCount + 1, 7<codegeex-cursor></codegeex-cursor>
