'------------------------------------------------------------------------------
' 模块：班级成绩报表
' 功能：生成按年级统计的班级成绩报表
' 说明：从成绩总表中提取指定年级的成绩数据，从任课教师表中获取任课教师信息，
'      并自动计算各班级的各科目平均分和名次，填充到班级成绩报表中。
'------------------------------------------------------------------------------

'------------------------------------------------------------------------------
' 主过程：生成班级成绩报表
'------------------------------------------------------------------------------
Sub 生成班级成绩报表()
    ' 工作表变量
    Dim ws成绩总表 As Worksheet, ws任课教师 As Worksheet, ws班级成绩报表 As Worksheet
    
    ' 基本参数
    Dim 年级 As String
    Dim 当前行 As Long
    Dim 科目列表() As String
    Dim 科目数量 As Integer
    Dim 科目列号() As Integer
    
    ' 数据存储
    Dim 成绩数据() As Variant  ' 用于存储成绩总表数据
    Dim 任课教师数据() As Variant  ' 用于存储任课教师表数据
    Dim 学校班级字典 As Object  ' 存储学校和班级信息
    
    ' 排名相关变量
    Dim 班级得分数组() As Variant  ' 用于存储班级得分和行号，以便排序
    Dim 班级数量 As Long  ' 用于记录班级总数
    Dim 班级计数() As Long  ' 用于记录每个科目的班级计数
    
    ' 循环变量
    Dim i As Long, j As Long, k As Long, m As Long
    Dim 学校Key As Variant, 班级Key As Variant
    Dim 学校信息 As Variant
    
    ' 数据变量
    Dim 当前学校序号 As String, 当前学校 As String, 当前校点 As String, 当前班级 As String
    Dim 当前学校序号值 As String, 当前学校值 As String, 当前校点值 As String
    Dim 学校键值 As String
    Dim 任课教师 As String
    
    ' 统计变量
    Dim 成绩 As Double
    Dim 实考人数 As Long
    Dim 总分() As Double
    Dim 平均分() As Double
    
    ' 使用结构化错误处理
    On Error GoTo ErrorHandler
    
    ' 获取工作表
    On Error Resume Next
    Set ws成绩总表 = ThisWorkbook.Worksheets("成绩总表")
    If ws成绩总表 Is Nothing Then
        MsgBox "未找到'成绩总表'工作表，请确保工作簿中包含此工作表！", vbCritical
        Exit Sub
    End If
    On Error GoTo ErrorHandler
    
    ' 检查任课教师表是否存在
    Dim 任课教师表存在 As Boolean
    任课教师表存在 = False
    For Each ws In ThisWorkbook.Worksheets
        If ws.Name = "任课教师" Then
            任课教师表存在 = True
            Set ws任课教师 = ws
            Exit For
        End If
    Next ws
    
    ' 如果任课教师表不存在，提示用户
    If Not 任课教师表存在 Then
        MsgBox "未找到'任课教师'工作表，将无法获取任课教师信息。", vbExclamation
    End If
    
    ' 检查班级成绩报表表是否存在，如果不存在则创建
    Dim 班级成绩报表存在 As Boolean
    班级成绩报表存在 = False
    For Each ws In ThisWorkbook.Worksheets
        If ws.Name = "班级成绩报表" Then
            班级成绩报表存在 = True
            Set ws班级成绩报表 = ws
            Exit For
        End If
    Next ws
    
    ' 如果班级成绩报表不存在，则创建
    If Not 班级成绩报表存在 Then
        Set ws班级成绩报表 = ThisWorkbook.Worksheets.Add(After:=ThisWorkbook.Worksheets(ThisWorkbook.Worksheets.Count))
        ws班级成绩报表.Name = "班级成绩报表"
    End If
    
    ' 应用屏幕更新关闭，提高性能
    Application.ScreenUpdating = False
    
    ' 获取年级
    年级 = InputBox("请输入要统计的年级:", "年级选择", "七年级")
    
    ' 检查年级是否有效
    If 年级 = "" Then
        MsgBox "未输入年级，操作取消!", vbExclamation
        GoTo CleanExit
    End If
    
    ' 将数据加载到数组中，减少工作表访问，提高性能
    On Error Resume Next
    成绩数据 = ws成绩总表.UsedRange.Value
    If Err.Number <> 0 Then
        MsgBox "读取成绩总表数据时出错: " & Err.Description, vbCritical
        GoTo CleanExit
    End If
    
    If 任课教师表存在 Then
        任课教师数据 = ws任课教师.UsedRange.Value
        If Err.Number <> 0 Then
            MsgBox "读取任课教师表数据时出错: " & Err.Description, vbExclamation
            任课教师表存在 = False
        End If
    End If
    On Error GoTo ErrorHandler
    
    ' 检查成绩数据是否为空或格式不正确
    If Not IsArray(成绩数据) Then
        MsgBox "成绩总表数据格式不正确或为空!", vbCritical
        GoTo CleanExit
    End If
    
    ' 检查成绩数据的维度
    Dim 成绩数据行数 As Long, 成绩数据列数 As Long
    On Error Resume Next
    成绩数据行数 = UBound(成绩数据, 1)
    成绩数据列数 = UBound(成绩数据, 2)
    On Error GoTo ErrorHandler
    
    If 成绩数据行数 < 1 Or 成绩数据列数 < 5 Then
        MsgBox "成绩总表数据不完整，至少需要包含表头和基本列（年级、学校序号、学校、校点、班级）!", vbCritical
        GoTo CleanExit
    End If
    
    ' 获取科目列表
    Dim 科目列表集合 As New Collection
    On Error Resume Next ' 忽略重复项错误
    For i = 1 To 成绩数据列数
        If 成绩数据(1, i) <> "" And _
           成绩数据(1, i) <> "年级" And _
           成绩数据(1, i) <> "学校序号" And _
           成绩数据(1, i) <> "学校" And _
           成绩数据(1, i) <> "校点" And _
           成绩数据(1, i) <> "班级" And _
           成绩数据(1, i) <> "姓名" And _
           成绩数据(1, i) <> "学号" And _
           成绩数据(1, i) <> "性别" And _
           成绩数据(1, i) <> "备注" Then
            科目列表集合.Add 成绩数据(1, i), CStr(成绩数据(1, i))
        End If
    Next i
    On Error GoTo ErrorHandler ' 恢复错误处理
    
    ' 检查是否找到科目
    科目数量 = 科目列表集合.Count
    If 科目数量 = 0 Then
        MsgBox "在成绩总表中未找到任何科目列!", vbCritical
        GoTo CleanExit
    End If
    
    ' 将科目列表集合转换为数组
    ReDim 科目列表(1 To 科目数量)
    ReDim 科目列号(1 To 科目数量)
    
    For i = 1 To 科目数量
        科目列表(i) = 科目列表集合(i)
    Next i
    
    ' 查找科目在成绩总表中的列号
    For i = 1 To 科目数量
        科目列号(i) = 查找列号(成绩数据, 科目列表(i))
        
        ' 如果找不到科目，则退出
        If 科目列号(i) = 0 Then
            MsgBox "在成绩总表中找不到科目 """ & 科目列表(i) & """!", vbExclamation
            GoTo CleanExit
        End If
    Next i
    
    ' 统计学校和班级数量
    Set 学校班级字典 = CreateObject("Scripting.Dictionary")
    
    ' 开始收集学校和班级信息
    ' 遍历成绩总表数据，收集所有符合年级条件的学校和班级
    For i = 2 To 成绩数据行数
        ' 检查年级是否匹配
        If CStr(成绩数据(i, 1)) = 年级 Then
            ' 安全地获取数据，避免空值或错误
            On Error Resume Next
            当前学校序号 = CStr(成绩数据(i, 2))
            当前学校 = CStr(成绩数据(i, 3))
            当前校点 = CStr(成绩数据(i, 4))
            当前班级 = CStr(成绩数据(i, 5))
            On Error GoTo ErrorHandler
            
            ' 确保所有必要的数据都存在
            If 当前学校 <> "" And 当前班级 <> "" Then
                ' 将学校和班级添加到字典中
                学校键值 = 当前学校序号 & "|" & 当前学校 & "|" & 当前校点
                
                If Not 学校班级字典.Exists(学校键值) Then
                    学校班级字典.Add 学校键值, CreateObject("Scripting.Dictionary")
                End If
                
                If Not 学校班级字典(学校键值).Exists(当前班级) Then
                    学校班级字典(学校键值).Add 当前班级, True
                End If
            End If
        End If
    Next i
    
    ' 检查是否找到符合条件的班级
    班级数量 = 0
    For Each 学校Key In 学校班级字典.Keys
        For Each 班级Key In 学校班级字典(学校Key).Keys
            班级数量 = 班级数量 + 1
        Next 班级Key
    Next 学校Key
    
    If 班级数量 = 0 Then
        MsgBox "未找到年级为 """ & 年级 & """ 的班级数据!", vbExclamation
        GoTo CleanExit
    End If
    
    ' 清空班级成绩报表表
    ws班级成绩报表.Cells.Clear
    
    ' 创建表头
    ws班级成绩报表.Cells(1, 1).Value = "年级：" & 年级
    ws班级成绩报表.Cells(1, 1).Font.Bold = True
    ws班级成绩报表.Cells(1, 1).Font.Size = 14
    
    ' 添加表头
    ws班级成绩报表.Cells(3, 1).Value = "序号"
    ws班级成绩报表.Cells(3, 2).Value = "学校"
    ws班级成绩报表.Cells(3, 3).Value = "校点"
    ws班级成绩报表.Cells(3, 4).Value = "班级"
    ws班级成绩报表.Cells(3, 5).Value = "实考人数"
    
    ' 添加科目表头
    For i = 1 To 科目数量
        ws班级成绩报表.Cells(3, 5 + i).Value = 科目列表(i)
        ws班级成绩报表.Cells(3, 5 + 科目数量 + i).Value = 科目列表(i) & "名次"
        ws班级成绩报表.Cells(3, 5 + 科目数量 * 2 + i).Value = 科目列表(i) & "任课教师"
    Next i
    
    ' 设置表头格式
    With ws班级成绩报表.Range(ws班级成绩报表.Cells(3, 1), ws班级成绩报表.Cells(3, 5 + 科目数量 * 3))
        .Font.Bold = True
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .Interior.Color = RGB(217, 217, 217) ' 浅灰色背景
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlThin
        .Borders.ColorIndex = xlAutomatic
    End With
    
    ' 初始化班级得分数组（每个科目一个）
    ReDim 班级得分数组(1 To 科目数量, 1 To 班级数量, 1 To 3)  ' 维度：科目、班级、属性（1=平均分，2=行号，3=学校班级标识）
    
    ' 初始化班级计数数组（每个科目一个计数器）
    ReDim 班级计数(1 To 科目数量)
    For i = 1 To 科目数量
        班级计数(i) = 0
    Next i
    
    ' 当前行从第4行开始（表头在第3行）
    当前行 = 4
    Dim 序号 As Long
    序号 = 1
    
    ' 遍历每个学校和班级进行统计
    For Each 学校Key In 学校班级字典.Keys
        ' 安全地解析学校信息
        On Error Resume Next
        学校信息 = Split(学校Key, "|")
        If UBound(学校信息) >= 2 Then
            当前学校序号值 = 学校信息(0)
            当前学校值 = 学校信息(1)
            当前校点值 = 学校信息(2)
        Else
            当前学校序号值 = ""
            当前学校值 = 学校Key
            当前校点值 = ""
        End If
        On Error GoTo ErrorHandler
        
        ' 遍历该学校的每个班级
        For Each 班级Key In 学校班级字典(学校Key).Keys
            当前班级 = 班级Key
            
            ' 初始化统计数据
            实考人数 = 0
            ReDim 总分(1 To 科目数量)
            ReDim 平均分(1 To 科目数量)
            
            For j = 1 To 科目数量
                总分(j) = 0
                平均分(j) = 0
            Next j
            
            ' 统计实考人数和各科目总分
            For i = 2 To 成绩数据行数
                ' 安全地比较数据
                On Error Resume Next
                Dim 匹配年级 As Boolean, 匹配学校序号 As Boolean, 匹配学校 As Boolean, 匹配校点 As Boolean, 匹配班级 As Boolean
                
                匹配年级 = (CStr(成绩数据(i, 1)) = 年级)
                匹配学校序号 = (CStr(成绩数据(i, 2)) = 当前学校序号值)
                匹配学校 = (CStr(成绩数据(i, 3)) = 当前学校值)
                匹配校点 = (CStr(成绩数据(i, 4)) = 当前校点值)
                匹配班级 = (CStr(成绩数据(i, 5)) = 当前班级)
                
                If Err.Number <> 0 Then
                    Err.Clear
                    匹配年级 = False
                    匹配学校序号 = False
                    匹配学校 = False
                    匹配校点 = False
                    匹配班级 = False
                End If
                On Error GoTo ErrorHandler
                
                If 匹配年级 And 匹配学校序号 And 匹配学校 And 匹配校点 And 匹配班级 Then
                    ' 实考人数加1
                    实考人数 = 实考人数 + 1
                    
                    ' 统计各科目总分
                    For j = 1 To 科目数量
                        ' 安全地获取成绩
                        On Error Resume Next
                        If 科目列号(j) <= 成绩数据列数 Then
                            成绩 = Val(成绩数据(i, 科目列号(j)))
                            If 成绩 > 0 Then
                                总分(j) = 总分(j) + 成绩
                            End If
                        End If
                        On Error GoTo ErrorHandler
                    Next j
                End If
            Next i
            
            ' 计算各科目平均分
            For j = 1 To 科目数量
                If 实考人数 > 0 Then
                    平均分(j) = 总分(j) / 实考人数
                Else
                    平均分(j) = 0
                End If
            Next j
            
            ' 填充班级基本信息
            ws班级成绩报表.Cells(当前行, 1).Value = 序号
            ws班级成绩报表.Cells(当前行, 2).Value = 当前学校值
            ws班级成绩报表.Cells(当前行, 3).Value = 当前校点值
            ws班级成绩报表.Cells(当前行, 4).Value = 当前班级
            ws班级成绩报表.Cells(当前行, 5).Value = 实考人数
            
            ' 填充各科目平均分
            For j = 1 To 科目数量
                ws班级成绩报表.Cells(当前行, 5 + j).Value = 平均分(j)
                ws班级成绩报表.Cells(当前行, 5 + j).NumberFormat = "0.00"
            Next j
            
            ' 记录班级平均分和行号，以便后续排名
            For j = 1 To 科目数量
                ' 安全地增加班级计数
                班级计数(j) = 班级计数(j) + 1
                
                ' 确保不超出数组边界
                If 班级计数(j) <= 班级数量 Then
                    ' 存储平均分、行号和学校班级标识
                    班级得分数组(j, 班级计数(j), 1) = 平均分(j)  ' 平均分
                    班级得分数组(j, 班级计数(j), 2) = 当前行  ' 行号
                    班级得分数组(j, 班级计数(j), 3) = 当前学校值 & "-" & 当前班级  ' 学校班级标识
                End If
            Next j
            
            ' 获取任课教师信息
            If 任课教师表存在 Then
                ' 安全地获取任课教师数据的维度
                Dim 任课教师行数 As Long, 任课教师列数 As Long
                On Error Resume Next
                任课教师行数 = UBound(任课教师数据, 1)
                任课教师列数 = UBound(任课教师数据, 2)
                On Error GoTo ErrorHandler
                
                For j = 1 To 科目数量
                    任课教师 = ""
                    For k = 2 To 任课教师行数
                        ' 安全地比较数据
                        On Error Resume Next
                        Dim 匹配任课学校 As Boolean, 匹配任课班级 As Boolean, 匹配任课年级 As Boolean
                        
                        匹配任课学校 = (CStr(任课教师数据(k, 1)) = 当前学校值)
                        匹配任课班级 = (CStr(任课教师数据(k, 2)) = 当前班级)
                        匹配任课年级 = (CStr(任课教师数据(k, 3)) = 年级)
                        
                        If Err.Number <> 0 Then
                            Err.Clear
                            匹配任课学校 = False
                            匹配任课班级 = False
                            匹配任课年级 = False
                        End If
                        On Error GoTo ErrorHandler
                        
                        If 匹配任课学校 And 匹配任课班级 And 匹配任课年级 Then
                            ' 查找科目在任课教师表中的列号
                            For m = 4 To 任课教师列数
                                On Error Resume Next
                                If CStr(任课教师数据(1, m)) = 科目列表(j) Then
                                    任课教师 = CStr(任课教师数据(k, m))
                                    Exit For
                                End If
                                On Error GoTo ErrorHandler
                            Next m
                            Exit For
                        End If
                    Next k
                    
                    ' 填充任课教师
                    ws班级成绩报表.Cells(当前行, 5 + 科目数量 * 2 + j).Value = 任课教师
                Next j
            End If
            
            ' 移动到下一行
            当前行 = 当前行 + 1
            序号 = 序号 + 1
        Next 班级Key
    Next 学校Key
    
    ' 计算并填充各科目排名
    For j = 1 To 科目数量
        ' 确保班级计数不超过班级数量
        Dim 有效班级数 As Long
        有效班级数 = 班级计数(j)
        If 有效班级数 > 班级数量 Then 有效班级数 = 班级数量
        
        Call 计算科目排名(ws班级成绩报表, 班级得分数组, j, 有效班级数, 5 + 科目数量 + j)
    Next j
    
    ' 添加边框
    If 当前行 > 4 Then  ' 确保有数据行
        Dim 数据区域 As Range
        Set 数据区域 = ws班级成绩报表.Range(ws班级成绩报表.Cells(3, 1), ws班级成绩报表.Cells(当前行 - 1, 5 + 科目数量 * 3))
        With 数据区域.Borders
            .LineStyle = xlContinuous
            .Weight = xlThin
            .ColorIndex = xlAutomatic
        End With
    End If
    
    ' 自动调整列宽
    ws班级成绩报表.UsedRange.Columns.AutoFit
    
    ' 冻结窗格（前5列和前3行）
    ws班级成绩报表.Activate
    ActiveWindow.FreezePanes = False
    If 当前行 > 4 Then  ' 确保有数据行
        ws班级成绩报表.Cells(4, 6).Select
        ActiveWindow.FreezePanes = True
    End If
    
    ' 提示完成
    MsgBox "班级成绩报表生成完成！共统计了 " & 当前行 - 4 & " 个班级。", vbInformation
    
    ' 清除错误处理
    On Error GoTo 0
    
    ' 正常退出点
CleanExit:
    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    Exit Sub
    
    ' 错误处理
ErrorHandler:
    MsgBox "程序运行时发生错误：" & vbCrLf & _
           "错误代码：" & Err.Number & vbCrLf & _
           "错误描述：" & Err.Description & vbCrLf & _
           "错误来源：" & Err.Source, vbCritical
    Resume CleanExit
End Sub

'------------------------------------------------------------------------------
' 辅助函数：查找列号
' 功能：在数组的第一行中查找指定值的列号
' 参数：
'   - 数据数组：要搜索的二维数组
'   - 查找值：要查找的值
' 返回：如果找到返回列号，否则返回0
'------------------------------------------------------------------------------
Function 查找列号(数据数组 As Variant, 查找值 As String) As Long
    Dim i As Long
    查找列号 = 0
    
    ' 添加数组边界检查
    If Not IsArray(数据数组) Then Exit Function
    
    ' 安全地获取数组维度
    Dim 行数 As Long, 列数 As Long
    On Error Resume Next
    行数 = UBound(数据数组, 1)
    列数 = UBound(数据数组, 2)
    On Error GoTo 0
    
    If 行数 < 1 Then Exit Function
    
    For i = 1 To 列数
        On Error Resume Next
        If CStr(数据数组(1, i)) = 查找值 Then
            查找列号 = i
            Exit Function
        End If
        On Error GoTo 0
    Next i
End Function

'------------------------------------------------------------------------------
' 辅助函数：获取数值
' 功能：将变量转换为数值，处理非数值情况
' 参数：
'   - value：要转换的值
' 返回：数值结果，如果非数值则返回0
'------------------------------------------------------------------------------
Function Val(value As Variant) As Double
    On Error Resume Next
    If IsNumeric(value) Then
        Val = CDbl(value)
    Else
        Val = 0
    End If
    On Error GoTo 0
End Function

'------------------------------------------------------------------------------
' 辅助函数：计算科目排名
' 功能：根据班级平均分计算排名并填充到班级成绩报表中
' 参数：
'   - ws班级成绩报表：班级成绩报表工作表
'   - 班级得分数组：存储班级平均分和行号的数组
'   - 科目索引：当前处理的科目索引
'   - 班级数量：班级总数
'   - 排名列号：排名要填充的列号
'------------------------------------------------------------------------------
Sub 计算科目排名(ws班级成绩报表 As Worksheet, 班级得分数组 As Variant, 科目索引 As Long, 班级数量 As Long, 排名列号 As Long)
    Dim i As Long, j As Long
    Dim 临时得分 As Double, 临时行号 As Long, 临时标识 As String
    Dim 排名数组() As Long
    
    ' 如果班级数量小于1，则无需排名
    If 班级数量 < 1 Then Exit Sub
    
    ' 初始化排名数组
    ReDim 排名数组(1 To 班级数量)
    
    ' 对班级得分数组按平均分降序排序（冒泡排序）
    On Error Resume Next ' 添加错误处理，防止数组访问越界
    For i = 1 To 班级数量 - 1
        For j = i + 1 To 班级数量
            If 班级得分数组(科目索引, i, 1) < 班级得分数组(科目索引, j, 1) Then
                ' 交换平均分
                临时得分 = 班级得分数组(科目索引, i, 1)
                班级得分数组(科目索引, i, 1) = 班级得分数组(科目索引, j, 1)
                班级得分数组(科目索引, j, 1) = 临时得分
                
                ' 交换行号
                临时行号 = 班级得分数组(科目索引, i, 2)
                班级得分数组(科目索引, i, 2) = 班级得分数组(科目索引, j, 2)
                