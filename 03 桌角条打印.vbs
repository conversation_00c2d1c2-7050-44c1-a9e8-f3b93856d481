Sub 桌角条打印()
    Dim ws As Worksheet
    Dim wsOutput As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim currentRow As Long
    Dim currentCol As Long
    Dim examRoom As Integer
    Dim seatNumber As Integer
    Dim schoolPoint As String
    Dim gradeLevel As String ' 添加年级变量
    Dim prevExamRoom As Integer
    Dim prevSeatNumber As Integer
    Dim prevSchoolPoint As String
    Dim prevGradeLevel As String ' 添加上一个年级变量
    Dim studentInfo As String
    Dim combinedInfo As String ' 用于存储合并后的信息
    Dim isFirstRecord As Boolean ' 用于标记是否是第一条记录
    Dim lastExamRoomOfPrevSchoolPoint As Integer ' 记录上一个学校的最后一个考场

    ' 检查是否存在名为“桌角条打印”的工作表，如果存在则删除
    For Each ws In ThisWorkbook.Sheets
        If ws.Name = "桌角条打印" Then
            Application.DisplayAlerts = False ' 关闭警告提示
            ws.Delete
            Application.DisplayAlerts = True ' 打开警告提示
            Exit For
        End If
    Next ws

    ' 设置源工作表和输出工作表
    Set ws = ThisWorkbook.Sheets("学生考场编排表")
    Set wsOutput = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.count))
    wsOutput.Name = "桌角条打印"

    ' 获取源工作表的最后一行
    lastRow = ws.Cells(ws.Rows.count, 1).End(xlUp).Row

    ' 按学校、年级、考场号和座位号排序（考场号作为最后排序条件确保考场大小顺序）
    ws.Sort.SortFields.Clear
    ws.Sort.SortFields.Add Key:=ws.Range("F2:F" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ws.Sort.SortFields.Add Key:=ws.Range("G2:G" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ws.Sort.SortFields.Add Key:=ws.Range("B2:B" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal

    With ws.Sort
        .SetRange ws.Range("A1:G" & lastRow)
        .Header = xlYes
        .MatchCase = False
        .Orientation = xlTopToBottom
        .SortMethod = xlPinYin
        .Apply
    End With

    ' 初始化变量
    currentRow = 1
    currentCol = 1
    prevExamRoom = 0
    prevSeatNumber = 0
    prevSchoolPoint = ""
    prevGradeLevel = "" ' 初始化上一个年级
    studentInfo = ""
    isFirstRecord = True ' 标记为第一条记录
    lastExamRoomOfPrevSchoolPoint = 0

    ' 遍历源工作表中的每一行
    For i = 2 To lastRow
        schoolPoint = ws.Cells(i, 1).Value
        gradeLevel = ws.Cells(i, 2).Value ' 获取年级
        examRoom = ws.Cells(i, 6).Value
        seatNumber = ws.Cells(i, 7).Value

        ' 检查是否是新学校的第一个考场
        If schoolPoint <> prevSchoolPoint And examRoom = 1 Then
            ' 写入上一学校的最后一组信息
            If Not isFirstRecord Then
                ' 合并考场和学生信息
                combinedInfo = "第" & prevExamRoom & "考场 第" & prevSeatNumber & "桌" & vbCrLf & studentInfo
                ' 去除 combinedInfo 末尾的空白字符
                combinedInfo = Trim(combinedInfo)

                ' 写入合并后的信息
                wsOutput.Cells(currentRow, currentCol).Value = combinedInfo

                With wsOutput.Cells(currentRow, currentCol).Font
                    .Name = "黑体"
                    .Size = 18
                End With
                ' 设置单元格内容左对齐
                wsOutput.Cells(currentRow, currentCol).HorizontalAlignment = xlCenter

                ' 设置组信息的虚线边框
                With wsOutput.Cells(currentRow, currentCol).Resize(1, 1).Borders
                    ' 修改为双线样式
                    .LineStyle = xlDouble
                    .ColorIndex = 0 ' 边框颜色
                    .TintAndShade = 0
                    .Weight = xlThin ' 边框粗细
                End With

                ' 调整单元格格式
                wsOutput.Cells(currentRow, currentCol).RowHeight = 65
                wsOutput.Cells(currentRow, currentCol).ColumnWidth = 28

                ' 设置间隔列列宽为2个字符大小
                wsOutput.Columns("B").ColumnWidth = 2
                wsOutput.Columns("D").ColumnWidth = 2

                ' 按考场分开打印
                ' 移动到下一个单元格
                currentCol = currentCol + 2
                If currentCol > 5 Then
                    currentCol = 1
                    currentRow = currentRow + 2
                End If

                ' 重置学生信息
                studentInfo = ""
            End If

            wsOutput.Cells(currentRow, 1).Resize(1, 1).Merge
            wsOutput.Cells(currentRow, 1).Value = "学校：" & schoolPoint
            With wsOutput.Cells(currentRow, 1).Font
                .Name = "黑体"
                .Size = 18 ' 四号字大约为12磅
            End With
            wsOutput.Cells(currentRow, 1).HorizontalAlignment = xlCenter
            currentRow = currentRow
            prevSchoolPoint = schoolPoint
            ' 重置上一组信息
            prevExamRoom = 0
            prevSeatNumber = 0
            prevGradeLevel = "" ' 重置上一个年级
            isFirstRecord = True ' 标记为第一条记录
        End If

        If examRoom <> prevExamRoom Or seatNumber <> prevSeatNumber Then
            ' 如果不是第一条记录，写入上一组信息
            If Not isFirstRecord Then
                ' 合并考场和学生信息
                combinedInfo = "第" & prevExamRoom & "考场 第" & prevSeatNumber & "桌" & vbCrLf & studentInfo
                ' 去除 combinedInfo 末尾的空白字符
                combinedInfo = Trim(combinedInfo)
                ' 写入合并后的信息
                wsOutput.Cells(currentRow, currentCol).Value = combinedInfo


                With wsOutput.Cells(currentRow, currentCol).Font
                    .Name = "黑体"
                    .Size = 14
                End With
                ' 设置单元格内容左对齐
                wsOutput.Cells(currentRow, currentCol).HorizontalAlignment = xlCenter

                ' 设置组信息的虚线边框
                With wsOutput.Cells(currentRow, currentCol).Resize(1, 1).Borders
                    ' 修改为双线样式
                    .LineStyle = xlDouble
                    .ColorIndex = 0 ' 边框颜色
                    .TintAndShade = 0
                    .Weight = xlThin ' 边框粗细
                End With

                ' 调整单元格格式
                wsOutput.Cells(currentRow, currentCol).RowHeight = 60
                wsOutput.Cells(currentRow, currentCol).ColumnWidth = 28

                ' 移动到下一个单元格
                currentCol = currentCol + 2
                If currentCol > 5 Then
                    currentCol = 1
                    currentRow = currentRow + 2
                End If

                ' 重置学生信息
                studentInfo = ""
            End If

            ' 考场变化时强制分页：每个考场独立在一页上
            If Not isFirstRecord And examRoom <> prevExamRoom Then
                ' 在新考场开始前插入分页符
                wsOutput.HPageBreaks.Add Before:=wsOutput.Cells(currentRow, 1)
                currentRow = currentRow + 1
                currentCol = 1
            ElseIf seatNumber = 1 And prevSeatNumber > 0 Then
                ' 新增逻辑：如果当前座位号为 1 且不是考场的第一个座位，换一行从第一列开始
                ' 只有在同一考场内才执行这个逻辑
                currentCol = 1
                currentRow = currentRow + 2
            End If

            ' 更新上一组信息
            prevExamRoom = examRoom
            prevSeatNumber = seatNumber
            prevGradeLevel = gradeLevel ' 更新上一个年级
            isFirstRecord = False ' 标记不再是第一条记录
        End If

        ' 合并学生信息
        studentInfo = studentInfo & ws.Cells(i, 2).Value & "年级 " & ws.Cells(i, 3).Value & "班" & vbCrLf & ws.Cells(i, 4).Value & " " & ws.Cells(i, 5).Value & vbCrLf
    Next i

    ' 写入最后一组信息
    ' 合并考场和学生信息
    combinedInfo = "第" & prevExamRoom & "考场 第" & prevSeatNumber & "桌" & vbCrLf & studentInfo

    ' 去除 combinedInfo 末尾的空白字符
    combinedInfo = Trim(combinedInfo)
    ' 写入合并后的信息
    wsOutput.Cells(currentRow, currentCol).Value = combinedInfo


    With wsOutput.Cells(currentRow, currentCol).Font
        .Name = "黑体"
        .Size = 14
    End With
    ' 设置单元格内容居中
    wsOutput.Cells(currentRow, currentCol).HorizontalAlignment = xlCenter

    ' 设置组信息的虚线边框
    With wsOutput.Cells(currentRow, currentCol).Resize(1, 1).Borders
        ' 修改为双线样式
        .LineStyle = xlDouble
        .ColorIndex = 0 ' 边框颜色
        .TintAndShade = 0
        .Weight = xlThin ' 边框粗细
    End With

    ' 调整单元格格式
    wsOutput.Cells(currentRow, currentCol).RowHeight = 65
    wsOutput.Cells(currentRow, currentCol).ColumnWidth = 28

    ' 设置整个工作表的打印区域
    wsOutput.PageSetup.PrintArea = wsOutput.UsedRange.Address

    ' 打印设置
    wsOutput.PageSetup.Orientation = xlPortrait
    wsOutput.PageSetup.PaperSize = xlPaperA4

    ' 调整页边距，将厘米转换为磅（1 厘米约等于 28.35 磅）
    wsOutput.PageSetup.TopMargin = Application.CentimetersToPoints(1) ' 减小上边距
    wsOutput.PageSetup.BottomMargin = Application.CentimetersToPoints(1) ' 减小下边距
    wsOutput.PageSetup.LeftMargin = Application.CentimetersToPoints(0.5) ' 减小左边距
    wsOutput.PageSetup.RightMargin = Application.CentimetersToPoints(0.5) ' 减小右边距

    ' 调整缩放比例，使内容刚好在页面内
    wsOutput.PageSetup.FitToPagesWide = 1 ' 调整为一页宽
    wsOutput.PageSetup.FitToPagesTall = False ' 不限制页数

    ' 设置页脚：第几页 共几页
    wsOutput.PageSetup.LeftFooter = "&P 页，共 &N 页"
    wsOutput.PageSetup.RightFooter = "&P 页，共 &N 页"

    ' 打印预览
    wsOutput.PrintPreview

    ' 按考场分开打印
    Dim uniqueExamRooms As Object
    Set uniqueExamRooms = CreateObject("Scripting.Dictionary")
    For i = 2 To lastRow
        examRoom = ws.Cells(i, 6).Value
        If Not uniqueExamRooms.Exists(examRoom) Then
            uniqueExamRooms.Add examRoom, 1
        End If
    Next i

    Dim currentExamRoom As Variant
    For Each currentExamRoom In uniqueExamRooms.Keys
        ' 筛选当前考场的桌角条
        Dim printRange As Range
        Dim firstCell As Range
        Dim lastCell As Range
        Dim cell As Range
        Set printRange = Nothing
        For Each cell In wsOutput.UsedRange
            If InStr(cell.Value, "第" & currentExamRoom & "考场") > 0 Then
                If printRange Is Nothing Then
                    Set firstCell = cell
                    Set lastCell = cell
                    Set printRange = cell.Resize(1, 5) ' 包含3列桌角条和2列间隔
                Else
                    If cell.Row > lastCell.Row Then
                        Set lastCell = cell
                    End If
                    Set printRange = Union(printRange, cell.Resize(1, 5)) ' 包含3列桌角条和2列间隔
                End If
            End If
        Next cell

        If Not printRange Is Nothing Then
            ' 设置打印区域
            wsOutput.PageSetup.PrintArea = printRange.Address

            ' 打印设置
            wsOutput.PageSetup.Orientation = xlPortrait
            wsOutput.PageSetup.PaperSize = xlPaperA4

            ' 调整页边距，将厘米转换为磅（1 厘米约等于 28.35 磅）
            wsOutput.PageSetup.TopMargin = Application.CentimetersToPoints(1) ' 减小上边距
            wsOutput.PageSetup.BottomMargin = Application.CentimetersToPoints(1) ' 减小下边距
            wsOutput.PageSetup.LeftMargin = Application.CentimetersToPoints(0.5) ' 减小左边距
            wsOutput.PageSetup.RightMargin = Application.CentimetersToPoints(0.5) ' 减小右边距

            ' 调整缩放比例，使三列刚好在一面宽度里
            wsOutput.PageSetup.FitToPagesWide = 1 ' 调整为一页宽
            wsOutput.PageSetup.FitToPagesTall = False ' 不限制页数

            ' 打印当前考场的桌角条
'            wsOutput.PrintOut
        End If
    Next currentExamRoom
End Sub