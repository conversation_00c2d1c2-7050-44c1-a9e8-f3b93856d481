Sub 排名()
    ' Calculate total scores and weighted scores in "成绩总表"
    Dim ws As Worksheet
    Dim lastRow As Long, lastCol As Long
    Dim i As <PERSON>, j <PERSON> Long
    Dim totalScore As Double
    Dim weightedScore As Double
    Dim dataArray As Variant
    Dim resultsArray As Variant
    Dim formatRange As Range
    ' Get the "成绩总表" worksheet
    On Error Resume Next
    Set ws = ThisWorkbook.Worksheets("成绩总表")
    On Error GoTo 0

    If ws Is Nothing Then
        MsgBox "找不到'成绩总表'工作表！", vbExclamation, "错误"
        Exit Sub
    End If

    ' Find the last row and column with data
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).row
    lastCol = ws.Cells(1, ws.Columns.Count).End(xlToLeft).Column

    ' Clear data in columns Q to T starting from row 2
    If lastRow >= 2 Then
        ws.Range(ws.Cells(2, 17), ws.Cells(lastRow, 20)).ClearContents ' Columns Q(17) to T(20)
    End If

    ' Find column indices for subjects and scores
    Dim colYuwen As Integer, colShuxue As Integer, colYingyu As Integer
    Dim colDaofa As Integer, colShengwu As Integer, colDili As Integer
    Dim colXinxi As Integer, colLishi As Integer, colWuli As Integer
    Dim colHuaxue As Integer, colTotal As Integer, colWeighted As Integer
    Dim colClassRank As Integer, colGradeRank As Integer
    Dim colGrade As Integer, colSchool As Integer, colClass As Integer

    colYuwen = 0: colShuxue = 0: colYingyu = 0: colDaofa = 0: colShengwu = 0
    colDili = 0: colXinxi = 0: colLishi = 0: colWuli = 0: colHuaxue = 0
    colTotal = 0: colWeighted = 0: colClassRank = 0: colGradeRank = 0
    colGrade = 0: colSchool = 0: colClass = 0

    ' Find column indices (assuming headers are in row 1)
    For i = 1 To lastCol
        Select Case ws.Cells(1, i).value
            Case "年级": colGrade = i
            Case "学校": colSchool = i
            Case "班级": colClass = i
            Case "语文": colYuwen = i
            Case "数学": colShuxue = i
            Case "英语": colYingyu = i
            Case "道法": colDaofa = i
            Case "生物": colShengwu = i
            Case "地理": colDili = i
            Case "信息": colXinxi = i
            Case "历史": colLishi = i
            Case "物理": colWuli = i
            Case "化学": colHuaxue = i
            Case "总分": colTotal = i
            Case "折分": colWeighted = i
            Case "班级排名": colClassRank = i
            Case "年级排名": colGradeRank = i
        End Select
    Next i

    ' Add ranking columns if they don't exist
    If colClassRank = 0 Then
        ' Find the next available column
        Dim nextCol As Integer
        nextCol = lastCol + 1

        ' Add "班级排名" column
        ws.Cells(1, nextCol).value = "班级排名"
        colClassRank = nextCol

        ' Add "年级排名" column
        ws.Cells(1, nextCol + 1).value = "年级排名"
        colGradeRank = nextCol + 1

        ' Update lastCol
        lastCol = nextCol + 1
    End If

    ' Load all data into an array for faster processing
    dataArray = ws.Range(ws.Cells(1, 1), ws.Cells(lastRow, lastCol)).value

    ' Create a results array for total scores, weighted scores, and rankings
    ReDim resultsArray(1 To lastRow, 1 To 4) ' 4 columns: total, weighted, class rank, grade rank

    ' Process the data array - clean non-positive and non-numeric values in columns G to P
    For i = 2 To UBound(dataArray, 1) ' Start from row 2
        ' Check and clean subject scores (columns G to P)
        If colYuwen > 0 Then
            If Not IsNumeric(dataArray(i, colYuwen)) Or dataArray(i, colYuwen) <= 0 Then
                dataArray(i, colYuwen) = ""
            End If
        End If

        If colShuxue > 0 Then
            If Not IsNumeric(dataArray(i, colShuxue)) Or dataArray(i, colShuxue) <= 0 Then
                dataArray(i, colShuxue) = ""
            End If
        End If

        If colYingyu > 0 Then
            If Not IsNumeric(dataArray(i, colYingyu)) Or dataArray(i, colYingyu) <= 0 Then
                dataArray(i, colYingyu) = ""
            End If
        End If

        If colDaofa > 0 Then
            If Not IsNumeric(dataArray(i, colDaofa)) Or dataArray(i, colDaofa) <= 0 Then
                dataArray(i, colDaofa) = ""
            End If
        End If

        If colShengwu > 0 Then
            If Not IsNumeric(dataArray(i, colShengwu)) Or dataArray(i, colShengwu) <= 0 Then
                dataArray(i, colShengwu) = ""
            End If
        End If

        If colDili > 0 Then
            If Not IsNumeric(dataArray(i, colDili)) Or dataArray(i, colDili) <= 0 Then
                dataArray(i, colDili) = ""
            End If
        End If

        If colXinxi > 0 Then
            If Not IsNumeric(dataArray(i, colXinxi)) Or dataArray(i, colXinxi) <= 0 Then
                dataArray(i, colXinxi) = ""
            End If
        End If

        If colLishi > 0 Then
            If Not IsNumeric(dataArray(i, colLishi)) Or dataArray(i, colLishi) <= 0 Then
                dataArray(i, colLishi) = ""
            End If
        End If

        If colWuli > 0 Then
            If Not IsNumeric(dataArray(i, colWuli)) Or dataArray(i, colWuli) <= 0 Then
                dataArray(i, colWuli) = ""
            End If
        End If

        If colHuaxue > 0 Then
            If Not IsNumeric(dataArray(i, colHuaxue)) Or dataArray(i, colHuaxue) <= 0 Then
                dataArray(i, colHuaxue) = ""
            End If
        End If

        ' Calculate total score
        totalScore = 0
        If colYuwen > 0 And IsNumeric(dataArray(i, colYuwen)) Then totalScore = totalScore + dataArray(i, colYuwen)
        If colShuxue > 0 And IsNumeric(dataArray(i, colShuxue)) Then totalScore = totalScore + dataArray(i, colShuxue)
        If colYingyu > 0 And IsNumeric(dataArray(i, colYingyu)) Then totalScore = totalScore + dataArray(i, colYingyu)
        If colDaofa > 0 And IsNumeric(dataArray(i, colDaofa)) Then totalScore = totalScore + dataArray(i, colDaofa)
        If colShengwu > 0 And IsNumeric(dataArray(i, colShengwu)) Then totalScore = totalScore + dataArray(i, colShengwu)
        If colDili > 0 And IsNumeric(dataArray(i, colDili)) Then totalScore = totalScore + dataArray(i, colDili)
        If colXinxi > 0 And IsNumeric(dataArray(i, colXinxi)) Then totalScore = totalScore + dataArray(i, colXinxi)
        If colLishi > 0 And IsNumeric(dataArray(i, colLishi)) Then totalScore = totalScore + dataArray(i, colLishi)
        If colWuli > 0 And IsNumeric(dataArray(i, colWuli)) Then totalScore = totalScore + dataArray(i, colWuli)
        If colHuaxue > 0 And IsNumeric(dataArray(i, colHuaxue)) Then totalScore = totalScore + dataArray(i, colHuaxue)

        ' Calculate weighted score
        Dim yuwen As Double, shuxue As Double, yingyu As Double
        Dim daofa As Double, shengwu As Double, lishi As Double
        Dim dili As Double, huaxue As Double, wuli As Double

        yuwen = IIf(colYuwen > 0 And IsNumeric(dataArray(i, colYuwen)), dataArray(i, colYuwen), 0)
        shuxue = IIf(colShuxue > 0 And IsNumeric(dataArray(i, colShuxue)), dataArray(i, colShuxue), 0)
        yingyu = IIf(colYingyu > 0 And IsNumeric(dataArray(i, colYingyu)), dataArray(i, colYingyu), 0)
        daofa = IIf(colDaofa > 0 And IsNumeric(dataArray(i, colDaofa)), dataArray(i, colDaofa), 0)
        shengwu = IIf(colShengwu > 0 And IsNumeric(dataArray(i, colShengwu)), dataArray(i, colShengwu), 0)
        lishi = IIf(colLishi > 0 And IsNumeric(dataArray(i, colLishi)), dataArray(i, colLishi), 0)
        dili = IIf(colDili > 0 And IsNumeric(dataArray(i, colDili)), dataArray(i, colDili), 0)
        huaxue = IIf(colHuaxue > 0 And IsNumeric(dataArray(i, colHuaxue)), dataArray(i, colHuaxue), 0)
        wuli = IIf(colWuli > 0 And IsNumeric(dataArray(i, colWuli)), dataArray(i, colWuli), 0)

        ' Add 10 points if information score is 60 or above, otherwise 0 points
        Dim xinxiPoints As Double
        xinxiPoints = IIf(colXinxi > 0 And IsNumeric(dataArray(i, colXinxi)) And dataArray(i, colXinxi) >= 60, 10, 0)

        weightedScore = yuwen + shuxue + yingyu + (daofa + shengwu + lishi) * 0.4 + (dili + huaxue) * 0.3 + wuli * 0.5 + xinxiPoints

        ' Store results in the results array
        If totalScore > 0 Then
            resultsArray(i, 1) = totalScore ' Total score
            resultsArray(i, 2) = Round(weightedScore, 2) ' Weighted score
        Else
            resultsArray(i, 1) = "" ' Total score
            resultsArray(i, 2) = "" ' Weighted score
        End If
    Next i

    ' Write the cleaned data back to the worksheet
    ws.Range(ws.Cells(1, 1), ws.Cells(lastRow, lastCol)).value = dataArray

    ' Write total scores and weighted scores to the worksheet
    For i = 2 To lastRow
        If colTotal > 0 Then ws.Cells(i, colTotal).value = resultsArray(i, 1)
        If colWeighted > 0 Then ws.Cells(i, colWeighted).value = resultsArray(i, 2)
    Next i

    ' Calculate rankings after all scores are calculated
    Call CalculateRankings(ws, lastRow, colGrade, colSchool, colClass, colWeighted, colClassRank, colGradeRank)
End Sub

Sub CalculateRankings(ws As Worksheet, lastRow As Long, colGrade As Integer, colSchool As Integer, colClass As Integer, colWeighted As Integer, colClassRank As Integer, colGradeRank As Integer)
    ' Calculate class and grade rankings based on weighted scores
    Dim i As Long, j As Long
    Dim gradeDict As Object
    Dim classDict As Object
    Dim gradeClassKey As Variant
    Dim grade As Variant, school As String, class As String
    Dim studentKey As Variant
    Dim dataArray As Variant
    Dim rankArray As Variant

    ' Load all data into an array for faster processing
    dataArray = ws.Range(ws.Cells(1, 1), ws.Cells(lastRow, colGradeRank)).value

    ' Create a rank array to store class and grade rankings
    ReDim rankArray(1 To lastRow, 1 To 2) ' 2 columns: class rank, grade rank

    ' Create dictionaries to store grades and classes
    Set gradeDict = CreateObject("Scripting.Dictionary")
    Set classDict = CreateObject("Scripting.Dictionary")

    ' Collect all grades and classes
    For i = 2 To UBound(dataArray, 1)
        grade = dataArray(i, colGrade)
        school = dataArray(i, colSchool)
        class = dataArray(i, colClass)

        ' Skip if no weighted score
        If IsEmpty(dataArray(i, colWeighted)) Or Not IsNumeric(dataArray(i, colWeighted)) Then
            rankArray(i, 1) = "" ' Class rank
            rankArray(i, 2) = "" ' Grade rank
            GoTo NextStudent
        End If

        ' Add grade to dictionary if not exists
        If Not gradeDict.Exists(grade) Then
            gradeDict.Add grade, CreateObject("Scripting.Dictionary")
        End If

        ' Create grade-class key
        gradeClassKey = grade & "|" & school & "|" & class

        ' Add class to dictionary if not exists
        If Not classDict.Exists(gradeClassKey) Then
            classDict.Add gradeClassKey, CreateObject("Scripting.Dictionary")
        End If

        ' Add student to grade and class dictionaries
        Dim weightedScore As Double
        weightedScore = dataArray(i, colWeighted)

        ' Add to grade dictionary with row number as key
        gradeDict(grade).Add i, weightedScore

        ' Add to class dictionary with row number as key
        classDict(gradeClassKey).Add i, weightedScore

NextStudent:
    Next i

    ' Calculate class rankings
    Dim classStudents As Object
    Dim classScores() As Double
    Dim classRows() As Long
    Dim classCount As Long
    Dim currentRank As Long
    Dim tempScore As Double
    Dim tempRow As Long

    For Each gradeClassKey In classDict.Keys
        ' Get all students in this class
        Set classStudents = classDict(gradeClassKey)

        ' Sort students by weighted score (descending)
        classCount = classStudents.Count
        ReDim classScores(1 To classCount)
        ReDim classRows(1 To classCount)

        j = 1
        For Each studentKey In classStudents.Keys
            classRows(j) = studentKey
            classScores(j) = classStudents(studentKey)
            j = j + 1
        Next studentKey

        ' Sort using bubble sort (descending)
        For i = 1 To classCount - 1
            For j = i + 1 To classCount
                If classScores(i) < classScores(j) Then
                    ' Swap scores
                    tempScore = classScores(i)
                    classScores(i) = classScores(j)
                    classScores(j) = tempScore

                    ' Swap rows
                    tempRow = classRows(i)
                    classRows(i) = classRows(j)
                    classRows(j) = tempRow
                End If
            Next j
        Next i

        ' Assign class ranks with proper handling of ties
        currentRank = 1

        ' Assign first rank
        rankArray(classRows(1), 1) = currentRank

        ' Assign remaining ranks with tie handling
        For i = 2 To classCount
            If classScores(i) < classScores(i - 1) Then
                ' Different score, increment rank
                currentRank = i
            End If
            ' Assign the rank (same rank for tied scores)
            rankArray(classRows(i), 1) = currentRank
        Next i
    Next gradeClassKey

    ' Calculate grade rankings
    Dim gradeStudents As Object
    Dim gradeScores() As Double
    Dim gradeRows() As Long
    Dim gradeCount As Long
    Dim currentGradeRank As Long
    Dim tempGradeScore As Double
    Dim tempGradeRow As Long

    For Each grade In gradeDict.Keys
        ' Get all students in this grade
        Set gradeStudents = gradeDict(grade)

        ' Sort students by weighted score (descending)
        gradeCount = gradeStudents.Count
        ReDim gradeScores(1 To gradeCount)
        ReDim gradeRows(1 To gradeCount)

        j = 1
        For Each studentKey In gradeStudents.Keys
            gradeRows(j) = studentKey
            gradeScores(j) = gradeStudents(studentKey)
            j = j + 1
        Next studentKey

        ' Sort using bubble sort (descending)
        For i = 1 To gradeCount - 1
            For j = i + 1 To gradeCount
                If gradeScores(i) < gradeScores(j) Then
                    ' Swap scores
                    tempGradeScore = gradeScores(i)
                    gradeScores(i) = gradeScores(j)
                    gradeScores(j) = tempGradeScore

                    ' Swap rows
                    tempGradeRow = gradeRows(i)
                    gradeRows(i) = gradeRows(j)
                    gradeRows(j) = tempGradeRow
                End If
            Next j
        Next i

        ' Assign grade ranks with proper handling of ties
        currentGradeRank = 1

        ' Assign first rank
        rankArray(gradeRows(1), 2) = currentGradeRank

        ' Assign remaining ranks with tie handling
        For i = 2 To gradeCount
            If gradeScores(i) < gradeScores(i - 1) Then
                ' Different score, increment rank
                currentGradeRank = i
            End If
            ' Assign the rank (same rank for tied scores)
            rankArray(gradeRows(i), 2) = currentGradeRank
        Next i
    Next grade

    ' Write rankings back to the worksheet
    For i = 2 To lastRow
        ws.Cells(i, colClassRank).value = rankArray(i, 1)
        ws.Cells(i, colGradeRank).value = rankArray(i, 2)
    Next i
    Set formatRange = ws.Range("G2:R" & lastRow)
    formatRange.NumberFormat = "0.00"
End Sub
