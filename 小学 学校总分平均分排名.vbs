 ' 主程序
Sub Main()
    On Error Resume Next
    
    Dim wsSource As Worksheet, wsTarget As Worksheet
    Dim lastRow As Long, i As Long
    Dim schoolDict As Object
    Dim arrData(), arrOutput()
    Dim gradeArr(1 To 6) As String
    Dim subjectArr(1 To 5) As String
    Dim subjectCols(1 To 13) As Integer
    Dim totalScoreCol As Integer
    
    ' 初始化年级数组
    gradeArr(1) = "一": gradeArr(2) = "二": gradeArr(3) = "三"
    gradeArr(4) = "四": gradeArr(5) = "五": gradeArr(6) = "六"
    
    ' 初始化科目数组和对应列号(根据实际调整)
    subjectArr(1) = "语文": subjectCols(1) = 9 ' I列
    subjectArr(2) = "数学": subjectCols(2) = 10 ' J列
    subjectArr(3) = "英语": subjectCols(3) = 11 ' K列
    subjectArr(4) = "科学": subjectCols(4) = 12 ' L列
    subjectArr(5) = "道德与法治": subjectCols(5) = 13 ' M列

    
    totalScoreCol = 23 ' W列(总分)
    
    ' 设置工作表
    Set wsSource = ThisWorkbook.Worksheets("成绩汇总表")
    Set wsTarget = ThisWorkbook.Worksheets("学校统计")
    
    If wsSource Is Nothing Or wsTarget Is Nothing Then
        MsgBox "请确保工作簿包含'成绩汇总表'和'学校统计'工作表", vbExclamation
        Exit Sub
    End If
    
    ' 获取源数据最后一行
    lastRow = wsSource.Cells(wsSource.rows.Count, "A").End(xlUp).Row
    If lastRow < 2 Then
        MsgBox "成绩汇总表中没有数据", vbExclamation
        Exit Sub
    End If
    
    ' 读取源数据到数组
    arrData = wsSource.Range("A2:X" & lastRow).Value
    
    ' 创建字典存储学校数据
    Set schoolDict = CreateObject("Scripting.Dictionary")
    
    ' 处理数据
    ProcessData arrData, schoolDict, gradeArr, subjectArr, subjectCols, totalScoreCol
    
    ' 准备输出数组
    PrepareOutput schoolDict, gradeArr, subjectArr, arrOutput
    
    ' 输出结果
    OutputResults wsTarget, arrOutput
    
    ' 清理
    Set schoolDict = Nothing
    Set wsSource = Nothing
    Set wsTarget = Nothing
    
    MsgBox "统计完成!", vbInformation
End Sub

' 处理数据
Sub ProcessData(arrData, schoolDict, gradeArr, subjectArr, subjectCols, totalScoreCol)
    Dim i As Long, j As Long
    Dim schoolName As String, gradeName As String
    Dim totalScore As Double, subjectScore As Double
    Dim schoolGradeKey As String
    Dim schoolData As Object, gradeData As Object
    
    ' 添加调试信息
    Debug.Print "开始处理数据，共" & UBound(arrData) - LBound(arrData) + 1 & "行数据"
    
    For i = LBound(arrData) To UBound(arrData)
        schoolName = Trim(arrData(i, 2)) ' B列:学校
        gradeName = Trim(arrData(i, 4)) ' D列:年级
        
        ' 检查总分是否存在
        If IsNumeric(arrData(i, totalScoreCol)) Then
            totalScore = arrData(i, totalScoreCol)
        Else
            totalScore = 0
        End If
        
        ' 调试输出当前行信息
        Debug.Print "处理第" & i & "行 - 学校:" & schoolName & ", 年级:" & gradeName & ", 总分:" & totalScore
        
        ' 放宽有效性条件，只跳过完全无效的行
        If schoolName = "" Then 
            Debug.Print "跳过无效行(缺少学校信息)"
            GoTo NextRow
        End If
        
        ' 如果年级为空，使用默认年级"一"
        If gradeName = "" Then
            gradeName = "一"
            Debug.Print "警告: 第" & i & "行缺少年级信息，使用默认年级'一'"
        End If
        
        ' 允许总分为0的记录，仅记录警告
        If totalScore = 0 Then
            Debug.Print "警告: 第" & i & "行总分为0，但仍计入统计"
            totalScore = 0.01 ' 设置为极小值避免影响排名
        End If
        
        ' 确保学校名称标准化
        schoolName = Trim(schoolName)
        If schoolName = "育英学校" Then
            Debug.Print "处理育英学校数据"
        End If
        
        ' 调试字典操作
        Debug.Print "处理学校: " & schoolName & ", 年级: " & gradeName
        
        ' 如果字典中没有该学校，则添加
        If Not schoolDict.Exists(schoolName) Then
            Debug.Print "新增学校: " & schoolName
            Set schoolData = CreateObject("Scripting.Dictionary")
            schoolDict.Add schoolName, schoolData
        Else
            Set schoolData = schoolDict(schoolName)
        End If
        
        ' 如果学校字典中没有该年级，则添加
        If Not schoolData.Exists(gradeName) Then
            Debug.Print "新增年级: " & gradeName & " 到学校 " & schoolName
            Set gradeData = CreateObject("Scripting.Dictionary")
            ' 初始化年级数据
            With gradeData
                .Add "Count", 0                  ' 参考人数
                .Add "TotalScoreSum", 0          ' 总分总和
                .Add "ExcellentCount", 0         ' 优秀人数(总分>=425)
                .Add "PassCount", 0              ' 合格人数(总分>=300)
                .Add "LowScoreCount", 0          ' 低分人数(总分<200)
                .Add "MaxScore", 0               ' 最高分
                .Add "MinScore", 1000            ' 最低分(初始设为较大值)
                
                ' 初始化各科数据
                For j = LBound(subjectArr) To UBound(subjectArr)
                    .Add subjectArr(j) & "_Sum", 0    ' 科目总分
                    .Add subjectArr(j) & "_Count", 0  ' 科目有分人数
                Next j
            End With
            schoolData.Add gradeName, gradeData
        Else
            Set gradeData = schoolData(gradeName)
            Debug.Print "使用已有年级: " & gradeName & " 在学校 " & schoolName & _
                        ", 当前人数: " & gradeData("Count")
        End If
        
        ' 更新统计数据
        With gradeData
            ' 更新人数统计
            .Item("Count") = .Item("Count") + 1
            .Item("TotalScoreSum") = .Item("TotalScoreSum") + totalScore
            
            ' 更新分数段统计
            If totalScore >= 425 Then
                .Item("ExcellentCount") = .Item("ExcellentCount") + 1
            End If
            If totalScore >= 300 Then
                .Item("PassCount") = .Item("PassCount") + 1
            End If
            If totalScore < 200 Then
                .Item("LowScoreCount") = .Item("LowScoreCount") + 1
            End If
            
            ' 更新最高分和最低分
            If totalScore > .Item("MaxScore") Then
                .Item("MaxScore") = totalScore
            End If
            If totalScore < .Item("MinScore") Then
                .Item("MinScore") = totalScore
            End If
            
            ' 更新各科统计
            For j = LBound(subjectArr) To UBound(subjectArr)
                If IsNumeric(arrData(i, subjectCols(j))) Then
                    subjectScore = arrData(i, subjectCols(j))
                    If subjectScore > 0 Then
                        .Item(subjectArr(j) & "_Sum") = .Item(subjectArr(j) & "_Sum") + subjectScore
                        .Item(subjectArr(j) & "_Count") = .Item(subjectArr(j) & "_Count") + 1
                    End If
                End If
            Next j
        End With
        
NextRow:
    Next i
End Sub

' 准备输出数组 - 按年级分组后立即排名
Sub PrepareOutput(schoolDict, gradeArr, subjectArr, arrOutput)
    Dim schoolName As Variant, gradeName As Variant
    Dim schoolData As Object, gradeData As Object
    Dim outputRow As Long, i As Long, j As Long
    Dim tempArr() As Variant
    Dim schoolCount As Long, gradeCount As Long
    Dim totalScoreAvg As Double, subjectAvg As Double
    
    ' 计算输出行数
    schoolCount = schoolDict.Count
    gradeCount = UBound(gradeArr) - LBound(gradeArr) + 1
    ReDim arrOutput(1 To schoolCount * gradeCount + 1, 1 To 25) ' 25列对应目标表
    
    ' 按年级分组数据
    Dim gradeDict As Object
    Set gradeDict = CreateObject("Scripting.Dictionary")
    
    ' 先收集所有数据
    outputRow = 1
    For Each schoolName In schoolDict.Keys
        Set schoolData = schoolDict(schoolName)
        
        For i = LBound(gradeArr) To UBound(gradeArr)
            gradeName = gradeArr(i)
            
            If schoolData.Exists(gradeName) Then
                Set gradeData = schoolData(gradeName)
                
                ' 填充基础数据
                arrOutput(outputRow, 1) = gradeName
                arrOutput(outputRow, 2) = schoolName
                arrOutput(outputRow, 3) = gradeData("Count")
                
                ' 计算总分平均分
                If gradeData("Count") > 0 Then
                    totalScoreAvg = gradeData("TotalScoreSum") / gradeData("Count")
                    arrOutput(outputRow, 4) = Round(totalScoreAvg, 2)
                Else
                    arrOutput(outputRow, 4) = 0
                End If
                
                ' 填充分数段数据
                arrOutput(outputRow, 6) = gradeData("ExcellentCount")
                If gradeData("Count") > 0 Then
                    arrOutput(outputRow, 7) = Round(gradeData("ExcellentCount") / gradeData("Count") * 100, 2)
                Else
                    arrOutput(outputRow, 7) = 0
                End If
                
                arrOutput(outputRow, 8) = gradeData("PassCount")
                If gradeData("Count") > 0 Then
                    arrOutput(outputRow, 9) = Round(gradeData("PassCount") / gradeData("Count") * 100, 2)
                Else
                    arrOutput(outputRow, 9) = 0
                End If
                
                arrOutput(outputRow, 10) = gradeData("LowScoreCount")
                If gradeData("Count") > 0 Then
                    arrOutput(outputRow, 11) = Round(gradeData("LowScoreCount") / gradeData("Count") * 100, 2)
                Else
                    arrOutput(outputRow, 11) = 0
                End If
                
                arrOutput(outputRow, 12) = gradeData("MaxScore")
                arrOutput(outputRow, 13) = gradeData("MinScore")
                
                ' 填充各科平均分
                For j = LBound(subjectArr) To UBound(subjectArr)
                    If gradeData(subjectArr(j) & "_Count") > 0 Then
                        subjectAvg = gradeData(subjectArr(j) & "_Sum") / gradeData(subjectArr(j) & "_Count")
                        arrOutput(outputRow, 13 + j * 2) = Round(subjectAvg, 2)
                    Else
                        arrOutput(outputRow, 13 + j * 2) = 0
                    End If
                Next j
                
                ' 按年级分组存储行号
                If Not gradeDict.Exists(gradeName) Then
                    gradeDict.Add gradeName, CreateObject("System.Collections.ArrayList")
                End If
                gradeDict(gradeName).Add outputRow
                
                outputRow = outputRow + 1
            End If
        Next i
    Next schoolName
    
    ' 对每个年级分别进行排名
    Dim gradeRows As Object, row As Variant
    For Each gradeName In gradeDict.Keys
        Set gradeRows = gradeDict(gradeName)
        
        ' 对总分排名(E列=5)
        CalculateGradeRanking arrOutput, gradeRows, 4, 5
        
        ' 对各科目排名
        ' 语文排名(P列=16)
        CalculateGradeRanking arrOutput, gradeRows, 15, 16
        ' 数学排名(R列=18)
        CalculateGradeRanking arrOutput, gradeRows, 17, 18
        ' 英语排名(T列=20)
        CalculateGradeRanking arrOutput, gradeRows, 19, 20
        ' 科学排名(V列=22)
        CalculateGradeRanking arrOutput, gradeRows, 21, 22
        ' 道法排名(X列=24)
        CalculateGradeRanking arrOutput, gradeRows, 23, 24
    Next
End Sub

' 计算单个年级的排名
Sub CalculateGradeRanking(arrOutput, gradeRows, scoreCol, rankCol)
    Dim scoreList As Object
    Set scoreList = CreateObject("System.Collections.ArrayList")
    
    ' 1. 收集所有有效数据(仅排除育英学校)
    For Each row In gradeRows
        If arrOutput(row, 2) <> "育英学校" Then
            ' 包括0分在内的所有分数
            If IsNumeric(arrOutput(row, scoreCol)) Then
                scoreList.Add Array(arrOutput(row, scoreCol), row)
                Debug.Print "添加数据: 行" & row & ", 分数=" & arrOutput(row, scoreCol)
            Else
                arrOutput(row, rankCol) = "-"
                Debug.Print "跳过非数值数据: 行" & row
            End If
        Else
            arrOutput(row, rankCol) = "-"
            Debug.Print "跳过育英学校: 行" & row
        End If
    Next
    
    ' 2. 按分数降序排序
    If scoreList.Count > 0 Then
        Debug.Print "开始排序，共" & scoreList.Count & "条数据"
        
        ' 使用快速排序
        QuickSortScores scoreList
        
        ' 3. 分配排名(处理并列)
        Dim currentRank As Long: currentRank = 1
        Dim prevScore As Double: prevScore = -1
        
        For i = 0 To scoreList.Count - 1
            Dim item: item = scoreList(i)
            Dim score As Double: score = item(0)
            Dim row As Long: row = item(1)
            
            If i > 0 And score <> prevScore Then
                currentRank = i + 1
            End If
            
            ' 0分也参与排名
            arrOutput(row, rankCol) = currentRank
            Debug.Print "分配排名: 行" & row & ", 分数=" & score & ", 排名=" & currentRank
            prevScore = score
        Next i
    Else
        Debug.Print "警告: 没有有效数据可排名"
    End If
End Sub

' 快速排序分数列表
Sub QuickSortScores(list)
    If list.Count <= 1 Then Exit Sub
    
    Dim pivot: pivot = list(0)(0)
    Dim less As Object, equal As Object, greater As Object
    Set less = CreateObject("System.Collections.ArrayList")
    Set equal = CreateObject("System.Collections.ArrayList")
    Set greater = CreateObject("System.Collections.ArrayList")
    
    ' 分区
    For Each item In list
        If item(0) > pivot Then
            greater.Add item
        ElseIf item(0) = pivot Then
            equal.Add item
        Else
            less.Add item
        End If
    Next
    
    ' 递归排序
    QuickSortScores greater
    QuickSortScores less
    
    ' 合并结果
    list.Clear
    For Each item In greater
        list.Add item
    Next
    For Each item In equal
        list.Add item
    Next
    For Each item In less
        list.Add item
    Next
End Sub
End Sub

' 计算排名 - 按年级分组排名(参考校级排名逻辑)
Sub CalculateRankings(arrOutput, gradeArr, subjectArr)
    Dim gradeDict As Object
    Set gradeDict = CreateObject("Scripting.Dictionary")
    
    ' 1. 按年级分组数据
    Dim i As Long, j As Long
    For i = 1 To UBound(arrOutput, 1)
        Dim grade As Variant
        grade = arrOutput(i, 1)
        
        If Not gradeDict.Exists(grade) Then
            gradeDict.Add grade, CreateObject("System.Collections.ArrayList")
        End If
        gradeDict(grade).Add i ' 存储行号
    Next
    
    ' 2. 处理每个年级的排名
    Dim rowIndices As Object
    For Each grade In gradeDict.Keys
        Set rowIndices = gradeDict(grade)
        
        ' 2.1 总分排名 (确保排名写入第5列)
        CalculateSingleRanking arrOutput, rowIndices, 4, 5 
        
        ' 2.2 各科目排名
        For j = 1 To UBound(subjectArr)
            Dim scoreCol As Long, rankCol As Long
            scoreCol = 13 + j * 2     ' 科目分数列(13,15,17...)
            rankCol = scoreCol + 1     ' 科目排名列(14,16,18...)
            
            ' 特殊处理：确保不覆盖参考人数(第3列)和总分排名(第5列)
            If rankCol <> 3 And rankCol <> 5 Then 
                ' 处理特殊学校
                For i = 0 To rowIndices.Count - 1
                    Dim row As Long
                    row = rowIndices(i)
                    If arrOutput(row, 2) = "育英学校" Or arrOutput(row, scoreCol) = 0 Then
                        arrOutput(row, rankCol) = "-"
                    End If
                Next
                
                ' 计算有效学校的排名
                CalculateSingleRanking arrOutput, rowIndices, scoreCol, rankCol
            End If
        Next
    Next
End Sub

' 计算单个排名(总分或科目)
Sub CalculateSingleRanking(arrOutput, rowIndices, scoreCol, rankCol)
    ' 1. 提取分数数据
    Dim scores(), rows()
    ReDim scores(rowIndices.Count - 1)
    ReDim rows(rowIndices.Count - 1)
    
    For i = 0 To rowIndices.Count - 1
        rows(i) = rowIndices(i)
        scores(i) = arrOutput(rows(i), scoreCol)
    Next
    
    ' 2. 排序(降序)
    QuickSort scores, rows, 0, UBound(scores)
    
    ' 3. 分配排名(处理并列)
    Dim currentRank As Long: currentRank = 1
    For i = 0 To UBound(scores)
        ' 处理并列情况
        If i > 0 And scores(i) <> scores(i - 1) Then
            currentRank = i + 1
        End If
        
        ' 写入排名
        arrOutput(rows(i), rankCol) = currentRank
    Next
End Sub

' 快速排序算法
Sub QuickSort(ByRef scores(), ByRef rows(), ByVal low As Long, ByVal high As Long)
    If low < high Then
        Dim pivot As Double
        pivot = Partition(scores, rows, low, high)
        QuickSort scores, rows, low, pivot - 1
        QuickSort scores, rows, pivot + 1, high
    End If
End Sub

' 分区函数(快速排序辅助)
Function Partition(ByRef scores(), ByRef rows(), ByVal low As Long, ByVal high As Long) As Long
    Dim pivot As Double: pivot = scores(high)
    Dim i As Long: i = low - 1
    
    For j = low To high - 1
        If scores(j) >= pivot Then ' 降序排序
            i = i + 1
            Swap scores(i), scores(j)
            Swap rows(i), rows(j)
        End If
    Next
    
    Swap scores(i + 1), scores(high)
    Swap rows(i + 1), rows(high)
    Partition = i + 1
End Function

' 交换两个变量
Sub Swap(ByRef a, ByRef b)
    Dim temp
    temp = a
    a = b
    b = temp
End Sub


' 输出结果到工作表
Sub OutputResults(wsTarget As Worksheet, arrOutput)
    On Error Resume Next
    
    Dim lastRow As Long
    Dim outputRange As Range
    
    ' 清除旧数据(从第5行开始)
    lastRow = wsTarget.Cells(wsTarget.rows.Count, "A").End(xlUp).Row
    If lastRow >= 5 Then
        wsTarget.Range("A5:X" & lastRow).ClearContents
    End If
    
    ' 设置输出范围
    Set outputRange = wsTarget.Range("A5").Resize(UBound(arrOutput, 1), UBound(arrOutput, 2))
    
    ' 计算并添加排名
    Dim i As Long, j As Long
    Dim rankCount As Long
    Dim tempArr() As Variant
    ReDim tempArr(1 To UBound(arrOutput, 1), 1 To 1)
    
    ' 筛选有效学校(排除育英学校和没有成绩的学校)
    rankCount = 0
    For i = 1 To UBound(arrOutput, 1)
        If arrOutput(i, 1) <> "育英学校" And arrOutput(i, 4) <> 0 Then
            rankCount = rankCount + 1
            tempArr(rankCount, 1) = arrOutput(i, 4) ' 存储总分平均分用于排序
        End If
    Next i
    
    ' 对有效学校进行排名
    If rankCount > 0 Then
        ' 排序tempArr数组
        For i = 1 To rankCount - 1
            For j = i + 1 To rankCount
                If tempArr(i, 1) < tempArr(j, 1) Then
                    Dim tempVal As Variant
                    tempVal = tempArr(i, 1)
                    tempArr(i, 1) = tempArr(j, 1)
                    tempArr(j, 1) = tempVal
                End If
            Next j
        Next i
        
        ' 添加排名到arrOutput
        Dim currentRank As Long
        currentRank = 0
        For i = 1 To UBound(arrOutput, 1)
            If arrOutput(i, 1) = "育英学校" Or arrOutput(i, 4) = 0 Then
                arrOutput(i, 3) = "-" ' 不参与排名的学校显示为-
            Else
                currentRank = currentRank + 1
                arrOutput(i, 3) = currentRank ' 添加排名
            End If
        Next i
    End If
    
    ' 写入数据
    outputRange.Value = arrOutput
    
    ' 设置数字格式
    With outputRange
        ' 设置平均分保留2位小数
        .Columns(4).NumberFormat = "0.00" ' 总分平均分
        .Columns(7).NumberFormat = "0.00" ' 优秀比例
        .Columns(9).NumberFormat = "0.00" ' 合格比例
        .Columns(11).NumberFormat = "0.00" ' 低分比例
        
        ' 设置各科平均分格式
        For i = 1 To 13
            .Columns(13 + i * 2).NumberFormat = "0.00"
        Next i
    End With
    
    ' 自动调整列宽
    outputRange.EntireColumn.AutoFit
    
    ' 错误处理
    If Err.Number <> 0 Then
        MsgBox "输出结果时出错: " & Err.Description, vbExclamation
        Err.Clear
    End If
End Sub