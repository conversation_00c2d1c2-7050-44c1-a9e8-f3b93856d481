
Sub 门贴打印()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim examRoom As Integer
    Dim uniqueSites As Object
    Dim uniqueRooms As Object
    Dim i As Long
    Dim j As Long
    Dim k As Long
    Dim col As Long
    Dim newSheet As Worksheet ' 用于存储所有门贴的工作表
    Dim studentCount As Long
    Dim colChars As Variant
    Dim colVariant As Variant

    ' 新增代码：删除所有以"考场"结尾的工作表
    Application.DisplayAlerts = False
    Dim sht As Variant
    For Each sht In ThisWorkbook.Sheets
        If Right(sht.Name, 2) = "所有门贴" And sht.Name <> "学生信息汇总表" Then
            sht.Delete
        End If
    Next sht
    Application.DisplayAlerts = True

    ' 设置当前工作表
    Set ws = ThisWorkbook.Sheets("学生信息汇总表")
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

    ' 创建字典用于存储唯一的校点和考场
    Set uniqueSites = CreateObject("Scripting.Dictionary")
    Set uniqueRooms = CreateObject("Scripting.Dictionary")

    ' 按学校、校点、考场号和座位号排序
    ws.Sort.SortFields.Clear
    ws.Sort.SortFields.Add key:=ws.Range("A2:A" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ws.Sort.SortFields.Add key:=ws.Range("B2:B" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ws.Sort.SortFields.Add key:=ws.Range("G2:G" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ws.Sort.SortFields.Add key:=ws.Range("H2:H" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    With ws.Sort
        .SetRange ws.Range("A1:I" & lastRow)
        .Header = xlYes
        .MatchCase = False
        .Orientation = xlTopToBottom
        .SortMethod = xlPinYin
        .Apply
    End With

    ' 遍历数据，收集唯一的校点和考场
    For i = 2 To lastRow
        Dim schoolName As String
        schoolName = ws.Cells(i, 1).Value ' 学校名称
        examSite = Trim(ws.Cells(i, 2).Value) ' 校点，去除首尾空格
        If examSite <> "" Then ' 检查是否为空
            examRoom = ws.Cells(i, 7).Value
            If Not uniqueSites.Exists(examSite) Then
                uniqueSites.Add examSite, 1
            End If
            If Not uniqueRooms.Exists(examSite & "-" & examRoom) Then
                uniqueRooms.Add examSite & "-" & examRoom, 1
            End If
        End If
    Next i

    ' 检查是否存在名为“所有门贴”的工作表，如果存在则删除
    Dim tempWs As Variant
    For Each tempWs In ThisWorkbook.Sheets
        If tempWs.Name = "所有门贴" Then
            Application.DisplayAlerts = False ' 关闭警告提示
            tempWs.Delete
            Application.DisplayAlerts = True ' 打开警告提示
            Exit For
        End If
    Next tempWs

    ' 创建一个工作表用于存储所有门贴
    Set newSheet = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
    newSheet.Name = "所有门贴"

    k = 1 ' 初始化 k 的值

    ' 跟踪当前学校
    Dim currentSchoolName As String
    Dim previousSchoolName As String
    Dim examSiteVar As Variant
    Dim examRoomKeyVar As Variant
    Dim examRoomKey As Variant

    previousSchoolName = ""

    ' 按校点和考场顺序打印
    For Each examSite In uniqueSites.Keys
        Debug.Print "当前校点: " & examSite
        For Each examRoomKey In uniqueRooms.Keys
            Debug.Print "当前考场: " & examRoomKey
            If Left(examRoomKey, Len(examSite)) = examSite Then
                examRoom = Val(Mid(examRoomKey, InStr(examRoomKey, "-") + 1))

                ' 设置表头
                If k >= 1 And k <= newSheet.Rows.Count Then
                    ' 获取对应的学校名称
                    Dim tempSchoolName As String
                    Dim tempJ As Long
                    tempSchoolName = ""
                    For tempJ = 2 To lastRow
                        If ws.Cells(tempJ, 2).Value = examSite And ws.Cells(tempJ, 7).Value = examRoom Then
                            tempSchoolName = ws.Cells(tempJ, 1).Value ' 从A列获取学校名称
                            Exit For
                        End If
                    Next tempJ

                    ' 检查是否是新学校
                    currentSchoolName = tempSchoolName
                    If currentSchoolName <> previousSchoolName Then
                        previousSchoolName = currentSchoolName

                        ' 当学校变化时，如果不是第一个学校，则添加分节符
                        If k > 1 Then
                            ' 在新学校前添加分节符，确保页码重置
                            newSheet.HPageBreaks.Add Before:=newSheet.Rows(k)
                        End If

                        ' 设置页脚
                        With newSheet.PageSetup
                            ' 使用Excel内置页码代码：&P表示当前页码，&N表示总页数
                            .CenterFooter = currentSchoolName & " 第&P页 共&N页"
                        End With
                    End If

                    newSheet.Cells(k, 1).Value = tempSchoolName & " " & examSite & " 第" & CStr(examRoom) & "考场"
                    With newSheet.Range(newSheet.Cells(k, 1), newSheet.Cells(k, 17))
                        .Merge
                        .HorizontalAlignment = xlCenter
                        .Font.Size = 36
                    End With
                Else
                    MsgBox "k 的值超出范围: " & k
                    Exit Sub
                End If
                k = k + 1

                ' 设置表头行
                If k + 1 <= newSheet.Rows.Count Then
                    With newSheet.Range(newSheet.Cells(k + 1, 1), newSheet.Cells(k + 1, 17)).Font
    .Size = 14
End With
newSheet.Cells(k + 1, 1).Value = "座位"
                    newSheet.Cells(k + 1, 2).Value = "年级"
                    newSheet.Cells(k + 1, 3).Value = "班级"
                    newSheet.Cells(k + 1, 4).Value = "准考号"
                    newSheet.Cells(k + 1, 5).Value = "姓名"
                    newSheet.Cells(k + 1, 7).Value = "座位"
                    newSheet.Cells(k + 1, 8).Value = "年级"
                    newSheet.Cells(k + 1, 9).Value = "班级"
                    newSheet.Cells(k + 1, 10).Value = "准考号"
                    newSheet.Cells(k + 1, 11).Value = "姓名"
                    newSheet.Cells(k + 1, 13).Value = "座位"
                    newSheet.Cells(k + 1, 14).Value = "年级"
                    newSheet.Cells(k + 1, 15).Value = "班级"
                    newSheet.Cells(k + 1, 16).Value = "准考号"
                    newSheet.Cells(k + 1, 17).Value = "姓名"
                End If
                k = k + 1

                ' 设置列标题，增加到三组
                col = 1 ' 从第1列开始，增加左侧边距
                For j = 1 To 3
                    If k + 1 <= newSheet.Rows.Count Then
                        newSheet.Cells(k + 1, col).Value = "座位"
                        newSheet.Cells(k + 1, col + 1).Value = "年级"
                        newSheet.Cells(k + 1, col + 2).Value = "班级"
                        newSheet.Cells(k + 1, col + 3).Value = "准考号"
                        newSheet.Cells(k + 1, col + 4).Value = "姓名"
                        col = col + 6 ' 列索引增加 6，预留分隔线位置
                    End If
                Next j

                ' 填充本考场学生信息，增加到三组
                k = k + 1
                col = 1 ' 从第1列开始
                studentCount = 0
                For i = 2 To lastRow
                    If ws.Cells(i, 2).Value = examSite And ws.Cells(i, 7).Value = examRoom Then
                        If k <= newSheet.Rows.Count And col <= newSheet.Columns.Count Then
                            newSheet.Cells(k, col).Value = ws.Cells(i, 8).Value
                            newSheet.Cells(k, col + 1).Value = ws.Cells(i, 3).Value
                            newSheet.Cells(k, col + 2).Value = ws.Cells(i, 4).Value
                            newSheet.Cells(k, col + 3).Value = ws.Cells(i, 5).Value
                            newSheet.Cells(k, col + 4).Value = ws.Cells(i, 6).Value
                            ' 设置字体大小为14号
                            With newSheet.Range(newSheet.Cells(k, col), newSheet.Cells(k, col + 4)).Font
                                .Size = 14
                            End With
                            ' 添加虚线底边框
                            With newSheet.Range(newSheet.Cells(k, col), newSheet.Cells(k, col + 4)).Borders(xlEdgeBottom)
                                .LineStyle = xlDash
                                .Weight = xlThin
                                .ColorIndex = xlAutomatic
                            End With
                            studentCount = studentCount + 1
                            If studentCount Mod 3 = 0 Then
                                k = k + 1
                                col = 1 ' 重置列索引为第1列
                            Else
                                col = col + 6 ' 列索引增加 6，预留分隔线位置
                            End If
                        End If
                    End If
                Next i

                ' 在每个门贴之后插入分页符
                If k + 1 <= newSheet.Rows.Count Then
                    ' 添加水平分页符
                    newSheet.HPageBreaks.Add Before:=newSheet.Rows(k + 1)
                End If
                k = k + 1 ' 更新 k 的值以准备下一个门贴
            End If
        Next examRoomKey
    Next examSite

    ' 设置页面布局为A4横向
    With newSheet.PageSetup
        .Orientation = xlLandscape
        .PaperSize = xlPaperA4
        .LeftMargin = Application.InchesToPoints(0.5) ' 设置左侧边距为0.5英寸
        .RightMargin = Application.InchesToPoints(0.5) ' 设置右侧边距为0.5英寸
        .TopMargin = Application.CentimetersToPoints(1)
        .BottomMargin = Application.CentimetersToPoints(1)
        .CenterHorizontally = True
        .FooterMargin = Application.InchesToPoints(0.5)
        ' 确保页码按分页符递增
        .DifferentFirstPageHeaderFooter = False
        .ScaleWithDocHeaderFooter = True
        .AlignMarginsHeaderFooter = True
        ' 设置默认页脚，确保所有页面都有页码
        ' 注意：学校名称将在每个学校的第一页设置
        .CenterFooter = "第&P页 共&N页"
    End With



    ' DJP列宽设置为11字符
    colChars = Array("D", "J", "P")
    For Each colVariant In colChars
        newSheet.Columns(colVariant).ColumnWidth = 11
    Next colVariant

    ' EKQ列宽设置为10字符
    colChars = Array("E", "K", "Q")
    For Each colVariant In colChars
        newSheet.Columns(colVariant).ColumnWidth = 14
    Next colVariant

    colChars = Array("F", "L")
    For Each colVariant In colChars
        newSheet.Columns(colVariant).ColumnWidth = 2
    Next colVariant

    ' 预览打印
    ' 新增性能优化设置
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    Application.EnableEvents = False

    ' 优化数据读取：将数据一次性读入数组
    Dim dataArray As Variant
    dataArray = ws.Range("A2:I" & lastRow).Value

    ' 优化排序逻辑：改用数组排序替代工作表排序（此部分需要根据具体需求实现）...

    ' 优化字典填充：使用数组数据替代单元格访问
    For i = 1 To UBound(dataArray, 1)
        Dim schoolNameArray As String
        schoolNameArray = dataArray(i, 1)  ' A列数据（学校名称）
        examSite = Trim(dataArray(i, 2))  ' B列数据（校点）
        If examSite <> "" Then
            examRoom = dataArray(i, 7)    ' G列数据（考场）
            uniqueSites(examSite) = 1
            uniqueRooms(examSite & "-" & examRoom) = 1
        End If
    Next i
    ' 缩小座位、年级、班级所在单元格列宽
    ' ABCFGHKLM列宽设置为5.5字符
    colChars = Array("A", "B", "C", "G", "H", "I", "M", "N", "O")
    For Each colVariant In colChars
        newSheet.Columns(colVariant).ColumnWidth = 5.5
    Next colVariant
    ' 恢复性能设置
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Application.EnableEvents = True

    ' 设置所有单元格内容居中
    newSheet.Cells.HorizontalAlignment = xlCenter

    newSheet.PrintPreview
        Dim sheet As Variant
        For Each sheet In ThisWorkbook.Sheets
            If sheet.Name = "所有门贴" Then
                Application.DisplayAlerts = False
                sheet.Delete
                Application.DisplayAlerts = True
            End If
        Next sheet

End Sub