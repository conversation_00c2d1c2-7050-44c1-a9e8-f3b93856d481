Sub 校级成绩汇总统计()
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    
    Dim ws As Worksheet, resultWs As Worksheet
    Dim lastRow As <PERSON>, lastCol As Long
    Dim i <PERSON>, j <PERSON>, k <PERSON>
    Dim dataArr As Variant, resultArr As Variant
    Dim schoolDict As Object, gradeDict As Object
    Dim schoolArr() As String, gradeArr() As String
    Dim schoolCount As <PERSON>, gradeCount As Long
    Dim totalScoreCol As Long, chineseCol <PERSON>, mathCol <PERSON>, englishCol <PERSON> Long, scienceCol <PERSON> Long, moralCol As Long
    Dim gradeCol As <PERSON>, schoolCol As Long, nameCol As Long
    Dim resultRow As Long
    
    ' 创建字典对象用于收集唯一值
    Set schoolDict = CreateObject("Scripting.Dictionary")
    Set gradeDict = CreateObject("Scripting.Dictionary")
    
    ' 设置工作表
    Set ws = ThisWorkbook.Sheets("成绩汇总表")
    Set resultWs = Sheets("学校统计")
    
    ' 删除第5行以后的所有行
    If resultWs.Cells(resultWs.Rows.Count, 1).End(xlUp).Row > 4 Then
        resultWs.Rows("5:" & resultWs.Rows.Count).Delete
    End If
    
    ' 找到最后一行和最后一列
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    lastCol = ws.Cells(1, ws.Columns.Count).End(xlToLeft).Column
    
    ' 一次性读取所有数据到数组
    dataArr = ws.Range(ws.Cells(1, 1), ws.Cells(lastRow, lastCol)).Value
    
    ' 找到关键列的位置
    For i = 1 To lastCol
        Select Case dataArr(1, i)
            Case "年级": gradeCol = i
            Case "学校": schoolCol = i
            Case "姓名": nameCol = i
            Case "语文": chineseCol = i
            Case "数学": mathCol = i
            Case "英语": englishCol = i
            Case "科学": scienceCol = i
            Case "道德与法治": moralCol = i
            Case "总分": totalScoreCol = i
        End Select
    Next i
    
    ' 收集唯一的学校和年级
    For i = 2 To UBound(dataArr, 1)
        If Len(Trim(CStr(dataArr(i, schoolCol)))) > 0 Then
            schoolDict(dataArr(i, schoolCol)) = 1
        End If
        
        If Len(Trim(CStr(dataArr(i, gradeCol)))) > 0 Then
            gradeDict(dataArr(i, gradeCol)) = 1
        End If
    Next i
    
    ' 转换字典键为数组
    schoolCount = schoolDict.Count
    gradeCount = gradeDict.Count
    
    ReDim schoolArr(1 To schoolCount)
    ReDim gradeArr(1 To gradeCount)
    
    i = 1
    For Each school In schoolDict.Keys
        schoolArr(i) = school
        i = i + 1
    Next
    
    i = 1
    For Each grade In gradeDict.Keys
        gradeArr(i) = grade
        i = i + 1
    Next
    
    ' 排序年级数组
    Call QuickSortArray(gradeArr, 1, gradeCount)
    
    ' 预分配结果数组
    Dim maxResultRows As Long
    maxResultRows = gradeCount * (schoolCount + 2) ' 每个年级的学校数+全区+空行
    ReDim resultArr(1 To maxResultRows, 1 To 24)
    resultRow = 1
    
    ' 对每个年级进行统计
    For i = 1 To gradeCount
        Dim currentGrade As String
        currentGrade = gradeArr(i)
        
        ' 创建统计数组 - 修改数组结构以避免冲突
        Dim stats() As Double
        ReDim stats(1 To schoolCount + 1, 1 To 20) ' 增加数组大小以存储更多统计值
        
        ' 初始化统计数组
        For j = 1 To schoolCount + 1
            stats(j, 5) = 9999 ' 最低分初始化为很大的值
        Next j
        
        ' 一次性统计所有数据
        For j = 2 To UBound(dataArr, 1)
            If dataArr(j, gradeCol) = currentGrade Then
                ' 找到当前学校的索引
                Dim schoolIndex As Long
                schoolIndex = 0
                
                For k = 1 To schoolCount
                    If dataArr(j, schoolCol) = schoolArr(k) Then
                        schoolIndex = k
                        Exit For
                    End If
                Next k
                
                If schoolIndex > 0 Then
                    ' 处理总分
                    If Len(Trim(CStr(dataArr(j, totalScoreCol)))) > 0 And IsNumeric(dataArr(j, totalScoreCol)) Then
                        Dim score As Double
                        score = CDbl(dataArr(j, totalScoreCol))
                        
                        ' 学校统计
                        stats(schoolIndex, 1) = stats(schoolIndex, 1) + 1 ' 总人数
                        stats(schoolIndex, 2) = stats(schoolIndex, 2) + score ' 总分
                        
                        ' 全区统计
                        stats(schoolCount + 1, 1) = stats(schoolCount + 1, 1) + 1
                        stats(schoolCount + 1, 2) = stats(schoolCount + 1, 2) + score
                        
                        ' 优秀、合格、低分统计
                        If score >= 425 Then
                            stats(schoolIndex, 6) = stats(schoolIndex, 6) + 1 ' 优秀人数
                            stats(schoolCount + 1, 6) = stats(schoolCount + 1, 6) + 1
                        End If
                        
                        If score >= 300 Then
                            stats(schoolIndex, 7) = stats(schoolIndex, 7) + 1 ' 合格人数
                            stats(schoolCount + 1, 7) = stats(schoolCount + 1, 7) + 1
                        End If
                        
                        If score < 200 Then
                            stats(schoolIndex, 8) = stats(schoolIndex, 8) + 1 ' 低分人数
                            stats(schoolCount + 1, 8) = stats(schoolCount + 1, 8) + 1
                        End If
                        
                        ' 最高分和最低分
                        If score > stats(schoolIndex, 4) Then
                            stats(schoolIndex, 4) = score ' 最高分
                        End If
                        
                        If score < stats(schoolIndex, 5) Then
                            stats(schoolIndex, 5) = score ' 最低分
                        End If
                        
                        ' 全区最高分和最低分
                        If score > stats(schoolCount + 1, 4) Then
                            stats(schoolCount + 1, 4) = score
                        End If
                        
                        If score < stats(schoolCount + 1, 5) Then
                            stats(schoolCount + 1, 5) = score
                        End If
                    End If
                    
                    ' 处理各科目 - 修正科目统计逻辑
                    ' 语文
                    If Len(Trim(CStr(dataArr(j, chineseCol)))) > 0 And IsNumeric(dataArr(j, chineseCol)) Then
                        stats(schoolIndex, 9) = stats(schoolIndex, 9) + CDbl(dataArr(j, chineseCol)) ' 语文总分
                        stats(schoolIndex, 10) = stats(schoolIndex, 10) + 1 ' 语文人数
                        stats(schoolCount + 1, 9) = stats(schoolCount + 1, 9) + CDbl(dataArr(j, chineseCol))
                        stats(schoolCount + 1, 10) = stats(schoolCount + 1, 10) + 1
                    End If
                    
                    ' 数学
                    If Len(Trim(CStr(dataArr(j, mathCol)))) > 0 And IsNumeric(dataArr(j, mathCol)) Then
                        stats(schoolIndex, 11) = stats(schoolIndex, 11) + CDbl(dataArr(j, mathCol))
                        stats(schoolIndex, 12) = stats(schoolIndex, 12) + 1
                        stats(schoolCount + 1, 11) = stats(schoolCount + 1, 11) + CDbl(dataArr(j, mathCol))
                        stats(schoolCount + 1, 12) = stats(schoolCount + 1, 12) + 1
                    End If
                    
                    ' 英语
                    If Len(Trim(CStr(dataArr(j, englishCol)))) > 0 And IsNumeric(dataArr(j, englishCol)) Then
                        stats(schoolIndex, 13) = stats(schoolIndex, 13) + CDbl(dataArr(j, englishCol))
                        stats(schoolIndex, 14) = stats(schoolIndex, 14) + 1
                        stats(schoolCount + 1, 13) = stats(schoolCount + 1, 13) + CDbl(dataArr(j, englishCol))
                        stats(schoolCount + 1, 14) = stats(schoolCount + 1, 14) + 1
                    End If
                    
                    ' 科学 - 修正索引
                    If Len(Trim(CStr(dataArr(j, scienceCol)))) > 0 And IsNumeric(dataArr(j, scienceCol)) Then
                        stats(schoolIndex, 15) = stats(schoolIndex, 15) + CDbl(dataArr(j, scienceCol))
                        stats(schoolIndex, 16) = stats(schoolIndex, 16) + 1 ' 科学人数
                        stats(schoolCount + 1, 15) = stats(schoolCount + 1, 15) + CDbl(dataArr(j, scienceCol))
                        stats(schoolCount + 1, 16) = stats(schoolCount + 1, 16) + 1
                    End If
                    
                    ' 道德与法治 - 修正索引
                    If Len(Trim(CStr(dataArr(j, moralCol)))) > 0 And IsNumeric(dataArr(j, moralCol)) Then
                        stats(schoolIndex, 17) = stats(schoolIndex, 17) + CDbl(dataArr(j, moralCol))
                        stats(schoolIndex, 18) = stats(schoolIndex, 18) + 1
                        stats(schoolCount + 1, 17) = stats(schoolCount + 1, 17) + CDbl(dataArr(j, moralCol))
                        stats(schoolCount + 1, 18) = stats(schoolCount + 1, 18) + 1
                    End If
                End If
            End If
        Next j
        
        ' 计算平均分和排名
        Dim avgScores() As Double, ranks() As Long
        ReDim avgScores(1 To schoolCount, 1 To 6) ' 总分、语文、数学、英语、科学、道法
        ReDim ranks(1 To schoolCount, 1 To 6)
        
        ' 计算平均分
        For j = 1 To schoolCount
            ' 总分平均分
            If stats(j, 1) > 0 Then
                avgScores(j, 1) = stats(j, 2) / stats(j, 1)
            End If
            
            ' 语文平均分
            If stats(j, 10) > 0 Then
                avgScores(j, 2) = stats(j, 9) / stats(j, 10)
            End If
            
            ' 数学平均分
            If stats(j, 12) > 0 Then
                avgScores(j, 3) = stats(j, 11) / stats(j, 12)
            End If
            
            ' 英语平均分
            If stats(j, 14) > 0 Then
                avgScores(j, 4) = stats(j, 13) / stats(j, 14)
            End If
            
            ' 科学平均分 - 修正索引
            If stats(j, 16) > 0 Then
                avgScores(j, 5) = stats(j, 15) / stats(j, 16)
            End If
            
            ' 道法平均分' 道法平均分 - 修正索引
            If stats(j, 18) > 0 Then
                avgScores(j, 6) = stats(j, 17) / stats(j, 18)
            End If
        Next j
        
        ' 计算排名
        For j = 1 To schoolCount
            If schoolArr(j) <> "育英学校" And stats(j, 1) > 0 Then
                ' 总分排名
                ranks(j, 1) = 1
                For k = 1 To schoolCount
                    If k <> j And schoolArr(k) <> "育英学校" And stats(k, 1) > 0 And avgScores(k, 1) > avgScores(j, 1) Then
                        ranks(j, 1) = ranks(j, 1) + 1
                    End If
                Next k
                
                ' 语文排名
                ranks(j, 2) = 1
                For k = 1 To schoolCount
                    If k <> j And schoolArr(k) <> "育英学校" And stats(k, 1) > 0 And avgScores(k, 2) > avgScores(j, 2) Then
                        ranks(j, 2) = ranks(j, 2) + 1
                    End If
                Next k
                
                ' 数学排名
                ranks(j, 3) = 1
                For k = 1 To schoolCount
                    If k <> j And schoolArr(k) <> "育英学校" And stats(k, 1) > 0 And avgScores(k, 3) > avgScores(j, 3) Then
                        ranks(j, 3) = ranks(j, 3) + 1
                    End If
                Next k
                
                ' 英语排名
                ranks(j, 4) = 1
                For k = 1 To schoolCount
                    If k <> j And schoolArr(k) <> "育英学校" And stats(k, 1) > 0 And avgScores(k, 4) > avgScores(j, 4) Then
                        ranks(j, 4) = ranks(j, 4) + 1
                    End If
                Next k
                
                ' 科学排名
                ranks(j, 5) = 1
                For k = 1 To schoolCount
                    If k <> j And schoolArr(k) <> "育英学校" And stats(k, 1) > 0 And avgScores(k, 5) > avgScores(j, 5) Then
                        ranks(j, 5) = ranks(j, 5) + 1
                    End If
                Next k
                
                ' 道法排名
                ranks(j, 6) = 1
                For k = 1 To schoolCount
                    If k <> j And schoolArr(k) <> "育英学校" And stats(k, 1) > 0 And avgScores(k, 6) > avgScores(j, 6) Then
                        ranks(j, 6) = ranks(j, 6) + 1
                    End If
                Next k
            End If
        Next j
        
        ' 填充结果数组
        For j = 1 To schoolCount
            ' 年级
            resultArr(resultRow, 1) = currentGrade
            ' 学校
            resultArr(resultRow, 2) = schoolArr(j)
            ' 参考人数
            resultArr(resultRow, 3) = stats(j, 1)
            ' 总分平均分
            resultArr(resultRow, 4) = Round(avgScores(j, 1), 2)
            
            ' 总分排名
            If schoolArr(j) <> "育英学校" And stats(j, 1) > 0 Then
                resultArr(resultRow, 5) = ranks(j, 1)
            Else
                resultArr(resultRow, 5) = "-"
            End If
            
            ' 总分优秀人数
            resultArr(resultRow, 6) = stats(j, 6)
            
            ' 总分优秀比例%
            If stats(j, 1) > 0 Then
                resultArr(resultRow, 7) = Round(stats(j, 6) / stats(j, 1) * 100, 2)
            Else
                resultArr(resultRow, 7) = 0
            End If
            
            ' 总分合格人数
            resultArr(resultRow, 8) = stats(j, 7)
            
            ' 总分合格比例%
            If stats(j, 1) > 0 Then
                resultArr(resultRow, 9) = Round(stats(j, 7) / stats(j, 1) * 100, 2)
            Else
                resultArr(resultRow, 9) = 0
            End If
            
            ' 总分低分人数
            resultArr(resultRow, 10) = stats(j, 8)
            
            ' 总分低分比例%
            If stats(j, 1) > 0 Then
                resultArr(resultRow, 11) = Round(stats(j, 8) / stats(j, 1) * 100, 2)
            Else
                resultArr(resultRow, 11) = 0
            End If
            
            ' 总分最高分
            resultArr(resultRow, 12) = stats(j, 4)
            
            ' 总分最低分
            If stats(j, 5) = 9999 Then
                resultArr(resultRow, 13) = 0
            Else
                resultArr(resultRow, 13) = stats(j, 5)
            End If
            
            ' 各科平均分
            resultArr(resultRow, 14) = Round((avgScores(j, 2) + avgScores(j, 3) + avgScores(j, 4) + avgScores(j, 5) + avgScores(j, 6)) / 5, 2)
            
            ' 语文平均分
            resultArr(resultRow, 15) = Round(avgScores(j, 2), 2)
            
            ' 语文平均分排名
            If schoolArr(j) <> "育英学校" And stats(j, 1) > 0 Then
                resultArr(resultRow, 16) = ranks(j, 2)
            Else
                resultArr(resultRow, 16) = "-"
            End If
            
            ' 数学平均分
            resultArr(resultRow, 17) = Round(avgScores(j, 3), 2)
            
            ' 数学平均分排名
            If schoolArr(j) <> "育英学校" And stats(j, 1) > 0 Then
                resultArr(resultRow, 18) = ranks(j, 3)
            Else
                resultArr(resultRow, 18) = "-"
            End If
            
            ' 英语平均分
            resultArr(resultRow, 19) = Round(avgScores(j, 4), 2)
            
            ' 英语平均分排名
            If schoolArr(j) <> "育英学校" And stats(j, 1) > 0 Then
                resultArr(resultRow, 20) = ranks(j, 4)
            Else
                resultArr(resultRow, 20) = "-"
            End If
            
            ' 科学平均分
            resultArr(resultRow, 21) = Round(avgScores(j, 5), 2)
            
            ' 科学平均分排名
            If schoolArr(j) <> "育英学校" And stats(j, 1) > 0 Then
                resultArr(resultRow, 22) = ranks(j, 5)
            Else
                resultArr(resultRow, 22) = "-"
            End If
            
            ' 道法平均分
            resultArr(resultRow, 23) = Round(avgScores(j, 6), 2)
            
            ' 道法平均分排名
            If schoolArr(j) <> "育英学校" And stats(j, 1) > 0 Then
                resultArr(resultRow, 24) = ranks(j, 6)
            Else
                resultArr(resultRow, 24) = "-"
            End If
            
            resultRow = resultRow + 1
        Next j
        
        ' 添加全区统计行
        ' 年级
        resultArr(resultRow, 1) = currentGrade
        ' 学校
        resultArr(resultRow, 2) = "全区"
        ' 参考人数
        resultArr(resultRow, 3) = stats(schoolCount + 1, 1)
        
        ' 全区总分平均分
        If stats(schoolCount + 1, 1) > 0 Then
            resultArr(resultRow, 4) = Round(stats(schoolCount + 1, 2) / stats(schoolCount + 1, 1), 2)
        Else
            resultArr(resultRow, 4) = 0
        End If
        
        ' 全区不参与排名
        resultArr(resultRow, 5) = "-"
        
        ' 总分优秀人数
        resultArr(resultRow, 6) = stats(schoolCount + 1, 6)
        
        ' 总分优秀比例%
        If stats(schoolCount + 1, 1) > 0 Then
            resultArr(resultRow, 7) = Round(stats(schoolCount + 1, 6) / stats(schoolCount + 1, 1) * 100, 2)
        Else
            resultArr(resultRow, 7) = 0
        End If
        
        ' 总分合格人数
        resultArr(resultRow, 8) = stats(schoolCount + 1, 7)
        
        ' 总分合格比例%
        If stats(schoolCount + 1, 1) > 0 Then
            resultArr(resultRow, 9) = Round(stats(schoolCount + 1, 7) / stats(schoolCount + 1, 1) * 100, 2)
        Else
            resultArr(resultRow, 9) = 0
        End If
        
        ' 总分低分人数
        resultArr(resultRow, 10) = stats(schoolCount + 1, 8)
        
        ' 总分低分比例%
        If stats(schoolCount + 1, 1) > 0 Then
            resultArr(resultRow, 11) = Round(stats(schoolCount + 1, 8) / stats(schoolCount + 1, 1) * 100, 2)
        Else
            resultArr(resultRow, 11) = 0
        End If
        
        ' 总分最高分
        resultArr(resultRow, 12) = stats(schoolCount + 1, 4)
        
        ' 总分最低分
        If stats(schoolCount + 1, 5) = 9999 Then
            resultArr(resultRow, 13) = 0
        Else
            resultArr(resultRow, 13) = stats(schoolCount + 1, 5)
        End If
        
        ' 全区各科平均分 - 修正索引
        Dim avgChinese As Double, avgMath As Double, avgEnglish As Double, avgScience As Double, avgMoral As Double
        
        If stats(schoolCount + 1, 10) > 0 Then
            avgChinese = stats(schoolCount + 1, 9) / stats(schoolCount + 1, 10)
        End If
        
        If stats(schoolCount + 1, 12) > 0 Then
            avgMath = stats(schoolCount + 1, 11) / stats(schoolCount + 1, 12)
        End If
        
        If stats(schoolCount + 1, 14) > 0 Then
            avgEnglish = stats(schoolCount + 1, 13) / stats(schoolCount + 1, 14)
        End If
        
        If stats(schoolCount + 1, 16) > 0 Then
            avgScience = stats(schoolCount + 1, 15) / stats(schoolCount + 1, 16)
        End If
        
        If stats(schoolCount + 1, 18) > 0 Then
            avgMoral = stats(schoolCount + 1, 17) / stats(schoolCount + 1, 18)
        End If
        
        resultArr(resultRow, 14) = Round((avgChinese + avgMath + avgEnglish + avgScience + avgMoral) / 5, 2)
        resultArr(resultRow, 15) = Round(avgChinese, 2)
        resultArr(resultRow, 16) = "-" ' 全区不参与排名
        resultArr(resultRow, 17) = Round(avgMath, 2)
        resultArr(resultRow, 18) = "-"
        resultArr(resultRow, 19) = Round(avgEnglish, 2)
        resultArr(resultRow, 20) = "-"
        resultArr(resultRow, 21) = Round(avgScience, 2)
        resultArr(resultRow, 22) = "-"
        resultArr(resultRow, 23) = Round(avgMoral, 2)
        resultArr(resultRow, 24) = "-"
        
        resultRow = resultRow + 1
        
        ' 添加空行
        resultRow = resultRow + 1
    Next i
    
    ' 一次性写入结果
    resultWs.Range("A5").Resize(resultRow - 1, 24).Value = resultArr
    
    ' 格式化结果
    resultWs.Range("G5:G" & resultRow + 4).NumberFormat = "0.00"
    resultWs.Range("I5:I" & resultRow + 4).NumberFormat = "0.00"
    resultWs.Range("K5:K" & resultRow + 4).NumberFormat = "0.00"
    
    ' 调用自定义排序
    Call 自定义排序
    
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    
    MsgBox "统计完成！", vbInformation
End Sub

Sub 自定义排序()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim dataRange As Range
    Dim i As Long, j As Long, k As Long
    
    ' 设置工作表
    Set ws = ThisWorkbook.Sheets("学校统计")
    
    ' 找到最后一行
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    
    ' 如果没有数据，则退出
    If lastRow < 6 Then
        MsgBox "没有找到数据！", vbExclamation
        Exit Sub
    End If
    
    ' 设置数据范围
    Set dataRange = ws.Range("A5:X" & lastRow)

    ' WPS兼容的边框设置方式
    dataRange.Borders(xlInsideHorizontal).LineStyle = xlContinuous
    dataRange.Borders(xlInsideHorizontal).Weight = xlThin
    dataRange.Borders(xlInsideVertical).LineStyle = xlContinuous
    dataRange.Borders(xlInsideVertical).Weight = xlThin
    
    dataRange.Borders(xlEdgeLeft).LineStyle = xlContinuous
    dataRange.Borders(xlEdgeLeft).Weight = xlMedium
    dataRange.Borders(xlEdgeTop).LineStyle = xlContinuous
    dataRange.Borders(xlEdgeTop).Weight = xlMedium
    dataRange.Borders(xlEdgeBottom).LineStyle = xlContinuous
    dataRange.Borders(xlEdgeBottom).Weight = xlMedium
    dataRange.Borders(xlEdgeRight).LineStyle = xlContinuous
    dataRange.Borders(xlEdgeRight).Weight = xlMedium
        ' 设置区域内文字居中
    dataRange.HorizontalAlignment = xlCenter
    dataRange.VerticalAlignment = xlCenter
    
    ' 定义年级顺序
    Dim gradeOrder As Object
    Set gradeOrder = CreateObject("Scripting.Dictionary")
    gradeOrder("一") = 1
    gradeOrder("二") = 2
    gradeOrder("三") = 3
    gradeOrder("四") = 4
    gradeOrder("五") = 5
    gradeOrder("六") = 6
    
    ' 定义学校顺序
    Dim schoolOrder As Object
    Set schoolOrder = CreateObject("Scripting.Dictionary")
    schoolOrder("全区") = 1
    schoolOrder("思茅一小") = 2
    schoolOrder("思茅二小") = 3
    schoolOrder("思茅三小") = 4
    schoolOrder("思茅四小") = 5
    schoolOrder("思茅五小") = 6
    schoolOrder("思茅六小") = 7
    schoolOrder("思茅六中") = 8
    schoolOrder("思茅七小") = 9
    schoolOrder("倚象小学") = 10
    schoolOrder("云仙小学") = 11
    schoolOrder("龙潭小学") = 12
    schoolOrder("思茅港小") = 13
    schoolOrder("六顺小学") = 14
    schoolOrder("传薪校区") = 15
    schoolOrder("博雅公学") = 16
    schoolOrder("育英学校") = 17
    
    ' 读取数据到二维数组
    Dim data() As Variant
    ReDim data(1 To lastRow - 4, 1 To 26) ' 24列数据 + 2列排序值
    
    ' 读取数据并添加排序值
    For i = 5 To lastRow
        For j = 1 To 24
            data(i - 4, j) = ws.Cells(i, j).Value
        Next j
        
        ' 添加年级排序值
        If gradeOrder.Exists(CStr(ws.Cells(i, 1).Value)) Then
            data(i - 4, 25) = gradeOrder(CStr(ws.Cells(i, 1).Value))
        Else
            data(i - 4, 25) = 999 ' 未知年级放最后
        End If
        
        ' 添加学校排序值
        If schoolOrder.Exists(CStr(ws.Cells(i, 2).Value)) Then
            data(i - 4, 26) = schoolOrder(CStr(ws.Cells(i, 2).Value))
        Else
            data(i - 4, 26) = 999 ' 未知学校放最后
        End If
    Next i
    
    ' 使用改进的冒泡排序 - 确保次关键字排序正确生效
    Dim swapped As Boolean
    Dim temp As Variant
    
    For i = 1 To UBound(data) - 1
        swapped = False
        
        For j = 1 To UBound(data) - i
            ' 比较主键（年级）
            If data(j, 25) > data(j + 1, 25) Then
                ' 交换整行
                For k = 1 To 26
                    temp = data(j, k)
                    data(j, k) = data(j + 1, k)
                    data(j + 1, k) = temp
                Next k
                swapped = True
            ' 如果主键相同，比较次键（学校）
            ElseIf data(j, 25) = data(j + 1, 25) And data(j, 26) > data(j + 1, 26) Then
                ' 交换整行
                For k = 1 To 26
                    temp = data(j, k)
                    data(j, k) = data(j + 1, k)
                    data(j + 1, k) = temp
                Next k
                swapped = True
            End If
        Next j
        
        ' 如果没有交换，说明已经排好序
        If Not swapped Then Exit For
    Next i
    
    ' 将排序后的数据写回工作表
    For i = 1 To UBound(data)
        For j = 1 To 24
            ws.Cells(i + 4, j).Value = data(i, j)
        Next j
    Next i
    
    ' 重新设置全区行的格式
    For i = 5 To lastRow
        If ws.Cells(i, 2).Value = "全区" Then
            ws.Range(ws.Cells(i, 1), ws.Cells(i, 24)).Interior.Color = RGB(255, 255, 0)
            ws.Range(ws.Cells(i, 1), ws.Cells(i, 24)).Font.Bold = True
        End If
    Next i
End Sub

' 快速排序算法用于排序数组
Sub QuickSortArray(ByRef arr() As String, ByVal first As Long, ByVal last As Long)
    Dim vPivot As Variant
    Dim vTemp As Variant
    Dim low As Long
    Dim high As Long
    
    If first >= last Then Exit Sub
    
    low = first
    high = last
    vPivot = arr((first + last) \ 2)
    
    Do While low <= high
        Do While arr(low) < vPivot And low < last
            low = low + 1
        Loop
        
        Do While vPivot < arr(high) And high > first
            high = high - 1
        Loop
        
        If low <= high Then
            vTemp = arr(low)
            arr(low) = arr(high)
            arr(high) = vTemp
            low = low + 1
            high = high - 1
        End If
    Loop
    
    If first < high Then QuickSortArray arr, first, high
    If low < last Then QuickSortArray arr, low, last
End Sub

 
