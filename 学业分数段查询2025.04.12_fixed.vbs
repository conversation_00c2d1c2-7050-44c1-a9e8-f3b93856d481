'------------------------------------------------------------------------------
' 模块：学业分数段统计
' 功能：根据"成绩总表"中的数据，按照年级筛选，统计各班级、各学校和全区的学业分数段情况
' 使用数组和字典进行高效处理，并按照要求格式化输出
'------------------------------------------------------------------------------
Sub 学业分数段统计()
    ' 声明变量
    Dim ws成绩总表 As Worksheet, ws学业分段统计 As Worksheet
    Dim 数据数组 As Variant
    Dim 最后行 As Long, i <PERSON>, j <PERSON> Long
    Dim 年级 As String
    Dim 学校序号 As String, 学校 As String, 班级 As String
    Dim 折分 As Double, 总分 As Double
    Dim 班级字典 As Object, 学校字典 As Object, 学校班级字典 As Object
    Dim 当前行 As Long
    Dim 班级Key As Variant
    Dim 分数段边界 As Variant
    Dim 班级统计数组() As Variant, 学校统计数组() As Variant, 全区统计数组 As Variant
    Dim 班级平均分字典 As Object, 学校平均分字典 As Object
    Dim 班级排名数组() As Variant, 学校排名数组() As Variant
    Dim 班级数量 As Long, 学校数量 As Long
    Dim 当前学校 As String, 上一学校 As String
    Dim 学校开始行 As Long, 学校结束行 As Long
    Dim 信息分 As Double, 信息加分 As Double

    ' 设置工作表引用
    Set ws成绩总表 = ThisWorkbook.Worksheets("成绩总表")
    Set ws学业分段统计 = ThisWorkbook.Worksheets("学业分段统计")

    ' 关闭屏幕更新和自动计算以提高性能
    With Application
        .ScreenUpdating = False
        .Calculation = xlCalculationManual
        .EnableEvents = False
    End With

    ' 获取年级值（从D2单元格）
    年级 = ws学业分段统计.Range("D2").value
    If 年级 = "" Then
        MsgBox "请在D2单元格输入要统计的年级！", vbExclamation
        GoTo CleanExit
    End If

    ' 获取成绩总表的最后一行
    最后行 = ws成绩总表.Cells(ws成绩总表.Rows.Count, 1).End(xlUp).Row
    If 最后行 < 2 Then
        MsgBox "成绩总表中没有数据！", vbExclamation
        GoTo CleanExit
    End If

    ' 先对成绩总表进行排序（按年级、学校序号、学校名称、班级排序）
    With ws成绩总表.Sort
        .SortFields.Clear
        .SortFields.Add key:=ws成绩总表.Range("A2:A" & 最后行), SortOn:=xlSortOnValues, Order:=xlAscending ' 年级
        .SortFields.Add key:=ws成绩总表.Range("B2:B" & 最后行), SortOn:=xlSortOnValues, Order:=xlAscending ' 学校序号
        .SortFields.Add key:=ws成绩总表.Range("C2:C" & 最后行), SortOn:=xlSortOnValues, Order:=xlAscending ' 学校
        .SortFields.Add key:=ws成绩总表.Range("D2:D" & 最后行), SortOn:=xlSortOnValues, Order:=xlAscending ' 班级
        .SetRange ws成绩总表.Range("A1:T" & 最后行) ' 包含标题行
        .Header = xlYes
        .MatchCase = False
        .Orientation = xlTopToBottom
        .Apply
    End With

    ' 将成绩总表数据读入数组以提高性能
    数据数组 = ws成绩总表.Range("A2:T" & 最后行).value

    ' 定义分数段边界
    分数段边界 = Array(0, 300, 320, 340, 360, 380, 400, 420, 440, 460, 480, 500, 520, 9999)

    ' 创建字典对象
    Set 班级字典 = CreateObject("Scripting.Dictionary")
    Set 学校字典 = CreateObject("Scripting.Dictionary")
    Set 学校班级字典 = CreateObject("Scripting.Dictionary")
    Set 班级平均分字典 = CreateObject("Scripting.Dictionary")
    Set 学校平均分字典 = CreateObject("Scripting.Dictionary")

    ' 初始化全区统计数组
    ReDim 全区统计数组(1 To 15)
    For i = 1 To 15
        全区统计数组(i) = 0
    Next i

    ' 第一步：收集所有学校和班级信息
    For i = 1 To UBound(数据数组, 1)
        If 数据数组(i, 1) = 年级 Then
            学校序号 = 数据数组(i, 2)
            学校 = 数据数组(i, 3)
            班级 = 数据数组(i, 4)

            ' 将学校添加到学校字典
            If Not 学校字典.Exists(学校) Then
                学校字典.Add 学校, 学校序号
            End If

            ' 将班级添加到学校班级字典
            If Not 学校班级字典.Exists(学校) Then
                学校班级字典.Add 学校, CreateObject("Scripting.Dictionary")
            End If

            If Not 学校班级字典(学校).Exists(班级) Then
                学校班级字典(学校).Add 班级, True
            End If
        End If
    Next i

    ' 如果没有数据，则退出
    If 学校字典.Count = 0 Then
        MsgBox "没有找到" & 年级 & "年级的数据！", vbExclamation
        GoTo CleanExit
    End If

    ' 清除学业分段统计表中的数据（保留前4行）
    ws学业分段统计.Rows("5:" & ws学业分段统计.Rows.Count).Delete

    ' 初始化班级统计数组
    班级数量 = 0
    Dim 学校Key As Variant
    For Each 学校Key In 学校班级字典.Keys
        班级数量 = 班级数量 + 学校班级字典(学校Key).Count
    Next 学校Key

    ReDim 班级统计数组(1 To 班级数量, 1 To 21)
    ReDim 班级排名数组(1 To 班级数量, 1 To 3)

    ' 初始化学校统计数组
    学校数量 = 学校字典.Count
    ReDim 学校统计数组(1 To 学校数量, 1 To 21)
    ReDim 学校排名数组(1 To 学校数量, 1 To 3)

    ' 第二步：统计各班级的分数段
    当前行 = 5 ' 从第5行开始填充数据
    Dim 班级计数 As Long
    班级计数 = 0
    Dim 学校计数 As Long
    学校计数 = 0

    ' 遍历每个学校
    For Each 学校Key In 学校班级字典.Keys
        学校计数 = 学校计数 + 1
        学校开始行 = 当前行
        上一学校 = 学校Key

        ' 遍历该学校的每个班级
        For Each 班级Key In 学校班级字典(学校Key).Keys
            班级计数 = 班级计数 + 1

            ' 初始化班级统计数据
            For j = 1 To 21
                班级统计数组(班级计数, j) = 0
            Next j

            ' 填充学校序号、学校名称和班级
            班级统计数组(班级计数, 1) = 学校字典(学校Key) ' 学校序号
            班级统计数组(班级计数, 2) = 学校Key ' 学校名称
            班级统计数组(班级计数, 3) = 班级Key ' 班级

            ' 遍历成绩总表，统计当前班级的分数段
            For i = 1 To UBound(数据数组, 1)
                If 数据数组(i, 1) = 年级 And _
                   数据数组(i, 3) = 学校Key And _
                   数据数组(i, 4) = 班级Key Then

                    ' 实有人数加1（有姓名的学生）
                    If 数据数组(i, 6) <> "" Then
                        班级统计数组(班级计数, 4) = 班级统计数组(班级计数, 4) + 1
                    End If

                    ' 获取折分和总分
                    折分 = Val(数据数组(i, 18))
                    总分 = Val(数据数组(i, 17))

                    ' 获取信息分，如果及格（>=60）则加10分
                    信息分 = Val(数据数组(i, 13))
                    信息加分 = IIf(信息分 >= 60, 10, 0)

                    ' 将信息加分添加到折分中，用于分数段统计
                    折分 = 折分 + 信息加分

                    ' 如果有折分，则统计分数段
                    If 折分 > 0 Then
                        ' 参考人数加1
                        班级统计数组(班级计数, 5) = 班级统计数组(班级计数, 5) + 1

                        ' 确定分数段并计数 - 使用折分而不是总分
                        For j = 1 To UBound(分数段边界) - 1
                            If 折分 >= 分数段边界(j - 1) And 折分 < 分数段边界(j) Then
                                班级统计数组(班级计数, j + 5) = 班级统计数组(班级计数, j + 5) + 1
                                Exit For
                            End If
                        Next j

                        ' 累加折分，用于计算平均分（折分已包含信息加分）
                        班级统计数组(班级计数, 19) = 班级统计数组(班级计数, 19) + 折分
                    End If
                End If
            Next i

            ' 计算平均分
            If 班级统计数组(班级计数, 5) > 0 Then
                班级统计数组(班级计数, 19) = Round(班级统计数组(班级计数, 19) / 班级统计数组(班级计数, 5), 2)
            Else
                班级统计数组(班级计数, 19) = 0
            End If

            ' 存储班级平均分，用于后续排名
            班级排名数组(班级计数, 1) = 班级统计数组(班级计数, 19) ' 平均分
            班级排名数组(班级计数, 2) = 班级计数 ' 班级索引
            班级排名数组(班级计数, 3) = 学校计数 ' 学校索引

            ' 累加到学校统计
            For j = 4 To 18
                学校统计数组(学校计数, j) = 学校统计数组(学校计数, j) + 班级统计数组(班级计数, j)
            Next j
            ' 累加学校总分（班级平均分 * 参考人数）
            学校统计数组(学校计数, 19) = 学校统计数组(学校计数, 19) + (班级统计数组(班级计数, 19) * 班级统计数组(班级计数, 5))

            ' 填充到学业分段统计表
            ws学业分段统计.Cells(当前行, 1).value = 班级统计数组(班级计数, 1) ' 学校序号
            ws学业分段统计.Cells(当前行, 2).value = 班级统计数组(班级计数, 2) ' 学校名称
            ws学业分段统计.Cells(当前行, 3).value = 班级统计数组(班级计数, 3) ' 班级

            For j = 4 To 18
                ws学业分段统计.Cells(当前行, j).value = 班级统计数组(班级计数, j)
            Next j

            ws学业分段统计.Cells(当前行, 19).value = 班级统计数组(班级计数, 19) ' 平均分

            ' 移动到下一行
            当前行 = 当前行 + 1
        Next 班级Key

        ' 学校统计行
        学校结束行 = 当前行 - 1

        ' 填充学校序号和学校名称
        学校统计数组(学校计数, 1) = 学校字典(学校Key) ' 学校序号
        学校统计数组(学校计数, 2) = 学校Key ' 学校名称
        学校统计数组(学校计数, 3) = 学校Key & "汇总" ' 汇总标识

        ' 计算学校平均分
        If 学校统计数组(学校计数, 5) > 0 Then
            学校统计数组(学校计数, 19) = Round(学校统计数组(学校计数, 19) / 学校统计数组(学校计数, 5), 2)
        Else
            学校统计数组(学校计数, 19) = 0
        End If

        ' 存储学校平均分，用于后续排名
        学校排名数组(学校计数, 1) = 学校统计数组(学校计数, 19) ' 平均分
        学校排名数组(学校计数, 2) = 学校计数 ' 学校索引

        ' 填充到学业分段统计表
        ws学业分段统计.Cells(当前行, 1).value = 学校统计数组(学校计数, 1) ' 学校序号
        ws学业分段统计.Cells(当前行, 2).value = 学校统计数组(学校计数, 2) ' 学校名称

        For j = 4 To 19
            ws学业分段统计.Cells(当前行, j).value = 学校统计数组(学校计数, j)
        Next j

        ' 合并单元格B和C
        ws学业分段统计.Range(ws学业分段统计.Cells(当前行, 2), ws学业分段统计.Cells(当前行, 3)).Merge
        ws学业分段统计.Cells(当前行, 2).value = 学校Key & "汇总"

        ' 设置汇总行的格式
        With ws学业分段统计.Range(ws学业分段统计.Cells(当前行, 1), ws学业分段统计.Cells(当前行, 20))
            .Interior.Color = RGB(255, 255, 0) ' 黄色背景
            .Font.Bold = True ' 加粗
            .Font.Color = RGB(255, 0, 0) ' 红色字体
        End With

        ' 累加到全区统计
        For j = 4 To 18
            全区统计数组(j - 3) = 全区统计数组(j - 3) + 学校统计数组(学校计数, j)
        Next j

        ' 移动到下一行
        当前行 = 当前行 + 1
    Next 学校Key

    ' 全区统计行
    ' 计算全区平均分
    Dim 全区总分 As Double, 全区参考人数 As Long
    全区参考人数 = 全区统计数组(2) ' 参考人数

    ' 重新计算全区总分（使用班级平均分和参考人数）
    全区总分 = 0
    For i = 1 To 班级计数
        ' 累加班级总分（班级平均分 * 参考人数）
        全区总分 = 全区总分 + (班级统计数组(i, 19) * 班级统计数组(i, 5))
    Next i

    Dim 全区平均分 As Double
    If 全区参考人数 > 0 Then
        全区平均分 = Round(全区总分 / 全区参考人数, 2)
    Else
        全区平均分 = 0
    End If

    ' 填充到学业分段统计表
    ws学业分段统计.Cells(当前行, 1).value = "" ' 学校序号留空

    ' 合并单元格B和C
    ws学业分段统计.Range(ws学业分段统计.Cells(当前行, 2), ws学业分段统计.Cells(当前行, 3)).Merge
    ws学业分段统计.Cells(当前行, 2).value = "全区汇总"

    ' 填充统计数据
    ws学业分段统计.Cells(当前行, 4).value = 全区统计数组(1) ' 实有人数
    ws学业分段统计.Cells(当前行, 5).value = 全区统计数组(2) ' 参考人数

    For j = 6 To 18
        ws学业分段统计.Cells(当前行, j).value = 全区统计数组(j - 3)
    Next j

    ws学业分段统计.Cells(当前行, 19).value = 全区平均分 ' 平均分

    ' 设置全区汇总行的格式
    With ws学业分段统计.Range(ws学业分段统计.Cells(当前行, 1), ws学业分段统计.Cells(当前行, 20))
        .Interior.Color = RGB(255, 255, 0) ' 黄色背景
        .Font.Bold = True ' 加粗
        .Font.Color = RGB(255, 0, 0) ' 红色字体
    End With

    ' 第三步：计算排名
    ' 班级排名（按平均分降序排序）
    Call 冒泡排序(班级排名数组, 班级计数)

    ' 学校排名（按平均分降序排序）
    Call 冒泡排序(学校排名数组, 学校计数)

    ' 填充班级排名
    For i = 1 To 班级计数
        Dim 班级索引 As Long
        班级索引 = 班级排名数组(i, 2)
        Dim 班级行号 As Long
        班级行号 = 4 + 班级索引

        ' 查找对应的行
        For j = 5 To 当前行 - 1
            If ws学业分段统计.Cells(j, 1).value = 班级统计数组(班级索引, 1) And _
               ws学业分段统计.Cells(j, 2).value = 班级统计数组(班级索引, 2) And _
               ws学业分段统计.Cells(j, 3).value = 班级统计数组(班级索引, 3) Then
                ws学业分段统计.Cells(j, 20).value = i
                Exit For
            End If
        Next j
    Next i

    ' 填充学校排名
    For i = 1 To 学校计数
        Dim 学校索引 As Long
        学校索引 = 学校排名数组(i, 2)

        ' 查找对应的行
        For j = 5 To 当前行 - 1
            If Not ws学业分段统计.Cells(j, 3).MergeCells And _
               ws学业分段统计.Cells(j, 1).value = 学校统计数组(学校索引, 1) And _
               ws学业分段统计.Cells(j, 2).value = 学校统计数组(学校索引, 2) And _
               InStr(ws学业分段统计.Cells(j, 2).value & ws学业分段统计.Cells(j, 3).value, "汇总") > 0 Then
                ws学业分段统计.Cells(j, 20).value = i
                Exit For
            End If
        Next j
    Next i

    ' 第四步：设置单元格格式
    ' 设置数据区域的格式
    With ws学业分段统计.Range(ws学业分段统计.Cells(5, 1), ws学业分段统计.Cells(当前行, 20))
        .HorizontalAlignment = xlCenter ' 水平居中
        .VerticalAlignment = xlCenter ' 垂直居中
        .Borders.LineStyle = xlContinuous ' 添加边框
        .Borders.Weight = xlThin ' 细边框
    End With

    ' 设置A-T列的背景色
    ws学业分段统计.Range(ws学业分段统计.Cells(5, 1), ws学业分段统计.Cells(当前行, 20)).Interior.Color = RGB(255, 255, 204) ' 浅黄色背景

    ' 自动调整列宽
    ws学业分段统计.Columns("A:T").AutoFit

CleanExit:
    ' 恢复Excel设置
    With Application
        .ScreenUpdating = True
        .Calculation = xlCalculationAutomatic
        .EnableEvents = True
    End With
End Sub

' 冒泡排序（按第一列降序排序）
Sub 冒泡排序(ByRef 数组 As Variant, ByVal 数量 As Long)
    Dim i As Long, j As Long
    Dim 临时值 As Variant

    For i = 1 To 数量 - 1
        For j = i + 1 To 数量
            If 数组(i, 1) < 数组(j, 1) Then
                ' 交换第一列（平均分）
                临时值 = 数组(i, 1)
                数组(i, 1) = 数组(j, 1)
                数组(j, 1) = 临时值

                ' 交换第二列（索引）
                临时值 = 数组(i, 2)
                数组(i, 2) = 数组(j, 2)
                数组(j, 2) = 临时值

                ' 交换第三列（如果有）
                If UBound(数组, 2) >= 3 Then
                    临时值 = 数组(i, 3)
                    数组(i, 3) = 数组(j, 3)
                    数组(j, 3) = 临时值
                End If
            End If
        Next j
    Next i
End Sub
