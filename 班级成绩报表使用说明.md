# 班级成绩报表生成工具

## 功能介绍

本工具用于生成按年级统计的班级成绩报表，可以自动从成绩总表中提取数据，计算各班级各科目的平均分和名次，并结合任课教师信息生成完整的统计报表。

## 文件说明

本工具包含以下文件：

1. `班级成绩报表_fixed.vbs` - 主脚本文件
2. `计算科目排名.vbs` - 包含"计算科目排名"子过程的文件

## 使用方法

### 准备工作

1. 确保Excel/WPS工作簿中包含以下工作表：
   - `成绩总表` - 包含学生个人成绩数据（必需）
   - `任课教师` - 包含各班级各科目的任课教师信息（可选）

2. 将两个VBS文件的内容合并到一个VBA模块中：
   - 打开Excel/WPS，按Alt+F11打开VBA编辑器
   - 在项目浏览器中右键点击项目名称，选择"插入"->"模块"
   - 先复制`班级成绩报表_fixed.vbs`的内容到模块中
   - 然后将`计算科目排名.vbs`中的"计算科目排名"子过程复制到模块末尾
   - 保存工作簿

### 运行脚本

1. 在VBA编辑器中，运行"生成班级成绩报表"过程
2. 在弹出的对话框中输入要统计的年级（如"七年级"）
3. 脚本会自动生成"班级成绩报表"工作表，包含所有统计结果

## 数据格式要求

### 成绩总表

成绩总表必须包含以下列：
- 年级（第1列）
- 学校序号（第2列）
- 学校（第3列）
- 校点（第4列）
- 班级（第5列）
- 各科目成绩列（从第6列开始，表头为科目名称）

示例：

| 年级 | 学校序号 | 学校 | 校点 | 班级 | 语文 | 数学 | 英语 | ... |
|------|----------|------|------|------|------|------|------|-----|
| 七年级 | 1 | XX中学 | 总校 | 1班 | 85 | 90 | 88 | ... |
| 七年级 | 1 | XX中学 | 总校 | 1班 | 78 | 92 | 85 | ... |
| ... | ... | ... | ... | ... | ... | ... | ... | ... |

### 任课教师表

任课教师表必须包含以下列：
- 学校（第1列）
- 班级（第2列）
- 年级（第3列）
- 各科目任课教师列（从第4列开始，表头为科目名称）

示例：

| 学校 | 班级 | 年级 | 语文 | 数学 | 英语 | ... |
|------|------|------|------|------|------|-----|
| XX中学 | 1班 | 七年级 | 张老师 | 李老师 | 王老师 | ... |
| XX中学 | 2班 | 七年级 | 赵老师 | 钱老师 | 孙老师 | ... |
| ... | ... | ... | ... | ... | ... | ... |

## 生成的报表格式

生成的班级成绩报表包含以下列：
- 序号
- 学校
- 校点
- 班级
- 实考人数
- 各科目平均分
- 各科目名次
- 各科目任课教师

## 常见问题解决

### 1. "下标越界"错误

如果遇到"下标越界"错误，可能是因为：
- 数据格式不符合要求
- 成绩总表或任课教师表中存在空值或格式错误
- 班级数量统计不准确

解决方法：
- 检查数据格式是否符合要求
- 确保关键列（年级、学校、班级等）没有空值
- 确保科目名称在成绩总表和任课教师表中保持一致

### 2. 找不到工作表

如果提示"未找到'成绩总表'工作表"或"未找到'任课教师'工作表"，请确保：
- 工作簿中包含名为"成绩总表"的工作表
- 如果需要显示任课教师信息，确保工作簿中包含名为"任课教师"的工作表

### 3. 未找到科目列

如果提示"在成绩总表中找不到科目"，请确保：
- 成绩总表中包含科目列，且表头正确
- 科目名称不包含特殊字符或格式

## 注意事项

1. 脚本会自动识别成绩总表中的所有科目列
2. 如果任课教师表不存在，报表中将不显示任课教师信息
3. 排名是按照平均分从高到低排序，相同分数的班级排名相同
4. 脚本会自动处理数据中的异常值（如非数值、负数等）