Sub StatisticScores()
    Dim wb As Workbook
    Dim wsScore As Worksheet
    Dim wsStat As Worksheet
    Dim lastRow As Long
    Dim i As Long, j As Long
    Dim grade As String
    Dim schoolDict As Object
    Dim classDict As Object
    Dim scoreArr() As Variant
    Dim statArr() As Variant
    Dim score As Double
    Dim statRow As Long
    Dim classItem As Variant
    Dim schoolItem As Variant
    Dim classInfo() As String
    Dim schoolInfo() As String
    Dim classStat() As Variant
    Dim schoolStat() As Variant
    Dim totalData() As Variant
    Dim classRankRange As Range
    Dim schoolRankRange As Range
    Dim summaryRows() As Long
    Dim classStudentDict As Object
    Dim schoolStudentDict As Object
    Dim student As Variant ' 声明 student 变量

    ' 设置工作簿和工作表
    Set wb = ThisWorkbook
    Set wsScore = wb.Sheets("成绩总表")
    Set wsStat = wb.Sheets("学业分段统计")

    ' 清除第5行开始的所有单元格格式
    wsStat.Rows("5:" & wsStat.Rows.Count).ClearFormats

    ' 获取要统计的年级
    grade = wsStat.Range("D2").value

    ' 获取成绩总表的最后一行
    lastRow = wsScore.Cells(wsScore.Rows.Count, 1).End(xlUp).Row

    ' 获取成绩总表的数据到数组
    scoreArr = wsScore.Range("A1:V" & lastRow).value

    ' 创建字典用于存储学校和班级的数据
    Set schoolDict = CreateObject("Scripting.Dictionary")
    Set classDict = CreateObject("Scripting.Dictionary")

    ' 遍历成绩总表的数据
    For i = 2 To UBound(scoreArr)
        If scoreArr(i, 1) = grade And scoreArr(i, 18) <> "" Then
            Dim schoolNo As String
            Dim schoolName As String
            Dim className As String
            Dim studentName As String
            schoolNo = scoreArr(i, 2)
            schoolName = scoreArr(i, 3)
            className = scoreArr(i, 4)
            studentName = scoreArr(i, 5) ' 假设姓名在第5列
            score = scoreArr(i, 18)

            ' 处理班级数据
            Dim classKey As String
            classKey = schoolNo & "|" & schoolName & "|" & className
            If Not classDict.exists(classKey) Then
                classDict(classKey) = Array(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)
                Set classStudentDict = CreateObject("Scripting.Dictionary")
                classStudentDict(studentName) = 1
                ' 修改部分：使用 Set 关键字赋值
                Set classDict(classKey & "_students") = classStudentDict
            Else
                Set classStudentDict = classDict(classKey & "_students")
                If Not classStudentDict.exists(studentName) Then
                    classStudentDict(studentName) = 1
                End If
            End If
            classStat = classDict(classKey)
            classStat(0) = classStudentDict.Count ' 实有人数
            classStat(1) = classStat(1) + 1 ' 参考人数
            classStat(18) = classStat(18) + score ' 总分
            Select Case score
                Case Is < 300
                    classStat(2) = classStat(2) + 1
                Case 300 To 319
                    classStat(3) = classStat(3) + 1
                Case 320 To 339
                    classStat(4) = classStat(4) + 1
                Case 340 To 359
                    classStat(5) = classStat(5) + 1
                Case 360 To 379
                    classStat(6) = classStat(6) + 1
                Case 380 To 399
                    classStat(7) = classStat(7) + 1
                Case 400 To 419
                    classStat(8) = classStat(8) + 1
                Case 420 To 439
                    classStat(9) = classStat(9) + 1
                Case 440 To 459
                    classStat(10) = classStat(10) + 1
                Case 460 To 479
                    classStat(11) = classStat(11) + 1
                Case 480 To 499
                    classStat(12) = classStat(12) + 1
                Case 500 To 519
                    classStat(13) = classStat(13) + 1
                Case Is >= 520
                    classStat(14) = classStat(14) + 1
            End Select
            classDict(classKey) = classStat
            ' 修改部分：使用 Set 关键字赋值
            Set classDict(classKey & "_students") = classStudentDict

            ' 处理学校数据
            Dim schoolKey As String
            schoolKey = schoolNo & "|" & schoolName
            If Not schoolDict.exists(schoolKey) Then
                schoolDict(schoolKey) = Array(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)
                Set schoolStudentDict = CreateObject("Scripting.Dictionary")
                schoolStudentDict(studentName) = 1
                ' 修改部分：使用 Set 关键字赋值
                Set schoolDict(schoolKey & "_students") = schoolStudentDict
            Else
                Set schoolStudentDict = schoolDict(schoolKey & "_students")
                If Not schoolStudentDict.exists(studentName) Then
                    schoolStudentDict(studentName) = 1
                End If
            End If
            schoolStat = schoolDict(schoolKey)
            schoolStat(0) = schoolStudentDict.Count ' 实有人数
            schoolStat(1) = schoolStat(1) + 1 ' 参考人数
            schoolStat(18) = schoolStat(18) + score ' 总分
            Select Case score
                Case Is < 300
                    schoolStat(2) = schoolStat(2) + 1
                Case 300 To 319
                    schoolStat(3) = schoolStat(3) + 1
                Case 320 To 339
                    schoolStat(4) = schoolStat(4) + 1
                Case 340 To 359
                    schoolStat(5) = schoolStat(5) + 1
                Case 360 To 379
                    schoolStat(6) = schoolStat(6) + 1
                Case 380 To 399
                    schoolStat(7) = schoolStat(7) + 1
                Case 400 To 419
                    schoolStat(8) = schoolStat(8) + 1
                Case 420 To 439
                    schoolStat(9) = schoolStat(9) + 1
                Case 440 To 459
                    schoolStat(10) = schoolStat(10) + 1
                Case 460 To 479
                    schoolStat(11) = schoolStat(11) + 1
                Case 480 To 499
                    schoolStat(12) = schoolStat(12) + 1
                Case 500 To 519
                    schoolStat(13) = schoolStat(13) + 1
                Case Is >= 520
                    schoolStat(14) = schoolStat(14) + 1
            End Select
            schoolDict(schoolKey) = schoolStat
            ' 修改部分：使用 Set 关键字赋值
            Set schoolDict(schoolKey & "_students") = schoolStudentDict
        End If
    Next i

    ' 初始化统计数组，改为 20 列
    ReDim statArr(1 To classDict.Count + schoolDict.Count + 1, 1 To 20)

    ' 填充班级统计数据
    statRow = 1
    For Each classItem In classDict.keys
        If Not Right(classItem, 9) = "_students" Then
            classInfo = Split(classItem, "|")
            classStat = classDict(classItem)
            statArr(statRow, 1) = classInfo(0)
            statArr(statRow, 2) = classInfo(1)
            statArr(statRow, 3) = classInfo(2)
            For i = 0 To 16
                statArr(statRow, i + 4) = classStat(i)
            Next i
            If classStat(1) > 0 Then
                statArr(statRow, 19) = classStat(18) / classStat(1)
            End If
            statRow = statRow + 1
        End If
    Next classItem

    ' 填充学校统计数据，每个学校汇总跟在其班级后面
    Dim schoolClassCountDict As Object
    Set schoolClassCountDict = CreateObject("Scripting.Dictionary")
    For Each classItem In classDict.keys
        If Not Right(classItem, 9) = "_students" Then
            classInfo = Split(classItem, "|")
            schoolKey = classInfo(0) & "|" & classInfo(1)
            If Not schoolClassCountDict.exists(schoolKey) Then
                schoolClassCountDict(schoolKey) = 0
            End If
            schoolClassCountDict(schoolKey) = schoolClassCountDict(schoolKey) + 1
        End If
    Next classItem

    Dim currentRow As Long
    currentRow = 1
    Dim classRowsAdded As Long
    For Each schoolItem In schoolDict.keys
        If Not Right(schoolItem, 9) = "_students" Then
            schoolInfo = Split(schoolItem, "|")
            schoolStat = schoolDict(schoolItem)
            classRowsAdded = 0
            ' 先输出该学校的班级数据
            For Each classItem In classDict.keys
                If Not Right(classItem, 9) = "_students" Then
                    classInfo = Split(classItem, "|")
                    If classInfo(0) & "|" & classInfo(1) = schoolItem Then
                        classStat = classDict(classItem)
                        statArr(currentRow, 1) = classInfo(0)
                        statArr(currentRow, 2) = classInfo(1)
                        statArr(currentRow, 3) = classInfo(2)
                        For i = 4 To 19
                            statArr(currentRow, i) = classStat(i - 4)
                        Next i
                        If classStat(1) > 0 Then
                            statArr(currentRow, 19) = classStat(18) / classStat(1)
                        End If
                        currentRow = currentRow + 1
                        classRowsAdded = classRowsAdded + 1
                    End If
                End If
            Next classItem
            ' 输出该学校的汇总数据
            statArr(currentRow, 1) = schoolInfo(0)
            statArr(currentRow, 2) = schoolInfo(1) & "汇总"
            statArr(currentRow, 3) = ""
            For i = 4 To 19
                statArr(currentRow, i) = schoolStat(i - 4)
            Next i
            If schoolStat(1) > 0 Then
                statArr(currentRow, 19) = schoolStat(18) / schoolStat(1)
            End If
            currentRow = currentRow + 1
        End If
    Next schoolItem

    ' 计算全区汇总数据
    ReDim totalData(0 To 18)
    Dim allStudentsDict As Object
    Set allStudentsDict = CreateObject("Scripting.Dictionary")
    For Each schoolItem In schoolDict.keys
        If Not Right(schoolItem, 9) = "_students" Then
            schoolStat = schoolDict(schoolItem)
            Set schoolStudentDict = schoolDict(schoolItem & "_students")
            ' 修改部分：直接遍历字典获取键
            For Each student In schoolStudentDict
                If Not allStudentsDict.exists(student) Then
                    allStudentsDict(student) = 1
                End If
            Next student
            For i = 0 To 18
                totalData(i) = totalData(i) + schoolStat(i)
            Next i
        End If
    Next schoolItem
    totalData(0) = allStudentsDict.Count ' 全区实有人数
    statArr(currentRow, 1) = ""
    statArr(currentRow, 2) = "全区汇总"
    statArr(currentRow, 3) = ""
    For i = 4 To 19
        statArr(currentRow, i) = totalData(i - 4)
    Next i
    If totalData(1) > 0 Then
        statArr(currentRow, 19) = totalData(18) / totalData(1)
    End If

    ' 填充统计数据到工作表
    wsStat.Range("A5").Resize(UBound(statArr), UBound(statArr, 2)).value = statArr

    ' 计算班级平均分排名
    Set classRankRange = wsStat.Range("S5:S" & classDict.Count / 2 + 4)
    classRankRange.Offset(0, 1).FormulaR1C1 = "=RANK(RC[-1],R5C[-1]:R" & classDict.Count / 2 + 4 & "C[-1],0)"
    classRankRange.Offset(0, 1).value = classRankRange.Offset(0, 1).value

    ' 计算学校平均分排名
    Set schoolRankRange = wsStat.Range("S" & classDict.Count / 2 + 5 & ":S" & classDict.Count / 2 + schoolDict.Count / 2 + 4)
    schoolRankRange.Offset(0, 1).FormulaR1C1 = "=RANK(RC[-1],R" & classDict.Count / 2 + 5 & "C[-1]:R" & classDict.Count / 2 + schoolDict.Count / 2 + 4 & "C[-1],0)"
    schoolRankRange.Offset(0, 1).value = schoolRankRange.Offset(0, 1).value

    ' 设置汇总行格式
    ReDim summaryRows(0 To schoolDict.Count / 2)
    j = 0
    currentRow = 5
    For Each schoolItem In schoolDict.keys
        If Not Right(schoolItem, 9) = "_students" Then
            currentRow = currentRow + schoolClassCountDict(schoolItem)
            summaryRows(j) = currentRow
            j = j + 1
            currentRow = currentRow + 1
        End If
    Next schoolItem
    ' 处理全区汇总行
    ReDim Preserve summaryRows(0 To schoolDict.Count / 2)
    summaryRows(UBound(summaryRows)) = currentRow

    For i = LBound(summaryRows) To UBound(summaryRows)
        wsStat.Rows(summaryRows(i)).Font.Color = RGB(255, 0, 0)
        wsStat.Rows(summaryRows(i)).Font.Bold = True
        wsStat.Range(wsStat.Cells(summaryRows(i), 1), wsStat.Cells(summaryRows(i), 20)).Interior.Color = RGB(255, 255, 0)
        wsStat.Cells(summaryRows(i), 2).Resize(1, 2).Merge
    Next i

    ' 给所有数据单元格添加边框
    wsStat.Range("A5:T" & classDict.Count / 2 + schoolDict.Count / 2 + 5).Borders.LineStyle = xlContinuous

End Sub