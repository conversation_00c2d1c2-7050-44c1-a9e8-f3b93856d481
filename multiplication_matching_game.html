<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>乘法口诀连线游戏</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f8ff;
        }

        .game-container {
            width: 800px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            text-align: center;
        }

        h1 {
            color: #333;
        }

        .start-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 20px 0;
        }

        .start-btn:hover {
            background-color: #45a049;
        }

        .game-area {
            display: none;
            margin-top: 20px;
        }

        .columns-container {
            display: flex;
            justify-content: space-between;
            position: relative;
        }

        .column {
            width: 60%;
        }

        .column-spacer {
            width: 40%;
            position: relative;
        }

        .item {
            background-color: #e9f5f9;
            border: 2px solid #7fb3d5;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            cursor: pointer;
            font-size: 18px;
            position: relative;
            z-index: 2;
            width: 33%;
            margin-left: auto;
            margin-right: auto;
        }

        .item:hover {
            background-color: #d4e6f1;
        }

        .item.selected {
            background-color: #aed6f1;
            border-color: #3498db;
        }

        .score-display {
            font-size: 24px;
            font-weight: bold;
            margin-top: 20px;
            display: none;
        }

        #canvas-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        canvas {
            width: 100%;
            height: 100%;
        }

        .instructions {
            margin: 20px 0;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>乘法口诀连线游戏</h1>
        <div class="instructions">
            点击"开始游戏"按钮，将左侧的乘法式子与右侧的正确结果连线。连对显示绿线，连错显示红线。
        </div>
        <button class="start-btn" id="start-btn">开始游戏</button>

        <div class="game-area" id="game-area">
            <div class="columns-container">
                <div class="column" id="expressions-column">
                    <!-- 乘法表达式将在这里生成 -->
                </div>

                <div class="column-spacer">
                    <div id="canvas-container">
                        <canvas id="connection-canvas"></canvas>
                    </div>
                </div>

                <div class="column" id="results-column">
                    <!-- 结果将在这里生成 -->
                </div>
            </div>
        </div>

        <div class="score-display" id="score-display">
            得分: <span id="score">0</span>/100
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const startBtn = document.getElementById('start-btn');
            const gameArea = document.getElementById('game-area');
            const expressionsColumn = document.getElementById('expressions-column');
            const resultsColumn = document.getElementById('results-column');
            const scoreDisplay = document.getElementById('score-display');
            const scoreElement = document.getElementById('score');
            const canvas = document.getElementById('connection-canvas');
            const ctx = canvas.getContext('2d');

            let expressions = [];
            let results = [];
            let connections = [];
            let selectedItem = null;
            let gameCompleted = false;

            // 设置Canvas大小
            function resizeCanvas() {
                const container = document.getElementById('canvas-container');
                canvas.width = container.offsetWidth;
                canvas.height = container.offsetHeight;
            }

            window.addEventListener('resize', resizeCanvas);

            // 生成随机乘法表达式
            function generateExpressions() {
                expressions = [];
                results = [];

                // 生成10个不重复的乘法表达式
                while (expressions.length < 10) {
                    const a = Math.floor(Math.random() * 9) + 1;
                    const b = Math.floor(Math.random() * 9) + 1;
                    const expression = `${a} × ${b}`;
                    const result = a * b;

                    // 检查是否已存在相同的表达式或结果
                    let isDuplicate = false;
                    for (let i = 0; i < expressions.length; i++) {
                        if (expressions[i] === expression || results[i] === result) {
                            isDuplicate = true;
                            break;
                        }
                    }

                    if (!isDuplicate) {
                        expressions.push(expression);
                        results.push(result);
                    }
                }
            }

            // 打乱结果顺序
            function shuffleResults() {
                for (let i = results.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [results[i], results[j]] = [results[j], results[i]];
                }
            }

            // 渲染表达式和结果
            function renderItems() {
                expressionsColumn.innerHTML = '';
                resultsColumn.innerHTML = '';

                expressions.forEach((expression, index) => {
                    const item = document.createElement('div');
                    item.className = 'item expression';
                    item.textContent = expression;
                    item.dataset.index = index;
                    item.addEventListener('click', handleItemClick);
                    expressionsColumn.appendChild(item);
                });

                results.forEach((result, index) => {
                    const item = document.createElement('div');
                    item.className = 'item result';
                    item.textContent = result;
                    item.dataset.index = index;
                    item.addEventListener('click', handleItemClick);
                    resultsColumn.appendChild(item);
                });
            }

            // 处理点击事件
            function handleItemClick(event) {
                if (gameCompleted) return;

                const clickedItem = event.target;
                const isExpression = clickedItem.classList.contains('expression');

                // 如果没有选中项，或者选中了同一列的项，则选中当前项
                if (!selectedItem || (isExpression && selectedItem.classList.contains('expression')) ||
                    (!isExpression && selectedItem.classList.contains('result'))) {

                    // 取消之前的选中状态
                    if (selectedItem) {
                        selectedItem.classList.remove('selected');
                    }

                    // 选中当前项
                    clickedItem.classList.add('selected');
                    selectedItem = clickedItem;
                } else {
                    // 已经选中了另一列的项，尝试连线
                    const expressionItem = isExpression ? clickedItem : selectedItem;
                    const resultItem = isExpression ? selectedItem : clickedItem;

                    const expressionIndex = parseInt(expressionItem.dataset.index);
                    const resultIndex = parseInt(resultItem.dataset.index);

                    // 检查是否已经连线
                    const alreadyConnected = connections.some(conn =>
                        (conn.expressionIndex === expressionIndex) ||
                        (conn.resultIndex === resultIndex)
                    );

                    if (!alreadyConnected) {
                        // 创建新连线
                        const expressionValue = expressions[expressionIndex];
                        const [a, b] = expressionValue.split(' × ').map(Number);
                        const correctResult = a * b;
                        const selectedResult = results[resultIndex];

                        const isCorrect = correctResult === selectedResult;

                        connections.push({
                            expressionIndex,
                            resultIndex,
                            isCorrect
                        });

                        // 绘制连线
                        drawConnections();

                        // 检查游戏是否结束
                        if (connections.length === 10) {
                            endGame();
                        }
                    }

                    // 取消选中状态
                    selectedItem.classList.remove('selected');
                    clickedItem.classList.remove('selected');
                    selectedItem = null;
                }
            }

            // 绘制所有连线
            function drawConnections() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                connections.forEach(connection => {
                    const expressionItem = expressionsColumn.children[connection.expressionIndex];
                    const resultItem = resultsColumn.children[connection.resultIndex];

                    const startX = expressionItem.offsetLeft + expressionItem.offsetWidth;
                    const startY = expressionItem.offsetTop + expressionItem.offsetHeight / 2;
                    const endX = resultItem.offsetLeft;
                    const endY = resultItem.offsetTop + resultItem.offsetHeight / 2;

                    ctx.beginPath();
                    ctx.moveTo(startX, startY);
                    ctx.lineTo(endX, endY);
                    ctx.strokeStyle = connection.isCorrect ? '#2ecc71' : '#e74c3c';
                    ctx.lineWidth = 3;
                    ctx.stroke();
                });
            }

            // 结束游戏并计算得分
            function endGame() {
                gameCompleted = true;

                // 计算得分
                const correctConnections = connections.filter(conn => conn.isCorrect).length;
                scoreElement.textContent = correctConnections;
                scoreDisplay.style.display = 'block';

                // 修改开始按钮为重新开始
                startBtn.textContent = '再来一次';
            }

            // 开始游戏
            function startGame() {
                // 重置游戏状态
                connections = [];
                selectedItem = null;
                gameCompleted = false;
                scoreDisplay.style.display = 'none';

                // 生成新的表达式和结果
                generateExpressions();
                shuffleResults();
                renderItems();

                // 显示游戏区域
                gameArea.style.display = 'block';

                // 调整Canvas大小
                resizeCanvas();

                // 清除Canvas
                ctx.clearRect(0, 0, canvas.width, canvas.height);
            }

            // 绑定开始按钮事件
            startBtn.addEventListener('click', startGame);
        });
    </script>
</body>
</html>
