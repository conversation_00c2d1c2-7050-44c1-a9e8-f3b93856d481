
Sub 原始分数段统计()
    Dim wb As Workbook
    Dim wsScore As Worksheet
    Dim wsStat As Worksheet
    Dim lastRow As Long
    Dim i As Long, j As Long
    Dim grade As String
    Dim schoolDict As Object
    Dim classDict As Object
    Dim scoreArr() As Variant
    Dim statArr() As Variant
    Dim score As Variant ' 修改为 Variant 类型
    Dim statRow As Long
    Dim classItem As Variant
    Dim schoolItem As Variant
    Dim classInfo() As String
    Dim schoolInfo() As String
    Dim classStat() As Variant
    Dim schoolStat() As Variant
    Dim totalData() As Variant
    Dim classRankRange As Range
    Dim schoolRankRange As Range
    Dim summaryRows() As Long
    Dim classStudentDict As Object
    Dim schoolStudentDict As Object
    Dim student As Variant ' 声明 student 变量

    ' 设置工作簿和工作表
    Set wb = ThisWorkbook
    Set wsScore = wb.Sheets("成绩总表")
    Set wsStat = wb.Sheets("原始分数段统计")

    ' 清除第5行开始的所有单元格格式
    wsStat.Rows("5:" & wsStat.Rows.Count).ClearFormats

    ' 获取要统计的年级
    grade = wsStat.Range("D2").value

    ' 获取成绩总表的最后一行
    lastRow = wsScore.Cells(wsScore.Rows.Count, 1).End(xlUp).Row

    ' 获取成绩总表的数据到数组
    scoreArr = wsScore.Range("A1:V" & lastRow).value

    ' 创建字典用于存储学校和班级的数据
    Set schoolDict = CreateObject("Scripting.Dictionary")
    Set classDict = CreateObject("Scripting.Dictionary")

    ' 遍历成绩总表的数据
    For i = 2 To UBound(scoreArr)
        If scoreArr(i, 1) = grade Then
            Dim schoolNo As String
            Dim schoolName As String
            Dim className As String
            Dim studentName As String
            schoolNo = scoreArr(i, 2)
            schoolName = scoreArr(i, 3)
            className = scoreArr(i, 4)
            studentName = scoreArr(i, 6) ' 姓名在第6列（F列）
            score = scoreArr(i, 17) ' 总分在第17列（Q列）

            ' 检查 score 是否为数值
            If Not IsNumeric(score) Then
                score = ""
            End If

            ' 处理班级数据
            Dim classKey As String
            classKey = schoolNo & "|" & schoolName & "|" & className
            If Not classDict.Exists(classKey) Then
                classDict(classKey) = Array(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)
                Set classStudentDict = CreateObject("Scripting.Dictionary")
                classStudentDict(studentName) = 1
                Set classDict(classKey & "_students") = classStudentDict
            Else
                Set classStudentDict = classDict(classKey & "_students")
                If Not classStudentDict.Exists(studentName) Then
                    classStudentDict(studentName) = 1
                End If
            End If
            classStat = classDict(classKey)
            classStat(0) = classStudentDict.Count ' 实有人数
            If score <> "" Then
                classStat(1) = classStat(1) + 1 ' 参考人数
                classStat(19) = classStat(19) + CDbl(score) ' 总分，确保转换为 Double 类型
            End If
            If score <> "" Then
                Select Case CDbl(score) ' 确保转换为 Double 类型
                    Case Is < 470
                        classStat(2) = classStat(2) + 1
                    Case 470 To 509
                        classStat(3) = classStat(3) + 1
                    Case 510 To 549
                        classStat(4) = classStat(4) + 1
                    Case 550 To 589
                        classStat(5) = classStat(5) + 1
                    Case 590 To 629
                        classStat(6) = classStat(6) + 1
                    Case 630 To 669
                        classStat(7) = classStat(7) + 1
                    Case 670 To 709
                        classStat(8) = classStat(8) + 1
                    Case 710 To 749
                        classStat(9) = classStat(9) + 1
                    Case 750 To 789
                        classStat(10) = classStat(10) + 1
                    Case 790 To 829
                        classStat(11) = classStat(11) + 1
                    Case 830 To 869
                        classStat(12) = classStat(12) + 1
                    Case 870 To 909
                        classStat(13) = classStat(13) + 1
                    Case 910 To 949
                        classStat(14) = classStat(14) + 1
                    Case Is >= 950
                        classStat(15) = classStat(15) + 1
                End Select
            End If
            classDict(classKey) = classStat
            Set classDict(classKey & "_students") = classStudentDict

            ' 处理学校数据
            Dim schoolKey As String
            schoolKey = schoolNo & "|" & schoolName
            If Not schoolDict.Exists(schoolKey) Then
                schoolDict(schoolKey) = Array(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)
                Set schoolStudentDict = CreateObject("Scripting.Dictionary")
                schoolStudentDict(studentName) = 1
                Set schoolDict(schoolKey & "_students") = schoolStudentDict
            Else
                Set schoolStudentDict = schoolDict(schoolKey & "_students")
                If Not schoolStudentDict.Exists(studentName) Then
                    schoolStudentDict(studentName) = 1
                End If
            End If
            schoolStat = schoolDict(schoolKey)
            schoolStat(0) = schoolStudentDict.Count ' 实有人数
            If score <> "" Then
                schoolStat(1) = schoolStat(1) + 1 ' 参考人数
                schoolStat(19) = schoolStat(19) + CDbl(score) ' 总分，确保转换为 Double 类型
            End If
            If score <> "" Then
                Select Case CDbl(score) ' 确保转换为 Double 类型
                    Case Is < 470
                        schoolStat(2) = schoolStat(2) + 1
                    Case 470 To 509
                        schoolStat(3) = schoolStat(3) + 1
                    Case 510 To 549
                        schoolStat(4) = schoolStat(4) + 1
                    Case 550 To 589
                        schoolStat(5) = schoolStat(5) + 1
                    Case 590 To 629
                        schoolStat(6) = schoolStat(6) + 1
                    Case 630 To 669
                        schoolStat(7) = schoolStat(7) + 1
                    Case 670 To 709
                        schoolStat(8) = schoolStat(8) + 1
                    Case 710 To 749
                        schoolStat(9) = schoolStat(9) + 1
                    Case 750 To 789
                        schoolStat(10) = schoolStat(10) + 1
                    Case 790 To 829
                        schoolStat(11) = schoolStat(11) + 1
                    Case 830 To 869
                        schoolStat(12) = schoolStat(12) + 1
                    Case 870 To 909
                        schoolStat(13) = schoolStat(13) + 1
                    Case 910 To 949
                        schoolStat(14) = schoolStat(14) + 1
                    Case Is >= 950
                        schoolStat(15) = schoolStat(15) + 1
                End Select
            End If
            schoolDict(schoolKey) = schoolStat
            Set schoolDict(schoolKey & "_students") = schoolStudentDict
        End If
    Next i

    ' 初始化统计数组，改为 21 列
    ReDim statArr(1 To classDict.Count + schoolDict.Count + 1, 1 To 21)

    ' 填充学校统计数据，每个学校汇总跟在其班级后面
    Dim schoolClassCountDict As Object
    Set schoolClassCountDict = CreateObject("Scripting.Dictionary")
    For Each classItem In classDict.keys
        If Not Right(classItem, 9) = "_students" Then
            classInfo = Split(classItem, "|")
            schoolKey = classInfo(0) & "|" & classInfo(1)
            If Not schoolClassCountDict.Exists(schoolKey) Then
                schoolClassCountDict(schoolKey) = 0
            End If
            schoolClassCountDict(schoolKey) = schoolClassCountDict(schoolKey) + 1
        End If
    Next classItem

    Dim currentRow As Long
    currentRow = 1
    Dim classRowsAdded As Long
    For Each schoolItem In schoolDict.keys
        If Not Right(schoolItem, 9) = "_students" Then
            schoolInfo = Split(schoolItem, "|")
            schoolStat = schoolDict(schoolItem)
            classRowsAdded = 0
            ' 先输出该学校的班级数据
            For Each classItem In classDict.keys
                If Not Right(classItem, 9) = "_students" Then
                    classInfo = Split(classItem, "|")
                    If classInfo(0) & "|" & classInfo(1) = schoolItem Then
                        classStat = classDict(classItem)
                        statArr(currentRow, 1) = classInfo(0)
                        statArr(currentRow, 2) = classInfo(1)
                        statArr(currentRow, 3) = classInfo(2)
                        For i = 4 To 20
                            statArr(currentRow, i) = classStat(i - 4)
                        Next i
                        If classStat(1) > 0 Then
                            statArr(currentRow, 20) = classStat(19) / classStat(1)
                        End If
                        currentRow = currentRow + 1
                        classRowsAdded = classRowsAdded + 1
                    End If
                End If
            Next classItem
            ' 输出该学校的汇总数据
            statArr(currentRow, 1) = schoolInfo(0)
            statArr(currentRow, 2) = schoolInfo(1) & "汇总"
            statArr(currentRow, 3) = ""
            For i = 4 To 20
                statArr(currentRow, i) = schoolStat(i - 4)
            Next i
            If schoolStat(1) > 0 Then
                statArr(currentRow, 20) = schoolStat(19) / schoolStat(1)
            End If
            currentRow = currentRow + 1
        End If
    Next schoolItem

    ' 计算全区汇总数据
    ReDim totalData(0 To 19)
    Dim allStudentsDict As Object
    Set allStudentsDict = CreateObject("Scripting.Dictionary")
    For Each schoolItem In schoolDict.keys
        If Not Right(schoolItem, 9) = "_students" Then
            schoolStat = schoolDict(schoolItem)
            Set schoolStudentDict = schoolDict(schoolItem & "_students")
            For Each student In schoolStudentDict
                If Not allStudentsDict.Exists(student) Then
                    allStudentsDict(student) = 1
                End If
            Next student
            For i = 0 To 19
                totalData(i) = totalData(i) + schoolStat(i)
            Next i
        End If
    Next schoolItem
    totalData(0) = allStudentsDict.Count ' 全区实有人数
    statArr(currentRow, 1) = ""
    statArr(currentRow, 2) = "全区汇总"
    statArr(currentRow, 3) = ""
    For i = 4 To 17
        statArr(currentRow, i) = totalData(i - 4)
    Next i
    ' 单独处理总分列
    statArr(currentRow, 20) = totalData(19)

    ' 增加当前行计数，以便正确计算最后一行
    currentRow = currentRow + 1

    ' 填充统计数据到工作表
    wsStat.Range("A5").Resize(UBound(statArr), UBound(statArr, 2)).value = statArr

    ' 计算班级和学校排名
    Dim classRows() As Long
    Dim schoolRows() As Long
    Dim rowCount As Long

    ' 收集班级行号
    rowCount = 0
    ReDim classRows(0 To classDict.Count - 1)
    For i = 5 To 4 + UBound(statArr)
        ' 检查是否为班级行（非汇总行）
        If wsStat.Cells(i, 3).value <> "" Then
            classRows(rowCount) = i
            rowCount = rowCount + 1
        End If
    Next i

    ' 调整数组大小以匹配实际班级数量
    If rowCount > 0 And rowCount < UBound(classRows) + 1 Then
        ReDim Preserve classRows(0 To rowCount - 1)
    End If

    ' 收集学校汇总行号
    rowCount = 0
    ReDim schoolRows(0 To schoolDict.Count - 1)
    For i = 5 To 4 + UBound(statArr)
        ' 检查是否为学校汇总行（包含"汇总"字样）
        If wsStat.Cells(i, 2).value <> "" And wsStat.Cells(i, 3).value = "" And InStr(wsStat.Cells(i, 2).value, "汇总") > 0 And wsStat.Cells(i, 2).value <> "全区汇总" Then
            schoolRows(rowCount) = i
            rowCount = rowCount + 1
        End If
    Next i

    ' 调整数组大小以匹配实际学校数量
    If rowCount > 0 And rowCount < UBound(schoolRows) + 1 Then
        ReDim Preserve schoolRows(0 To rowCount - 1)
    End If

    ' 计算班级排名
    If UBound(classRows) >= 0 Then
        ' 创建班级平均分数组
        Dim classAvgScores() As Double
        ReDim classAvgScores(0 To UBound(classRows))

        ' 获取所有班级的平均分
        For i = 0 To UBound(classRows)
            classAvgScores(i) = CDbl(wsStat.Cells(classRows(i), 20).value)
        Next i

        ' 计算每个班级的排名
        For i = 0 To UBound(classRows)
            Dim classRank As Long
            classRank = 1

            ' 计算排名（比较当前班级与所有班级）
            For j = 0 To UBound(classRows)
                If classAvgScores(j) > classAvgScores(i) Then
                    classRank = classRank + 1
                End If
            Next j

            ' 设置排名值
            wsStat.Cells(classRows(i), 21).value = classRank
        Next i
    End If

    ' 计算学校排名
    If UBound(schoolRows) >= 0 Then
        ' 创建学校平均分数组
        Dim schoolAvgScores() As Double
        ReDim schoolAvgScores(0 To UBound(schoolRows))

        ' 获取所有学校的平均分
        For i = 0 To UBound(schoolRows)
            schoolAvgScores(i) = CDbl(wsStat.Cells(schoolRows(i), 20).value)
        Next i

        ' 计算每个学校的排名
        For i = 0 To UBound(schoolRows)
            Dim schoolRank As Long
            schoolRank = 1

            ' 计算排名（比较当前学校与所有学校）
            For j = 0 To UBound(schoolRows)
                If schoolAvgScores(j) > schoolAvgScores(i) Then
                    schoolRank = schoolRank + 1
                End If
            Next j

            ' 设置排名值
            wsStat.Cells(schoolRows(i), 21).value = schoolRank
        Next i
    End If

    ' 排名已经直接设置为值，无需转换

    ' 设置汇总行格式
    ReDim summaryRows(0 To schoolDict.Count)

    ' 使用已经收集的学校汇总行号
    For i = 0 To UBound(schoolRows)
        summaryRows(i) = schoolRows(i)
    Next i

    ' 处理全区汇总行
    ' 找到全区汇总行
    Dim districtSummaryRow As Long
    For i = 5 To 4 + UBound(statArr)
        If wsStat.Cells(i, 2).value = "全区汇总" Then
            districtSummaryRow = i
            Exit For
        End If
    Next i

    If districtSummaryRow > 0 Then
        ReDim Preserve summaryRows(0 To UBound(schoolRows) + 1)
        summaryRows(UBound(summaryRows)) = districtSummaryRow
    End If

    For i = LBound(summaryRows) To UBound(summaryRows)
        If summaryRows(i) > 0 And summaryRows(i) <= wsStat.Rows.Count Then ' 检查行号范围
            wsStat.Rows(summaryRows(i)).Font.Color = RGB(255, 0, 0)
            wsStat.Rows(summaryRows(i)).Font.Bold = True
            wsStat.Range(wsStat.Cells(summaryRows(i), 1), wsStat.Cells(summaryRows(i), 21)).Interior.Color = RGB(255, 255, 0)
            wsStat.Cells(summaryRows(i), 2).Resize(1, 2).Merge
        End If
    Next i

    ' 给所有数据单元格添加边框和设置格式
    ' 使用实际数据行数
    Dim lastDataRow As Long
    lastDataRow = 4 + currentRow - 1 ' 减1是因为currentRow已经在最后增加1

    ' 添加边框
    wsStat.Range("A5:U" & lastDataRow).Borders.LineStyle = xlContinuous

    ' 设置所有数据居中
    With wsStat.Range("A5:U" & lastDataRow)
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
    End With

End Sub
