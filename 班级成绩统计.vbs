Sub 校级成绩汇总统计()
    Dim ws As Worksheet
    Dim lastRow As <PERSON>, lastCol As Long
    Dim i As <PERSON>, j <PERSON>, k <PERSON>
    Dim schoolArr() As String, gradeArr() As String
    Dim schoolCount As Long, gradeCount As Long
    Dim dataRange As Range
    Dim totalScoreCol As <PERSON>, chineseCol <PERSON>, mathCol <PERSON>, englishCol <PERSON>, scienceCol <PERSON>, moralCol As Long
    Dim gradeCol As <PERSON>, schoolCol As <PERSON>, nameCol As Long
    Dim resultRow As Long
    Dim currentGrade As String, currentSchool As String
    Dim totalStudents As Long, excellentCount As <PERSON>, passCount As Long, lowCount As Long
    Dim totalScore As Double, maxScore As Double, minScore As Double
    Dim chineseTotal As Double, mathTotal As Double, englishTotal As Double, scienceTotal As Double, moralTotal As Double
    Dim chineseCount As Long, mathCount As <PERSON>, englishCount As Long, scienceCount As Long, moralCount As Long
    Dim resultWs As Worksheet
    
    ' 设置工作表
    Set ws = ThisWorkbook.Sheets("成绩汇总表")
    Set resultWs = Sheets("学校统计") ' 
    ' 删除第5行以后的所有行
    If resultWs.Cells(resultWs.Rows.Count, 1).End(xlUp).Row > 4 Then
        resultWs.Rows("5:" & resultWs.Rows.Count).Delete
    End If
    ' 找到最后一行和最后一列
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row
    lastCol = ws.Cells(1, ws.Columns.Count).End(xlToLeft).Column

    ' 找到关键列的位置
    For i = 1 To lastCol
        Select Case ws.Cells(1, i).Value
            Case "年级"
                gradeCol = i
            Case "学校"
                schoolCol = i
            Case "姓名"
                nameCol = i
            Case "语文"
                chineseCol = i
            Case "数学"
                mathCol = i
            Case "英语"
                englishCol = i
            Case "科学"
                scienceCol = i
            Case "道德与法治"
                moralCol = i
            Case "总分"
                totalScoreCol = i
        End Select
    Next i

    ' 使用数组直接收集唯一的学校和年级
    ' 首先，创建临时数组来存储所有可能的值
    Dim tempSchools() As String
    Dim tempGrades() As String
    ReDim tempSchools(1 To lastRow)
    ReDim tempGrades(1 To lastRow)

    schoolCount = 0
    gradeCount = 0

    ' 收集所有学校和年级
    For i = 4 To lastRow
        Dim schoolFound As Boolean, gradeFound As Boolean
        schoolFound = False
        gradeFound = False

        ' 检查学校是否已经在数组中 - 使用Len函数检查是否为空
        If Len(Trim(ws.Cells(i, schoolCol).Value)) > 0 Then
            For j = 1 To schoolCount
                If tempSchools(j) = ws.Cells(i, schoolCol).Value Then
                    schoolFound = True
                    Exit For
                End If
            Next j

            ' 如果学校不在数组中，添加它
            If Not schoolFound Then
                schoolCount = schoolCount + 1
                tempSchools(schoolCount) = ws.Cells(i, schoolCol).Value
            End If
        End If

        ' 检查年级是否已经在数组中 - 使用Len函数检查是否为空
        If Len(Trim(ws.Cells(i, gradeCol).Value)) > 0 Then
            For j = 1 To gradeCount
                If tempGrades(j) = ws.Cells(i, gradeCol).Value Then
                    gradeFound = True
                    Exit For
                End If
            Next j

            ' 如果年级不在数组中，添加它
            If Not gradeFound Then
                gradeCount = gradeCount + 1
                tempGrades(gradeCount) = ws.Cells(i, gradeCol).Value
            End If
        End If
    Next i

    ' 重新调整数组大小以匹配实际的唯一值数量
    ReDim schoolArr(1 To schoolCount)
    ReDim gradeArr(1 To gradeCount)

    ' 复制唯一值到最终数组
    For i = 1 To schoolCount
        schoolArr(i) = tempSchools(i)
    Next i

    For i = 1 To gradeCount
        gradeArr(i) = tempGrades(i)
    Next i

    ' 排序年级数组（假设年级是数字或可比较的字符串）
    For i = 1 To gradeCount - 1
        For j = i + 1 To gradeCount
            If gradeArr(i) > gradeArr(j) Then
                currentGrade = gradeArr(i)
                gradeArr(i) = gradeArr(j)
                gradeArr(j) = currentGrade
            End If
        Next j
    Next i

    ' 结果起始行（假设表头已存在）
    resultRow = 5

    ' 对每个年级进行统计
    For i = 1 To gradeCount
        currentGrade = gradeArr(i)

        ' 创建一个临时数组来存储每个学校的统计结果
        Dim schoolStats() As Variant
        ReDim schoolStats(1 To schoolCount + 1, 1 To 24) ' +1 是为了全区统计

        ' 对每个学校进行统计
        For j = 1 To schoolCount
            currentSchool = schoolArr(j)

            ' 初始化统计变量
            totalStudents = 0
            totalScore = 0
            excellentCount = 0
            passCount = 0
            lowCount = 0
            maxScore = 0
            minScore = 9999
            chineseTotal = 0
            mathTotal = 0
            englishTotal = 0
            scienceTotal = 0
            moralTotal = 0
            chineseCount = 0
            mathCount = 0
            englishCount = 0
            scienceCount = 0
            moralCount = 0

            ' 统计数据
            For k = 4 To lastRow
                ' 如果学校和年级匹配
                If ws.Cells(k, schoolCol).Value = currentSchool And ws.Cells(k, gradeCol).Value = currentGrade Then
                    ' 检查总分是否有效
                    If Len(Trim(ws.Cells(k, totalScoreCol).Value)) > 0 And IsNumeric(ws.Cells(k, totalScoreCol).Value) Then
                        Dim score As Double
                        score = CDbl(ws.Cells(k, totalScoreCol).Value)
                        
                        ' 只有有效总分的学生才计入参考人数
                        totalStudents = totalStudents + 1
                        totalScore = totalScore + score
                        
                        If score >= 425 Then
                            excellentCount = excellentCount + 1
                        End If
                        
                        If score >= 300 Then
                            passCount = passCount + 1
                        End If
                        
                        If score < 200 Then
                            lowCount = lowCount + 1
                        End If
                        
                        If score > maxScore Then
                            maxScore = score
                        End If
                        
                        If score < minScore Then
                            minScore = score
                        End If
                    End If
                    
                    ' 各科目统计 - 不再限制只统计有总分的学生
                    ' 语文
                    If Len(Trim(ws.Cells(k, chineseCol).Value)) > 0 And IsNumeric(ws.Cells(k, chineseCol).Value) Then
                        chineseTotal = chineseTotal + CDbl(ws.Cells(k, chineseCol).Value)
                        chineseCount = chineseCount + 1
                    End If
                    
                    ' 数学
                    If Len(Trim(ws.Cells(k, mathCol).Value)) > 0 And IsNumeric(ws.Cells(k, mathCol).Value) Then
                        mathTotal = mathTotal + CDbl(ws.Cells(k, mathCol).Value)
                        mathCount = mathCount + 1
                    End If
                    
                    ' 英语
                    If Len(Trim(ws.Cells(k, englishCol).Value)) > 0 And IsNumeric(ws.Cells(k, englishCol).Value) Then
                        englishTotal = englishTotal + CDbl(ws.Cells(k, englishCol).Value)
                        englishCount = englishCount + 1
                    End If
                    
                    ' 科学
                    If Len(Trim(ws.Cells(k, scienceCol).Value)) > 0 And IsNumeric(ws.Cells(k, scienceCol).Value) Then
                        scienceTotal = scienceTotal + CDbl(ws.Cells(k, scienceCol).Value)
                        scienceCount = scienceCount + 1
                    End If
                    
                    ' 道德与法治
                    If Len(Trim(ws.Cells(k, moralCol).Value)) > 0 And IsNumeric(ws.Cells(k, moralCol).Value) Then
                        moralTotal = moralTotal + CDbl(ws.Cells(k, moralCol).Value)
                        moralCount = moralCount + 1
                    End If
                End If
            Next k

            ' 计算平均分
            Dim avgTotal As Double, avgChinese As Double, avgMath As Double, avgEnglish As Double, avgScience As Double, avgMoral As Double

            If totalStudents > 0 Then
                avgTotal = totalScore / totalStudents
            Else
                avgTotal = 0
            End If
            
            ' 使用各科目的有效学生数计算平均分
            If chineseCount > 0 Then
                avgChinese = chineseTotal / chineseCount
            Else
                avgChinese = 0
            End If
            
            If mathCount > 0 Then
                avgMath = mathTotal / mathCount
            Else
                avgMath = 0
            End If
            
            If englishCount > 0 Then
                avgEnglish = englishTotal / englishCount
            Else
                avgEnglish = 0
            End If
            
            If scienceCount > 0 Then
                avgScience = scienceTotal / scienceCount
            Else
                avgScience = 0
            End If
            
            If moralCount > 0 Then
                avgMoral = moralTotal / moralCount
            Else
                avgMoral = 0
            End If

            ' 如果没有学生，最低分设为0
            If minScore = 9999 Then
                minScore = 0
            End If

            ' 存储统计结果
            schoolStats(j, 1) = currentGrade
            schoolStats(j, 2) = currentSchool
            schoolStats(j, 3) = totalStudents
            schoolStats(j, 4) = avgTotal
            schoolStats(j, 5) = 0 ' 排名，稍后填充
            schoolStats(j, 6) = excellentCount
            
            If totalStudents > 0 Then
                schoolStats(j, 7) = excellentCount / totalStudents * 100
            Else
                schoolStats(j, 7) = 0
            End If
            
            schoolStats(j, 8) = passCount
            
            ' 合格比例%
            If totalStudents > 0 Then
                schoolStats(j, 9) = passCount / totalStudents * 100
            Else
                schoolStats(j, 9) = 0
            End If
            
            schoolStats(j, 10) = lowCount
            ' 低分比例%
            If totalStudents > 0 Then
                schoolStats(j, 11) = lowCount / totalStudents * 100
            Else
                schoolStats(j, 11) = 0
            End If
            schoolStats(j, 12) = maxScore
            schoolStats(j, 13) = minScore
            schoolStats(j, 14) = (avgChinese + avgMath + avgEnglish + avgScience + avgMoral) / 5 ' 各科平均分
            schoolStats(j, 15) = avgChinese
            schoolStats(j, 16) = 0 ' 语文排名，稍后填充
            schoolStats(j, 17) = avgMath
            schoolStats(j, 18) = 0 ' 数学排名，稍后填充
            schoolStats(j, 19) = avgEnglish
            schoolStats(j, 20) = 0 ' 英语排名，稍后填充
            schoolStats(j, 21) = avgScience
            schoolStats(j, 22) = 0 ' 科学排名，稍后填充
            schoolStats(j, 23) = avgMoral
            schoolStats(j, 24) = 0 ' 道法排名，稍后填充
        Next j

        ' 计算全区统计 (j = schoolCount + 1)
        totalStudents = 0
        totalScore = 0
        excellentCount = 0
        passCount = 0
        lowCount = 0
        maxScore = 0
        minScore = 9999
        chineseTotal = 0
        mathTotal = 0
        englishTotal = 0
        scienceTotal = 0
        moralTotal = 0
        chineseCount = 0
        mathCount = 0
        englishCount = 0
        scienceCount = 0
        moralCount = 0
        
        ' 汇总所有学校的数据
        For j = 1 To schoolCount
            ' 累加参考人数和总分
            totalStudents = totalStudents + schoolStats(j, 3)
            If schoolStats(j, 3) > 0 Then
                totalScore = totalScore + (schoolStats(j, 4) * schoolStats(j, 3)) ' 平均分 * 人数
            End If
            
            ' 累加优秀、合格、低分人数
            excellentCount = excellentCount + schoolStats(j, 6)
            passCount = passCount + schoolStats(j, 8)
            lowCount = lowCount + schoolStats(j, 10)
            
            ' 更新最高分和最低分
            If schoolStats(j, 12) > maxScore Then
                maxScore = schoolStats(j, 12)
            End If
            
            If schoolStats(j, 13) < minScore And schoolStats(j, 13) > 0 Then
                minScore = schoolStats(j, 13)
            End If
    ' 为全区统计累加各科目总分和人数
            ' 需要从原始数据重新计算，因为我们需要知道每个科目的实际有效人数
            For k = 4 To lastRow
                If ws.Cells(k, gradeCol).Value = currentGrade Then
                    ' 语文
                    If Len(Trim(ws.Cells(k, chineseCol).Value)) > 0 And IsNumeric(ws.Cells(k, chineseCol).Value) Then
                        chineseTotal = chineseTotal + CDbl(ws.Cells(k, chineseCol).Value)
                        chineseCount = chineseCount + 1
                    End If
                    
                    ' 数学
                    If Len(Trim(ws.Cells(k, mathCol).Value)) > 0 And IsNumeric(ws.Cells(k, mathCol).Value) Then
                        mathTotal = mathTotal + CDbl(ws.Cells(k, mathCol).Value)
                        mathCount = mathCount + 1
                    End If
                    
                    ' 英语
                    If Len(Trim(ws.Cells(k, englishCol).Value)) > 0 And IsNumeric(ws.Cells(k, englishCol).Value) Then
                        englishTotal = englishTotal + CDbl(ws.Cells(k, englishCol).Value)
                        englishCount = englishCount + 1
                    End If
                    
                    ' 科学
                    If Len(Trim(ws.Cells(k, scienceCol).Value)) > 0 And IsNumeric(ws.Cells(k, scienceCol).Value) Then
                        scienceTotal = scienceTotal + CDbl(ws.Cells(k, scienceCol).Value)
                        scienceCount = scienceCount + 1
                    End If
                    
                    ' 道德与法治
                    If Len(Trim(ws.Cells(k, moralCol).Value)) > 0 And IsNumeric(ws.Cells(k, moralCol).Value) Then
                        moralTotal = moralTotal + CDbl(ws.Cells(k, moralCol).Value)
                        moralCount = moralCount + 1
                    End If
                End If
            Next k
        Next j
        
        ' 计算全区平均分
        If totalStudents > 0 Then
            avgTotal = totalScore / totalStudents
        Else
            avgTotal = 0
        End If
        
        ' 使用各科目的有效学生数计算平均分
        If chineseCount > 0 Then
            avgChinese = chineseTotal / chineseCount
        Else
            avgChinese = 0
        End If
        
        If mathCount > 0 Then
            avgMath = mathTotal / mathCount
        Else
            avgMath = 0
        End If
        
        If englishCount > 0 Then
            avgEnglish = englishTotal / englishCount
        Else
            avgEnglish = 0
        End If
        
        If scienceCount > 0 Then
            avgScience = scienceTotal / scienceCount
        Else
            avgScience = 0
        End If
        
        If moralCount > 0 Then
            avgMoral = moralTotal / moralCount
        Else
            avgMoral = 0
        End If
        
        ' 如果没有学生，最低分设为0
        If minScore = 9999 Then
            minScore = 0
        End If
        
        ' 存储全区统计结果
        schoolStats(schoolCount + 1, 1) = currentGrade
        schoolStats(schoolCount + 1, 2) = "全区"
        schoolStats(schoolCount + 1, 3) = totalStudents
        schoolStats(schoolCount + 1, 4) = avgTotal
        schoolStats(schoolCount + 1, 5) = "-" ' 全区不参与排名
        schoolStats(schoolCount + 1, 6) = excellentCount
        
        If totalStudents > 0 Then
            schoolStats(schoolCount + 1, 7) = excellentCount / totalStudents * 100
        Else
            schoolStats(schoolCount + 1, 7) = 0
        End If
        
        schoolStats(schoolCount + 1, 8) = passCount
        
        ' 合格比例%
        If totalStudents > 0 Then
            schoolStats(schoolCount + 1, 9) = passCount / totalStudents * 100
        Else
            schoolStats(schoolCount + 1, 9) = 0
        End If
        
        schoolStats(schoolCount + 1, 10) = lowCount
        ' 低分比例%
        If totalStudents > 0 Then
            schoolStats(schoolCount + 1, 11) = lowCount / totalStudents * 100
        Else
            schoolStats(schoolCount + 1, 11) = 0
        End If
        
        schoolStats(schoolCount + 1, 12) = maxScore
        schoolStats(schoolCount + 1, 13) = minScore
        schoolStats(schoolCount + 1, 14) = (avgChinese + avgMath + avgEnglish + avgScience + avgMoral) / 5 ' 各科平均分
        schoolStats(schoolCount + 1, 15) = avgChinese
        schoolStats(schoolCount + 1, 16) = "-" ' 全区不参与排名
        schoolStats(schoolCount + 1, 17) = avgMath
        schoolStats(schoolCount + 1, 18) = "-" ' 全区不参与排名
        schoolStats(schoolCount + 1, 19) = avgEnglish
        schoolStats(schoolCount + 1, 20) = "-" ' 全区不参与排名
        schoolStats(schoolCount + 1, 21) = avgScience
        schoolStats(schoolCount + 1, 22) = "-" ' 全区不参与排名
        schoolStats(schoolCount + 1, 23) = avgMoral
        schoolStats(schoolCount + 1, 24) = "-" ' 全区不参与排名

        ' 计算排名（育英学校和无参考人数的学校不参与排名）
        ' 总分排名
        For j = 1 To schoolCount
            If schoolStats(j, 2) <> "育英学校" And schoolStats(j, 3) > 0 Then
                Dim rank As Long
                rank = 1
                For k = 1 To schoolCount
                    If k <> j And schoolStats(k, 2) <> "育英学校" And schoolStats(k, 3) > 0 And schoolStats(k, 4) > schoolStats(j, 4) Then
                        rank = rank + 1
                    End If
                Next k
                schoolStats(j, 5) = rank
            Else
                schoolStats(j, 5) = "-" ' 无参考人数或育英学校标记为"-"
            End If
        Next j

        ' 语文排名
        For j = 1 To schoolCount
            If schoolStats(j, 2) <> "育英学校" And schoolStats(j, 3) > 0 Then
                rank = 1
                For k = 1 To schoolCount
                    If k <> j And schoolStats(k, 2) <> "育英学校" And schoolStats(k, 3) > 0 And schoolStats(k, 15) > schoolStats(j, 15) Then
                        rank = rank + 1
                    End If
                Next k
                schoolStats(j, 16) = rank
            Else
                schoolStats(j, 16) = "-" ' 无参考人数或育英学校标记为"-"
            End If
        Next j

        ' 数学排名
        For j = 1 To schoolCount
            If schoolStats(j, 2) <> "育英学校" And schoolStats(j, 3) > 0 Then
                rank = 1
                For k = 1 To schoolCount
                    If k <> j And schoolStats(k, 2) <> "育英学校" And schoolStats(k, 3) > 0 And schoolStats(k, 17) > schoolStats(j, 17) Then
                        rank = rank + 1
                    End If
                Next k
                schoolStats(j, 18) = rank
            Else
                schoolStats(j, 18) = "-" ' 无参考人数或育英学校标记为"-"
            End If
        Next j

        ' 英语排名
        For j = 1 To schoolCount
            If schoolStats(j, 2) <> "育英学校" And schoolStats(j, 3) > 0 Then
                rank = 1
                For k = 1 To schoolCount
                    If k <> j And schoolStats(k, 2) <> "育英学校" And schoolStats(k, 3) > 0 And schoolStats(k, 19) > schoolStats(j, 19) Then
                        rank = rank + 1
                    End If
                Next k
                schoolStats(j, 20) = rank
            Else
                schoolStats(j, 20) = "-" ' 无参考人数或育英学校标记为"-"
            End If
        Next j

        ' 科学排名
        For j = 1 To schoolCount
            If schoolStats(j, 2) <> "育英学校" And schoolStats(j, 3) > 0 Then
                rank = 1
                For k = 1 To schoolCount
                    If k <> j And schoolStats(k, 2) <> "育英学校" And schoolStats(k, 3) > 0 And schoolStats(k, 21) > schoolStats(j, 21) Then
                        rank = rank + 1
                    End If
                Next k
                schoolStats(j, 22) = rank
            Else
                schoolStats(j, 22) = "-" ' 无参考人数或育英学校标记为"-"
            End If
        Next j

        ' 道法排名
        For j = 1 To schoolCount
            If schoolStats(j, 2) <> "育英学校" And schoolStats(j, 3) > 0 Then
                rank = 1
                For k = 1 To schoolCount
                    If k <> j And schoolStats(k, 2) <> "育英学校" And schoolStats(k, 3) > 0 And schoolStats(k, 23) > schoolStats(j, 23) Then
                        rank = rank + 1
                    End If
                Next k
                schoolStats(j, 24) = rank
            Else
                schoolStats(j, 24) = "-" ' 无参考人数或育英学校标记为"-"
            End If
        Next j

        ' 输出结果
        For j = 1 To schoolCount + 1
            ' 年级
            resultWs.Cells(resultRow, 1).Value = schoolStats(j, 1)
            ' 学校
            resultWs.Cells(resultRow, 2).Value = schoolStats(j, 2)
            ' 参考人数
            resultWs.Cells(resultRow, 3).Value = schoolStats(j, 3)
            ' 总分平均分
            resultWs.Cells(resultRow, 4).Value = Round(schoolStats(j, 4), 2)
            ' 总分排名
            resultWs.Cells(resultRow, 5).Value = schoolStats(j, 5)
            ' 总分优秀人数
            resultWs.Cells(resultRow, 6).Value = schoolStats(j, 6)
            ' 总分优秀比例%
            resultWs.Cells(resultRow, 7).Value = Round(schoolStats(j, 7), 2)
            ' 总分合格人数
            resultWs.Cells(resultRow, 8).Value = schoolStats(j, 8)
            ' 总分合格比例%
            resultWs.Cells(resultRow, 9).Value = Round(schoolStats(j, 9), 2)
            ' 总分低分人数
            resultWs.Cells(resultRow, 10).Value = schoolStats(j, 10)
            ' 总分低分比例%
            resultWs.Cells(resultRow, 11).Value = Round(schoolStats(j, 11), 2)
            ' 总分最高分
            resultWs.Cells(resultRow, 12).Value = schoolStats(j, 12)
            ' 总分最低分
            resultWs.Cells(resultRow, 13).Value = schoolStats(j, 13)
            ' 各科平均分
            resultWs.Cells(resultRow, 14).Value = Round(schoolStats(j, 14), 2)
            ' 语文平均分
            resultWs.Cells(resultRow, 15).Value = Round(schoolStats(j, 15), 2)
            ' 语文平均分排名
            resultWs.Cells(resultRow, 16).Value = schoolStats(j, 16)
            ' 数学平均分
            resultWs.Cells(resultRow, 17).Value = Round(schoolStats(j, 17), 2)
            ' 数学平均分排名
            resultWs.Cells(resultRow, 18).Value = schoolStats(j, 18)
            ' 英语平均分
            resultWs.Cells(resultRow, 19).Value = Round(schoolStats(j, 19), 2)
            ' 英语平均分排名
            resultWs.Cells(resultRow, 20).Value = schoolStats(j, 20)
            ' 科学平均分
            resultWs.Cells(resultRow, 21).Value = Round(schoolStats(j, 21), 2)
            ' 科学平均分排名
            resultWs.Cells(resultRow, 22).Value = schoolStats(j, 22)
            ' 道法平均分
            resultWs.Cells(resultRow, 23).Value = Round(schoolStats(j, 23), 2)
            ' 道法平均分排名
            resultWs.Cells(resultRow, 24).Value = schoolStats(j, 24)
            
            ' 如果是全区汇总行，设置粗体和黄色背景
            If schoolStats(j, 2) = "全区" Then
                Dim rowRange As Range
                Set rowRange = resultWs.Range(resultWs.Cells(resultRow, 1), resultWs.Cells(resultRow, 24))
                rowRange.Font.Bold = True
                rowRange.Interior.Color = RGB(255, 255, 0) ' 黄色背景
            End If
            
            ' 增加行号
            resultRow = resultRow + 1
        Next j
        
        ' 在每个年级统计后添加一个空行
        resultRow = resultRow + 1
    Next i
    
' 
' 格式化结果 - 使用WPS兼容的方式
    Dim formatRange As Range
    Set formatRange = resultWs.Range(resultWs.Cells(4, 1), resultWs.Cells(resultRow - 1, 24))

    
    ' 设置百分比格式 - 使用WPS兼容的方式
    resultWs.Range(resultWs.Cells(4, 7), resultWs.Cells(resultRow - 1, 7)).NumberFormat = "0.00\%"
    resultWs.Range(resultWs.Cells(4, 9), resultWs.Cells(resultRow - 1, 9)).NumberFormat = "0.00\%"
    resultWs.Range(resultWs.Cells(4, 11), resultWs.Cells(resultRow - 1, 11)).NumberFormat = "0.00\%"
    
    MsgBox "统计完成！", vbInformation
End Sub