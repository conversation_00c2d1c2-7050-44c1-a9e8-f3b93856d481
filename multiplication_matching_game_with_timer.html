<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>乘法口诀连线游戏</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f8ff;
        }

        .game-container {
            width: 800px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            text-align: center;
        }

        h1 {
            color: #333;
        }

        .start-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 20px 0;
        }

        .start-btn:hover {
            background-color: #45a049;
        }

        .game-area {
            display: none;
            margin-top: 20px;
        }

        .columns-container {
            display: flex;
            justify-content: space-between;
            position: relative;
            min-height: 500px;
        }

        .column {
            width: 40%;
        }

        .column-spacer {
            width: 20%;
            position: relative;
        }

        .item {
            background-color: #e9f5f9;
            border: 2px solid #7fb3d5;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            cursor: pointer;
            font-size: 18px;
            position: relative;
            z-index: 2;
            width: 60%;
            margin-left: auto;
            margin-right: auto;
            text-align: center;
            box-sizing: border-box;
        }

        #expressions-column .item {
            margin-right: 0;
            margin-left: auto;
        }

        #results-column .item {
            margin-left: 0;
            margin-right: auto;
        }

        .item:hover {
            background-color: #d4e6f1;
        }

        .item.selected {
            background-color: #aed6f1;
            border-color: #3498db;
        }

        .score-display {
            font-size: 24px;
            font-weight: bold;
            margin-top: 20px;
            display: none;
        }

        .connection-line {
            position: absolute;
            height: 4px;
            background-color: #000000;
            transform-origin: left center;
            z-index: 1;
            pointer-events: none;
        }

        .connection-line.correct {
            background-color: #2ecc71;
        }

        .connection-line.incorrect {
            background-color: #e74c3c;
        }

        .connection-dot {
            position: absolute;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #000000;
            z-index: 2;
            pointer-events: none;
        }

        .connection-dot.correct {
            background-color: #2ecc71;
        }

        .connection-dot.incorrect {
            background-color: #e74c3c;
        }

        .columns-container {
            position: relative;
        }

        #connections-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        canvas {
            width: 100%;
            height: 100%;
        }

        .instructions {
            margin: 20px 0;
            color: #555;
        }

        .timer-display {
            font-size: 24px;
            font-weight: bold;
            color: #e74c3c;
            margin: 10px 0;
            display: none;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 300px;
            text-align: center;
            border-radius: 10px;
        }

        .modal-message {
            font-size: 20px;
            margin-bottom: 20px;
        }

        .modal-button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>乘法口诀连线游戏</h1>
        <div class="instructions">
            点击"开始游戏"按钮，将左侧的乘法式子与右侧的正确结果连线。连完所有线后，正确的连线显示绿色，错误的显示红色。满分100分。你有35秒的时间完成挑战！
        </div>
        <button class="start-btn" id="start-btn">开始游戏</button>

        <div class="timer-display" id="timer-display">
            剩余时间: <span id="timer">35</span> 秒
        </div>

        <div class="game-area" id="game-area">
            <div class="columns-container" id="columns-container">
                <div class="column" id="expressions-column">
                    <!-- 乘法表达式将在这里生成 -->
                </div>

                <div class="column-spacer"></div>

                <div class="column" id="results-column">
                    <!-- 结果将在这里生成 -->
                </div>

                <!-- 连线将在这里生成 -->
                <div id="connections-container"></div>
            </div>
        </div>

        <div class="score-display" id="score-display">
            得分: <span id="score">0</span>/100
        </div>
    </div>

    <!-- 失败对话框 -->
    <div id="failure-modal" class="modal">
        <div class="modal-content">
            <div class="modal-message">挑战失败，下次要努力哦！</div>
            <button class="modal-button" id="modal-close-btn">确定</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const startBtn = document.getElementById('start-btn');
            const gameArea = document.getElementById('game-area');
            const expressionsColumn = document.getElementById('expressions-column');
            const resultsColumn = document.getElementById('results-column');
            const scoreDisplay = document.getElementById('score-display');
            const scoreElement = document.getElementById('score');
            const connectionsContainer = document.getElementById('connections-container');
            const timerDisplay = document.getElementById('timer-display');
            const timerElement = document.getElementById('timer');
            const failureModal = document.getElementById('failure-modal');
            const modalCloseBtn = document.getElementById('modal-close-btn');
            const columnsContainer = document.getElementById('columns-container');

            let expressions = [];
            let results = [];
            let connections = [];
            let selectedItem = null;
            let gameCompleted = false;
            let timerInterval = null;
            let timeLeft = 35; // 35秒倒计时

            // 当窗口大小改变时重新绘制连线
            function handleResize() {
                if (connections.length > 0) {
                    drawConnections();
                }
            }

            window.addEventListener('resize', handleResize);

            // 开始倒计时
            function startTimer() {
                // 重置时间
                timeLeft = 35;
                timerElement.textContent = timeLeft;
                timerDisplay.style.display = 'block';

                // 清除之前的计时器
                if (timerInterval) {
                    clearInterval(timerInterval);
                }

                // 设置新的计时器
                timerInterval = setInterval(function() {
                    timeLeft--;
                    timerElement.textContent = timeLeft;

                    // 时间到，但游戏未完成
                    if (timeLeft <= 0 && !gameCompleted) {
                        clearInterval(timerInterval);
                        showFailureModal();
                    }
                }, 1000);
            }

            // 显示失败对话框
            function showFailureModal() {
                failureModal.style.display = 'block';
                gameCompleted = true; // 防止继续游戏
            }

            // 关闭失败对话框
            modalCloseBtn.addEventListener('click', function() {
                failureModal.style.display = 'none';
                // 重置游戏
                startBtn.textContent = '再来一次';
            });

            // 生成随机乘法表达式
            function generateExpressions() {
                expressions = [];
                results = [];

                // 生成10个不重复的乘法表达式
                while (expressions.length < 10) {
                    const a = Math.floor(Math.random() * 9) + 1;
                    const b = Math.floor(Math.random() * 9) + 1;
                    const expression = `${a} × ${b}`;
                    const result = a * b;

                    // 检查是否已存在相同的表达式或结果
                    let isDuplicate = false;
                    for (let i = 0; i < expressions.length; i++) {
                        if (expressions[i] === expression || results[i] === result) {
                            isDuplicate = true;
                            break;
                        }
                    }

                    if (!isDuplicate) {
                        expressions.push(expression);
                        results.push(result);
                    }
                }
            }

            // 打乱结果顺序
            function shuffleResults() {
                for (let i = results.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [results[i], results[j]] = [results[j], results[i]];
                }
            }

            // 渲染表达式和结果
            function renderItems() {
                expressionsColumn.innerHTML = '';
                resultsColumn.innerHTML = '';

                expressions.forEach((expression, index) => {
                    const item = document.createElement('div');
                    item.className = 'item expression';
                    item.textContent = expression;
                    item.dataset.index = index;
                    item.addEventListener('click', handleItemClick);
                    expressionsColumn.appendChild(item);
                });

                results.forEach((result, index) => {
                    const item = document.createElement('div');
                    item.className = 'item result';
                    item.textContent = result;
                    item.dataset.index = index;
                    item.addEventListener('click', handleItemClick);
                    resultsColumn.appendChild(item);
                });
            }

            // 处理点击事件
            function handleItemClick(event) {
                if (gameCompleted) return;

                const clickedItem = event.target;
                const isExpression = clickedItem.classList.contains('expression');

                // 如果没有选中项，或者选中了同一列的项，则选中当前项
                if (!selectedItem || (isExpression && selectedItem.classList.contains('expression')) ||
                    (!isExpression && selectedItem.classList.contains('result'))) {

                    // 取消之前的选中状态
                    if (selectedItem) {
                        selectedItem.classList.remove('selected');
                    }

                    // 选中当前项
                    clickedItem.classList.add('selected');
                    selectedItem = clickedItem;
                } else {
                    // 已经选中了另一列的项，尝试连线
                    const expressionItem = isExpression ? clickedItem : selectedItem;
                    const resultItem = isExpression ? selectedItem : clickedItem;

                    const expressionIndex = parseInt(expressionItem.dataset.index);
                    const resultIndex = parseInt(resultItem.dataset.index);

                    // 检查是否已经连线
                    const alreadyConnected = connections.some(conn =>
                        (conn.expressionIndex === expressionIndex) ||
                        (conn.resultIndex === resultIndex)
                    );

                    if (!alreadyConnected) {
                        // 创建新连线
                        const expressionValue = expressions[expressionIndex];
                        const [a, b] = expressionValue.split(' × ').map(Number);
                        const correctResult = a * b;
                        const selectedResult = results[resultIndex];

                        const isCorrect = correctResult === selectedResult;

                        connections.push({
                            expressionIndex,
                            resultIndex,
                            isCorrect
                        });

                        // 绘制连线
                        drawConnections();

                        // 检查游戏是否结束
                        if (connections.length === 10) {
                            endGame(true);
                        }
                    }

                    // 取消选中状态
                    selectedItem.classList.remove('selected');
                    clickedItem.classList.remove('selected');
                    selectedItem = null;
                }
            }

            // 绘制所有连线
            function drawConnections() {
                // 清除现有的连线
                connectionsContainer.innerHTML = '';

                // 获取列容器的位置信息
                const containerRect = columnsContainer.getBoundingClientRect();

                connections.forEach((connection, index) => {
                    const expressionItem = expressionsColumn.children[connection.expressionIndex];
                    const resultItem = resultsColumn.children[connection.resultIndex];

                    // 获取元素的位置
                    const expressionRect = expressionItem.getBoundingClientRect();
                    const resultRect = resultItem.getBoundingClientRect();

                    // 计算相对于容器的位置
                    const startX = expressionRect.right - containerRect.left;
                    const startY = expressionRect.top + expressionRect.height/2 - containerRect.top;
                    const endX = resultRect.left - containerRect.left;
                    const endY = resultRect.top + resultRect.height/2 - containerRect.top;

                    // 计算连线长度和角度
                    const length = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
                    const angle = Math.atan2(endY - startY, endX - startX) * 180 / Math.PI;

                    // 创建连线元素
                    const line = document.createElement('div');
                    line.className = 'connection-line';
                    line.id = `line-${index}`;
                    line.style.width = `${length}px`;
                    line.style.left = `${startX}px`;
                    line.style.top = `${startY}px`;
                    line.style.transform = `rotate(${angle}deg)`;

                    // 如果游戏完成，添加颜色类
                    if (gameCompleted) {
                        if (connection.isCorrect) {
                            line.classList.add('correct');
                        } else {
                            line.classList.add('incorrect');
                        }
                    }

                    // 创建左侧圆点
                    const leftDot = document.createElement('div');
                    leftDot.className = 'connection-dot';
                    leftDot.style.left = `${startX - 5}px`;
                    leftDot.style.top = `${startY - 5}px`;

                    // 创建右侧圆点
                    const rightDot = document.createElement('div');
                    rightDot.className = 'connection-dot';
                    rightDot.style.left = `${endX - 5}px`;
                    rightDot.style.top = `${endY - 5}px`;

                    // 如果游戏完成，添加颜色类
                    if (gameCompleted) {
                        if (connection.isCorrect) {
                            leftDot.classList.add('correct');
                            rightDot.classList.add('correct');
                        } else {
                            leftDot.classList.add('incorrect');
                            rightDot.classList.add('incorrect');
                        }
                    }

                    // 添加到容器
                    connectionsContainer.appendChild(line);
                    connectionsContainer.appendChild(leftDot);
                    connectionsContainer.appendChild(rightDot);
                });
            }

            // 结束游戏并计算得分
            function endGame(success = true) {
                gameCompleted = true;

                // 停止计时器
                if (timerInterval) {
                    clearInterval(timerInterval);
                }

                if (success) {
                    // 计算得分 (满分100分)
                    const correctConnections = connections.filter(conn => conn.isCorrect).length;
                    const score = correctConnections * 10; // 每个正确连线10分
                    scoreElement.textContent = score;
                    scoreDisplay.style.display = 'block';

                    // 重新绘制连线，显示正确和错误的颜色
                    drawConnections();
                }

                // 修改开始按钮为重新开始
                startBtn.textContent = '再来一次';
            }

            // 开始游戏
            function startGame() {
                // 重置游戏状态
                connections = [];
                selectedItem = null;
                gameCompleted = false;
                scoreDisplay.style.display = 'none';

                // 隐藏失败对话框
                failureModal.style.display = 'none';

                // 清除现有的连线
                connectionsContainer.innerHTML = '';

                // 生成新的表达式和结果
                generateExpressions();
                shuffleResults();
                renderItems();

                // 显示游戏区域
                gameArea.style.display = 'block';

                // 开始倒计时
                startTimer();
            }

            // 绑定开始按钮事件
            startBtn.addEventListener('click', startGame);
        });
    </script>
</body>
</html>
