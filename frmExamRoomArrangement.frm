VERSION 5.00
Begin {C62A69F0-16DC-11CE-9E98-00AA00574A4F} frmExamRoomArrangement 
   Caption         =   "考场编排设置"
   ClientHeight    =   3015
   ClientLeft      =   120
   ClientTop       =   465
   ClientWidth     =   4560
   OleObjectBlob   =   "frmExamRoomArrangement.frx":0000
   StartUpPosition =   1  '所有者中心
End
Attribute VB_Name = "frmExamRoomArrangement"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = False
Option Explicit

Private Sub UserForm_Initialize()
    ' 初始化单选按钮
    optRoomSize25.Value = True
    optRoomSize30.Value = False
End Sub

Private Sub btnArrange_Click()
    Dim roomSize As Integer
    
    ' 获取选择的考场容量
    If optRoomSize25.Value Then
        roomSize = 25
    Else
        roomSize = 30
    End If
    
    ' 隐藏窗体
    Me.Hide
    
    ' 执行考场编排
    Call 执行考场编排(roomSize)
    
    ' 卸载窗体
    Unload Me
End Sub

Private Sub btnCancel_Click()
    ' 取消操作，卸载窗体
    Unload Me
End Sub
