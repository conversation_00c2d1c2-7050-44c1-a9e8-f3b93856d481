Sub 区级统计()
    
    Dim QP(1 To 5000, 1 To 27)
    Dim arr, brr, crr, H, k, m, n, o, p, x, y, z <PERSON> Integer, sr, sr1, sr2, l As String

    sh = Array("数学", "英语", "科学", "道德与法治", "音乐", "体育与健康", "美术", "信息科技", "劳动", "综合实践", "地方课程", "校本课程", "语文")
    o = Sheets("成绩汇总表").Range("G65536").End(xlUp).Row
    Sheets("成绩汇总表").Select
    Range("A1:W" & o).Select
        ActiveWorkbook.Worksheets("成绩汇总表").Sort.SortFields.Clear
        ActiveWorkbook.Worksheets("成绩汇总表").Sort.SortFields.Add Key:=Range("D1:D" & o), _
            SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
        ActiveWorkbook.Worksheets("成绩汇总表").Sort.SortFields.Add Key:=Range("A1:A" & o), _
            SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
        ActiveWorkbook.Worksheets("成绩汇总表").Sort.SortFields.Add Key:=Range("C1:C" & o), _
            SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
        ActiveWorkbook.Worksheets("成绩汇总表").Sort.SortFields.Add Key:=Range("E1:E" & o), _
            SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
     With ActiveWorkbook.Worksheets("成绩汇总表").Sort
        .SetRange Range("A1:W" & o)
        .Header = xlYes
        .MatchCase = False
        .Orientation = xlTopToBottom
        .SortMethod = xlPinYin
        .Apply
    End With

    arr = Sheet2.Range("A1:W" & o).Value
    Application.ScreenUpdating = False '关闭系统提示
    Sheets("成绩汇总表").Select
    Sheets("区统计").Activate
    
    For i = 1 To o
            If arr(i, 4) = Sheets("区统计").Cells(1, 3).Value Then j = j + 1
    Next
    
    ReDim brr(1 To j, 1 To 7)
    
    l = Sheets("区统计").Cells(1, 7).Value
    j = Application.Match(l, sh, 0) + 9
    Sheet4.Activate
    Rows("7:2000").Delete Shift:=xlUp
    For i = 2 To UBound(arr)
    
        If arr(i, 4) = Sheet4.Cells(1, 3).Value Then
        f = f + 1
                m = m + 1
                brr(m, 1) = arr(i, 1)
                brr(m, 2) = arr(i, 2)
                brr(m, 3) = arr(i, 3)
                brr(m, 4) = arr(i, 5)
                brr(m, 5) = arr(i, 6)
                brr(m, 6) = arr(i, 7)
                brr(m, 7) = arr(i, j)
        End If
    Next
    Set d = CreateObject("Scripting.Dictionary")
         For y = 0 To 2
            For x = 1 To UBound(brr)
                sr = brr(x, 2) & "-" & brr(x, 3) & "-" & brr(x, 4)
                sr1 = brr(x, 2) & "-" & brr(x, 3)
                sr2 = brr(x, 2)
                crr = Array(sr, sr1, sr2)
                      
                     If d.Exists(crr(y)) Then
                              H = d(crr(y)) '行
                              m = m + 1   '计数
                              d(s) = m    '给 学校-校点－班 编号
                             QP(H, 1) = brr(x, 1)
                             QP(H, 2) = brr(x, 2)
                             QP(H, 3) = brr(x, 3)
                             QP(H, 4) = brr(x, 4)
                              If brr(x, 7) > 0 And brr(x, 7) <> "" Then
                                QP(H, 5) = QP(H, 5) + 1
                                QP(H, 6) = QP(H, 6) + brr(x, 7)  '总分
                              Else
                              
                              End If
                              If brr(x, 7) > QP(H, 13) And brr(x, 7) <> "" Then QP(H, 13) = brr(x, 7)
                              If brr(x, 7) < QP(H, 14) And brr(x, 7) > 0 And brr(x, 7) <> "" Then QP(H, 14) = brr(x, 7)
                              If brr(x, 7) < 40 And brr(x, 7) > 0 And brr(x, 7) <> "" Then QP(H, 15) = QP(H, 15) + 1
                              If brr(x, 7) >= 40 And brr(x, 7) < 50 Then QP(H, 16) = QP(H, 16) + 1
                              If brr(x, 7) >= 50 And brr(x, 7) < 60 Then QP(H, 17) = QP(H, 17) + 1
                              If brr(x, 7) >= 60 And brr(x, 7) < 70 Then QP(H, 18) = QP(H, 18) + 1
                              If brr(x, 7) >= 70 And brr(x, 7) < 80 Then QP(H, 19) = QP(H, 19) + 1
                              If brr(x, 7) >= 80 And brr(x, 7) < 90 Then QP(H, 20) = QP(H, 20) + 1
                              If brr(x, 7) >= 90 And brr(x, 7) < 100 Then QP(H, 21) = QP(H, 21) + 1
                              If brr(x, 7) = 100 And brr(x, 7) <> "" Then QP(H, 22) = QP(H, 22) + 1
                                QP(H, 7) = QP(H, 15) + QP(H, 16) + QP(H, 17)              '低分数
                                QP(H, 9) = QP(H, 20) + QP(H, 21) + QP(H, 22)              '良好数
                                QP(H, 11) = QP(H, 21) + QP(H, 22)                         '优秀数
                                QP(H, 23) = QP(H, 21) + QP(H, 22)                 'A
                                QP(H, 24) = QP(H, 20)                             'B
                                QP(H, 25) = QP(H, 19)                             'C
                                QP(H, 26) = QP(H, 18)                             'D
                                QP(H, 27) = QP(H, 15) + QP(H, 16) + QP(H, 17)     'E
                     Else
                             k = k + 1
                             d(crr(y)) = k
                             QP(k, 1) = brr(x, 1)
                             QP(k, 2) = brr(x, 2)
                             QP(k, 3) = brr(x, 3)
                             QP(k, 4) = brr(x, 4)
                             If brr(x, 7) > 0 And brr(x, 7) <> "" Then
                                QP(k, 5) = 1
                                QP(k, 6) = brr(x, 7)
                             Else
                             
                             End If
                                If brr(x, 7) <> "" Then QP(k, 13) = brr(x, 7)
                                If brr(x, 7) <> "" Then QP(k, 14) = brr(x, 7)   ' brr(x, 7) > 0 And
                                If brr(x, 7) < 40 And brr(x, 7) > 0 And brr(x, 7) <> "" Then QP(k, 15) = 1
                                If brr(x, 7) >= 40 And brr(x, 7) < 50 Then QP(k, 16) = 1
                                If brr(x, 7) >= 50 And brr(x, 7) < 60 Then QP(k, 17) = 1
                                If brr(x, 7) >= 60 And brr(x, 7) < 70 Then QP(k, 18) = 1
                                If brr(x, 7) >= 70 And brr(x, 7) < 80 Then QP(k, 19) = 1
                                If brr(x, 7) >= 80 And brr(x, 7) < 90 Then QP(k, 20) = 1
                                If brr(x, 7) >= 90 And brr(x, 7) < 100 Then QP(k, 21) = 1
                                If brr(x, 7) = 100 And brr(x, 7) <> "" Then QP(k, 22) = 1
                                QP(k, 7) = QP(k, 15) + QP(k, 16) + QP(k, 17)              '低分数
                                QP(k, 9) = QP(k, 20) + QP(k, 21) + QP(k, 22)              '良好数
                                QP(k, 11) = QP(k, 21) + QP(k, 22)                         '优秀数
                                QP(k, 23) = QP(k, 21) + QP(k, 22)                 'A
                                QP(k, 24) = QP(k, 20)                             'B
                                QP(k, 25) = QP(k, 19)                             'C
                                QP(k, 26) = QP(k, 18)                             'D
                                QP(k, 27) = QP(k, 15) + QP(k, 16) + QP(k, 17)     'E
                              
                    '序号 学校 校点 班级 实考 平均 低分 低分% 良好   良好% 优秀 优秀% 最高分  最低分
                    '1    2     3    4    5    6    7     8    9      10    11   12     13      14
                    '0-39 40-49 50-59 60-69 70-79 80-89 90-99 100   A   B    C   D   E
                    ' 15    16   17    18    19    20    21   22    23  24   25  26  27
                             
                     End If
                 If y = 1 Then
                        QP(k, 1) = brr(x, 1)
                        QP(k, 2) = brr(x, 2)
                        QP(k, 3) = brr(x, 3)
                        If QP(k, 2) = QP(k, 3) Then
                             QP(k, 4) = QP(k, 2) & "合计"
                        Else
                             QP(k, 4) = brr(x, 3) & "小计"
                        End If
                        
                 ElseIf y = 2 Then
                         QP(k, 1) = brr(x, 1)
                         QP(k, 2) = brr(x, 2)
                         QP(k, 3) = ""
                         QP(k, 4) = QP(k, 2) & "合计"
                 End If
                 
            Next x
                For n = z + 1 To d(crr(y))
                    QP(n, 7) = QP(n, 15) + QP(n, 16) + QP(n, 17)              '低分数
                    QP(n, 9) = QP(n, 20) + QP(n, 21) + QP(n, 22)              '良好数
                    QP(n, 11) = QP(n, 21) + QP(n, 22)                         '优秀数
                    QP(n, 23) = QP(n, 21) + QP(n, 22)                 'A
                    QP(n, 24) = QP(n, 20)                             'B
                    QP(n, 25) = QP(n, 19)                             'C
                    QP(n, 26) = QP(n, 18)                             'D
                    QP(n, 27) = QP(n, 15) + QP(n, 16) + QP(n, 17)     'E
                    z = d(crr(y))
                Next n
        Next y
                     
              k = k + 1
              

                For x = 1 To UBound(brr)
                  If brr(x, 1) < 5 Then
                    QP(k, 1) = ""
                    QP(k, 2) = ""
                    QP(k, 3) = ""
                    QP(k, 4) = "城区合计"
                    If brr(x, 7) > 0 And brr(x, 7) <> "" Then
                        QP(k, 5) = QP(k, 5) + 1
                        QP(k, 6) = QP(k, 6) + brr(x, 7)  '总分
                    Else
                              
                    End If
                    
                    If brr(x, 7) > QP(k, 13) And brr(x, 7) <> "" Then QP(k, 13) = brr(x, 7)
                    If QP(k, 14) = "" And brr(x, 7) > 0 And brr(x, 7) <> "" Then QP(k, 14) = brr(x, 7)
                    If brr(x, 7) < QP(k, 14) And brr(x, 7) > 0 And brr(x, 7) <> "" Then QP(k, 14) = brr(x, 7)          '
                    If brr(x, 7) < 40 And brr(x, 7) > 0 And brr(x, 7) <> "" Then QP(k, 15) = QP(k, 15) + 1
                    If brr(x, 7) >= 40 And brr(x, 7) < 50 Then QP(k, 16) = QP(k, 16) + 1
                    If brr(x, 7) >= 50 And brr(x, 7) < 60 Then QP(k, 17) = QP(k, 17) + 1
                    If brr(x, 7) >= 60 And brr(x, 7) < 70 Then QP(k, 18) = QP(k, 18) + 1
                    If brr(x, 7) >= 70 And brr(x, 7) < 80 Then QP(k, 19) = QP(k, 19) + 1
                    If brr(x, 7) >= 80 And brr(x, 7) < 90 Then QP(k, 20) = QP(k, 20) + 1
                    If brr(x, 7) >= 90 And brr(x, 7) < 100 Then QP(k, 21) = QP(k, 21) + 1
                    If brr(x, 7) = 100 And brr(x, 7) <> "" Then QP(k, 22) = QP(k, 22) + 1
                    QP(k, 7) = QP(k, 15) + QP(k, 16) + QP(k, 17)              '低分数
                    QP(k, 9) = QP(k, 20) + QP(k, 21) + QP(k, 22)              '良好数
                    QP(k, 11) = QP(k, 21) + QP(k, 22)                         '优秀数
                    QP(k, 23) = QP(k, 21) + QP(k, 22)                 'A
                    QP(k, 24) = QP(k, 20)                             'B
                    QP(k, 25) = QP(k, 19)                             'C
                    QP(k, 26) = QP(k, 18)                             'D
                    QP(k, 27) = QP(k, 15) + QP(k, 16) + QP(k, 17)     'E
                  End If
                Next x
                     
              k = k + 2
              

                For x = 1 To UBound(brr)
                  If brr(x, 1) > 4 Then
                    QP(k, 1) = ""
                    QP(k, 2) = ""
                    QP(k, 3) = ""
                    QP(k, 4) = "乡镇合计"
                    If brr(x, 7) > 0 And brr(x, 7) <> "" Then
                        QP(k, 5) = QP(k, 5) + 1
                        QP(k, 6) = QP(k, 6) + brr(x, 7)  '总分
                    Else
                              
                    End If
                    
                    If brr(x, 7) > QP(k, 13) And brr(x, 7) <> "" Then QP(k, 13) = brr(x, 7)
                    If QP(k, 14) = "" And brr(x, 7) > 0 And brr(x, 7) <> "" Then QP(k, 14) = brr(x, 7)
                    If brr(x, 7) < QP(k, 14) And brr(x, 7) > 0 And brr(x, 7) <> "" Then QP(k, 14) = brr(x, 7)          '
                    If brr(x, 7) < 40 And brr(x, 7) > 0 And brr(x, 7) <> "" Then QP(k, 15) = QP(k, 15) + 1
                    If brr(x, 7) >= 40 And brr(x, 7) < 50 Then QP(k, 16) = QP(k, 16) + 1
                    If brr(x, 7) >= 50 And brr(x, 7) < 60 Then QP(k, 17) = QP(k, 17) + 1
                    If brr(x, 7) >= 60 And brr(x, 7) < 70 Then QP(k, 18) = QP(k, 18) + 1
                    If brr(x, 7) >= 70 And brr(x, 7) < 80 Then QP(k, 19) = QP(k, 19) + 1
                    If brr(x, 7) >= 80 And brr(x, 7) < 90 Then QP(k, 20) = QP(k, 20) + 1
                    If brr(x, 7) >= 90 And brr(x, 7) < 100 Then QP(k, 21) = QP(k, 21) + 1
                    If brr(x, 7) = 100 And brr(x, 7) <> "" Then QP(k, 22) = QP(k, 22) + 1
                    QP(k, 7) = QP(k, 15) + QP(k, 16) + QP(k, 17)              '低分数
                    QP(k, 9) = QP(k, 20) + QP(k, 21) + QP(k, 22)              '良好数
                    QP(k, 11) = QP(k, 21) + QP(k, 22)                         '优秀数
                    QP(k, 23) = QP(k, 21) + QP(k, 22)                 'A
                    QP(k, 24) = QP(k, 20)                             'B
                    QP(k, 25) = QP(k, 19)                             'C
                    QP(k, 26) = QP(k, 18)                             'D
                    QP(k, 27) = QP(k, 15) + QP(k, 16) + QP(k, 17)     'E
                  End If
                Next x
                     
                     
                     k = k + 3
                For x = 1 To UBound(brr)
                    QP(k, 1) = ""
                    QP(k, 2) = ""
                    QP(k, 3) = ""
                    QP(k, 4) = "总计"
                    If brr(x, 7) > 0 And brr(x, 7) <> "" Then
                        QP(k, 5) = QP(k, 5) + 1
                        QP(k, 6) = QP(k, 6) + brr(x, 7)  '总分
                    Else
                              
                    End If
                    
                    If brr(x, 7) > QP(k, 13) And brr(x, 7) <> "" Then QP(k, 13) = brr(x, 7)
                    If QP(k, 14) = "" And brr(x, 7) > 0 And brr(x, 7) <> "" Then QP(k, 14) = brr(x, 7)
                    If brr(x, 7) < QP(k, 14) And brr(x, 7) > 0 And brr(x, 7) <> "" Then QP(k, 14) = brr(x, 7)          '
                    If brr(x, 7) < 40 And brr(x, 7) > 0 And brr(x, 7) <> "" Then QP(k, 15) = QP(k, 15) + 1
                    If brr(x, 7) >= 40 And brr(x, 7) < 50 Then QP(k, 16) = QP(k, 16) + 1
                    If brr(x, 7) >= 50 And brr(x, 7) < 60 Then QP(k, 17) = QP(k, 17) + 1
                    If brr(x, 7) >= 60 And brr(x, 7) < 70 Then QP(k, 18) = QP(k, 18) + 1
                    If brr(x, 7) >= 70 And brr(x, 7) < 80 Then QP(k, 19) = QP(k, 19) + 1
                    If brr(x, 7) >= 80 And brr(x, 7) < 90 Then QP(k, 20) = QP(k, 20) + 1
                    If brr(x, 7) >= 90 And brr(x, 7) < 100 Then QP(k, 21) = QP(k, 21) + 1
                    If brr(x, 7) = 100 And brr(x, 7) <> "" Then QP(k, 22) = QP(k, 22) + 1
                    QP(k, 7) = QP(k, 15) + QP(k, 16) + QP(k, 17)              '低分数
                    QP(k, 9) = QP(k, 20) + QP(k, 21) + QP(k, 22)              '良好数
                    QP(k, 11) = QP(k, 21) + QP(k, 22)                         '优秀数
                    QP(k, 23) = QP(k, 21) + QP(k, 22)                 'A
                    QP(k, 24) = QP(k, 20)                             'B
                    QP(k, 25) = QP(k, 19)                             'C
                    QP(k, 26) = QP(k, 18)                             'D
                    QP(k, 27) = QP(k, 15) + QP(k, 16) + QP(k, 17)     'E
                    
                Next x

               Set d = Nothing
                      Sheets("区统计").Range("B6").Resize(500, 27) = QP
                    x = Sheets("区统计").Range("F65536").End(xlUp).Row
                    For p = 6 To x
                        If Cells(p, 6) > 0 Then
                            Cells(p, 7) = Cells(p, 7) / Cells(p, 6)
                            Cells(p, 9) = Cells(p, 8) / Cells(p, 6) * 100
                            Cells(p, 11) = Cells(p, 10) / Cells(p, 6) * 100
                            Cells(p, 13) = Cells(p, 12) / Cells(p, 6) * 100
                        End If
                    Next p
                    Range("B6:AB6").Select '格式化表格
                    Selection.Copy
                    Range("B7:AB" & x).Select
                    Selection.PasteSpecial Paste:=xlPasteFormats, Operation:=xlNone, SkipBlanks:=False, Transpose:=False
                    Application.CutCopyMode = False
                    
                    
            '↓↓↓↓↓↓排序、删除重复、校点只有一个班也不总计
                    Range("B5:AB" & x).Select
                        ActiveWorkbook.Worksheets("区统计").Sort.SortFields.Clear
                        ActiveWorkbook.Worksheets("区统计").Sort.SortFields.Add Key:=Range("B3:B300"), _
                            SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
                        ActiveWorkbook.Worksheets("区统计").Sort.SortFields.Add Key:=Range("D3:D300"), _
                            SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
                        ActiveWorkbook.Worksheets("区统计").Sort.SortFields.Add Key:=Range("E3:E300"), _
                            SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal

                    With ActiveWorkbook.Worksheets("区统计").Sort
                        .SetRange Range("B5:AB300")
                        .Header = xlYes
                        .MatchCase = False
                        .Orientation = xlTopToBottom
                        .SortMethod = xlPinYin
                        .Apply
                    End With
                    '删除非必要的多余总计行
                    For n = Range("e500").End(xlUp).Row To 6 Step -1
                    
                        If Application.WorksheetFunction.Sum(Range("F" & n & ":" & "AB" & n)) = Application.WorksheetFunction.Sum(Range("F" & (n - 1) & ":" & "AB" & (n - 1))) Then
                                
                            If Cells(n, 5).Value Like "*小计" Then
                                Worksheets("区统计").Rows(n).Delete Shift:=xlUp
                            Else
                                Worksheets("区统计").Rows(n - 1).Delete Shift:=xlUp
                            End If
                        End If
                    Next n
                        '格式化表格
                    
                    Range("B6:AC6").Select
                    Selection.Copy
                    Range("B7:AC" & x).Select
                    Selection.PasteSpecial Paste:=xlPasteFormats, Operation:=xlNone, SkipBlanks:=False, Transpose:=False
                    Application.CutCopyMode = False
                    Cells(6, 1).Select
                    Selection.AutoFill Destination:=Range("A6:A" & x)
                    Cells(6, 29).Select
                    Selection.AutoFill Destination:=Range("AC6:AC" & x)
                    
                    For p = 6 To x
                         If Cells(p, 5).Value Like "*小计" Or Cells(p, 4).Value Like "*小计" Then
                                Range("B" & p & ":" & "AC" & p).Select
                                With Selection.Interior
                                    .Pattern = xlSolid
                                    .PatternColorIndex = xlAutomatic
                                    .Color = 9419919
                                    .TintAndShade = 0
                                    .PatternTintAndShade = 0
                                End With
                                Cells(p, 1) = ""
                            End If
                         
                         
                         If Cells(p, 5).Value Like "*合计" Or Cells(p, 5).Value Like "*总计" Then
                                Range("B" & p & ":" & "AC" & p).Select
                                With Selection.Interior
                                    .Pattern = xlSolid
                                    .PatternColorIndex = xlAutomatic
                                    .Color = 65535
                                    .TintAndShade = 0
                                    .PatternTintAndShade = 0
                                End With
                                Application.DisplayAlerts = False
                                Cells(p, 1) = ""
                          End If
                          If Cells(p, 5).Value Like "*计" Then
                            Cells(p, 4) = Cells(p, 5)
                            Range("D" & p & ":E" & p).Merge
                          End If
                    Next p
           x = Sheets("区统计").Range("F65536").End(xlUp).Row
                    Rows(x + 1 & ":2000").Delete Shift:=xlUp
            '↑↑↑↑↑↑格式化表格 Selection.Merge
               Application.ScreenUpdating = True
End Sub
