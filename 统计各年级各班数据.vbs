Sub 统计班级平均分及排名()
    Dim wb As Workbook
    Dim ws成绩 As Worksheet, ws教师 As Worksheet, ws统计 As Worksheet
    Dim lastRow As Long, i As Long
    Dim dict班级 As Object, dict教师 As Object
    Dim arr成绩(), arr教师(), arr输出()
    Dim startTime As Double
    Dim j As Integer
    Dim classKeys() As Variant
    Dim allGrades As Object
    Dim currentGrade As Variant
    Dim gradeKeys As Variant
    Dim currentOutputRow As Long
    Dim classData As Variant ' Declare classData here for wider scope if needed or keep local

    ' 记录开始时间
    startTime = Timer
    
    ' 初始化字典对象
    Set allGrades = CreateObject("Scripting.Dictionary")
    
    ' 设置工作表对象
    Set wb = ThisWorkbook
    On Error Resume Next
    Set ws成绩 = wb.Sheets("成绩汇总表")
    Set ws教师 = wb.Sheets("任课教师")
    Set ws统计 = wb.Sheets("各年级各班统计")
    
    ' 创建或获取统计工作表
    If ws统计 Is Nothing Then
        Set ws统计 = wb.Sheets.Add(After:=wb.Sheets(wb.Sheets.Count))
        ws统计.Name = "各年级各班统计"
    Else
'        ws统计.Cells.Clear ' 根据需要取消注释以清除旧数据
    End If
    On Error GoTo 0
    
    ' 检查工作表是否存在
    If ws成绩 Is Nothing Then MsgBox "找不到工作表: 成绩汇总表", vbExclamation: Exit Sub
    If ws教师 Is Nothing Then MsgBox "找不到工作表: 任课教师", vbExclamation: Exit Sub
    ' ws统计 已经处理过，无需再次检查

    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    
    ' 设置统计表标题
    ws统计.Cells.Clear ' 清除旧数据，确保从干净的表开始
    ws统计.Range("A1").Value = "年级各班统计表"
    With ws统计.Range("A1")
        .Font.Size = 14
        .Font.Bold = True
        .HorizontalAlignment = xlCenter
    End With
    ws统计.Range("A1:W1").Merge ' 假设标题合并到W列
    
    ' 表头信息 (可以根据实际输出列数调整)
    Dim headers As Variant
    headers = Array("序号", "年级", "学校", "校点", "班级", "参考人数", _
                    "语文平均", "语文排名", "语文教师", _
                    "数学平均", "数学排名", "数学教师", _
                    "英语平均", "英语排名", "英语教师", _
                    "科学平均", "科学排名", "科学教师", _
                    "道法平均", "道法排名", "道法教师", _
                    "总分平均", "总分排名", "备注") ' 24列
    ws统计.Range("A3").Resize(1, UBound(headers) + 1).Value = headers
    With ws统计.Range("A3").Resize(1, UBound(headers) + 1)
        .Font.Bold = True
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .WrapText = True
    End With

    ' 从成绩表中获取所有年级 (假设年级在D列，即arr成绩的第4列)
    lastRow = ws成绩.Cells(ws成绩.Rows.Count, 4).End(xlUp).Row ' 基准列改为年级列
    If lastRow < 2 Then MsgBox "成绩汇总表年级列没有数据", vbInformation: GoTo CleanUp
    
    For i = 2 To lastRow ' 假设数据从第2行开始
        currentGrade = Trim(ws成绩.Cells(i, 4).Value) ' D列为年级
        If currentGrade <> "" And Not allGrades.Exists(currentGrade) Then
            allGrades.Add currentGrade, currentGrade
        End If
    Next i
    
    If allGrades.Count = 0 Then MsgBox "未找到任何年级数据", vbExclamation: GoTo CleanUp
    
    gradeKeys = allGrades.Keys
    currentOutputRow = 4 ' 数据从第4行开始输出 (A3是表头)
    
    ' 为每个年级创建统计
    For Each currentGrade In gradeKeys
        Set dict班级 = CreateObject("Scripting.Dictionary")
        Set dict教师 = CreateObject("Scripting.Dictionary")
        
        ' 1. 读取任课教师数据 (假设年级在教师表的C列)
        lastRow = ws教师.Cells(ws教师.Rows.Count, 1).End(xlUp).Row
        If lastRow > 1 Then
            arr教师 = ws教师.Range("A1:R" & lastRow).Value ' 假设教师信息到R列
            For i = 2 To UBound(arr教师, 1)
                If Trim(arr教师(i, 3)) = currentGrade Then ' C列为年级
                    Dim teacherKey As String
                    ' 教师字典的键：学校|校点|班级 (A列学校, B列校点, D列班级)
                    teacherKey = Trim(arr教师(i, 1)) & "|" & Trim(arr教师(i, 2)) & "|" & Trim(arr教师(i, 4))
                    
                    If Not dict教师.Exists(teacherKey) Then
                        Dim teacherInfo(1 To 5) As String
                        teacherInfo(1) = arr教师(i, 6)   '语文教师 (F列)
                        teacherInfo(2) = arr教师(i, 7)   '数学教师 (G列)
                        teacherInfo(3) = arr教师(i, 8)   '英语教师 (H列)
                        teacherInfo(4) = arr教师(i, 9)   '科学教师 (I列)
                        teacherInfo(5) = arr教师(i, 10)  '道法教师 (J列)
                        dict教师.Add teacherKey, teacherInfo
                    End If
                End If
            Next i
        End If
        
        ' 2. 统计成绩数据
        ' 假设成绩汇总表: A列-ID, B列-学校, C列-校点, D列-年级, E列-班级, ... , I列-语文, J列-数学, K列-英语, L列-科学, M列-道法, ... , W列-总分
        lastRow = ws成绩.Cells(ws成绩.Rows.Count, 4).End(xlUp).Row ' 以年级列为基准获取行数
        If lastRow < 2 Then GoTo NextGrade ' 如果该年级无数据则跳过 (理论上不会，因为年级是从这里来的)
        
        arr成绩 = ws成绩.Range("A1:W" & lastRow).Value ' 读取到W列即可
        
        For i = 2 To UBound(arr成绩, 1)
            If Trim(arr成绩(i, 4)) = currentGrade Then ' D列为年级
                Dim classKey As String
                ' 班级字典的键：学校|校点|班级 (B列学校, C列校点, E列班级)
                classKey = Trim(arr成绩(i, 2)) & "|" & Trim(arr成绩(i, 3)) & "|" & Trim(arr成绩(i, 5))
                
                If Not dict班级.Exists(classKey) Then
                    Dim classInfo(1 To 15) As Variant ' 扩展到15个元素
                    ' 初始化统计项
                    classInfo(1) = arr成绩(i, 2)   '学校 (B列)
                    classInfo(2) = arr成绩(i, 3)   '校点 (C列)
                    classInfo(3) = arr成绩(i, 5)   '班级 (E列)
                    classInfo(4) = 0               '参考人数 (基于W列)
                    classInfo(5) = 0               '语文总分
                    classInfo(6) = 0               '数学总分
                    classInfo(7) = 0               '英语总分
                    classInfo(8) = 0               '科学总分
                    classInfo(9) = 0               '道法总分
                    classInfo(10) = 0              '语文参考人数
                    classInfo(11) = 0              '数学参考人数
                    classInfo(12) = 0              '英语参考人数
                    classInfo(13) = 0              '科学参考人数
                    classInfo(14) = 0              '道法参考人数
                    classInfo(15) = 0              'W列总分累计 (新)
                    dict班级.Add classKey, classInfo
                End If
                
                ' 获取当前班级数据进行更新
                classData = dict班级(classKey)
                
                ' 统计W列总分和人数 (W列是arr成绩的第23列)
                Dim scoreW As Double
                If IsNumeric(arr成绩(i, 23)) And Trim(CStr(arr成绩(i, 23))) <> "" Then
                    scoreW = CDbl(arr成绩(i, 23))
                    If scoreW > 0 Then ' 仅当W列分数大于0时计入
                        classData(4) = classData(4) + 1         ' W列有效人数
                        classData(15) = classData(15) + scoreW  ' W列总分累计
                    End If
                End If
                
                ' 统计各科成绩 (假设成绩在 I列到M列)
                If IsNumeric(arr成绩(i, 9)) And Trim(CStr(arr成绩(i, 9))) <> "" And CDbl(arr成绩(i, 9)) > 0 Then  '语文 (I列)
                    classData(5) = classData(5) + CDbl(arr成绩(i, 9))
                    classData(10) = classData(10) + 1
                End If
                If IsNumeric(arr成绩(i, 10)) And Trim(CStr(arr成绩(i, 10))) <> "" And CDbl(arr成绩(i, 10)) > 0 Then '数学 (J列)
                    classData(6) = classData(6) + CDbl(arr成绩(i, 10))
                    classData(11) = classData(11) + 1
                End If
                If IsNumeric(arr成绩(i, 11)) And Trim(CStr(arr成绩(i, 11))) <> "" And CDbl(arr成绩(i, 11)) > 0 Then '英语 (K列)
                    classData(7) = classData(7) + CDbl(arr成绩(i, 11))
                    classData(12) = classData(12) + 1
                End If
                If IsNumeric(arr成绩(i, 12)) And Trim(CStr(arr成绩(i, 12))) <> "" And CDbl(arr成绩(i, 12)) > 0 Then '科学 (L列)
                    classData(8) = classData(8) + CDbl(arr成绩(i, 12))
                    classData(13) = classData(13) + 1
                End If
                If IsNumeric(arr成绩(i, 13)) And Trim(CStr(arr成绩(i, 13))) <> "" And CDbl(arr成绩(i, 13)) > 0 Then '道法 (M列)
                    classData(9) = classData(9) + CDbl(arr成绩(i, 13))
                    classData(14) = classData(14) + 1
                End If
                
                dict班级(classKey) = classData '写回字典
            End If
        Next i
        
        If dict班级.Count = 0 Then GoTo NextGrade ' 如果该年级没有班级数据
        
        classKeys = dict班级.Keys
        
        ' 3. 计算全区统计数据
        Dim arr全区合计(1 To 15) As Variant ' 扩展到15
        For i = 0 To UBound(classKeys)
            classData = dict班级(classKeys(i))
            For j = 4 To 14  ' 累加原有项 (classData(4)已经是W列人数)
                arr全区合计(j) = arr全区合计(j) + classData(j)
            Next
            arr全区合计(15) = arr全区合计(15) + classData(15) ' 累加W列总分
        Next i
    
        ReDim arr输出(1 To dict班级.Count + 1, 1 To 24) ' 输出数组24列
        Dim classIndex As Long: classIndex = 0
    
        ' 添加全区统计行
        classIndex = 1
        arr输出(classIndex, 1) = ""     '序号
        arr输出(classIndex, 2) = currentGrade  '年级
        arr输出(classIndex, 3) = ""     '学校
        arr输出(classIndex, 4) = "全区"  '校点
        arr输出(classIndex, 5) = "合计" '班级
        arr输出(classIndex, 6) = arr全区合计(4) '全区参考人数 (基于W列)
        
        ' 各科平均分
        If arr全区合计(10) > 0 Then arr输出(classIndex, 7) = Round(arr全区合计(5) / arr全区合计(10), 2) Else arr输出(classIndex, 7) = 0 '语文
        If arr全区合计(11) > 0 Then arr输出(classIndex, 10) = Round(arr全区合计(6) / arr全区合计(11), 2) Else arr输出(classIndex, 10) = 0 '数学
        If arr全区合计(12) > 0 Then arr输出(classIndex, 13) = Round(arr全区合计(7) / arr全区合计(12), 2) Else arr输出(classIndex, 13) = 0 '英语
        If arr全区合计(13) > 0 Then arr输出(classIndex, 16) = Round(arr全区合计(8) / arr全区合计(13), 2) Else arr输出(classIndex, 16) = 0 '科学
        If arr全区合计(14) > 0 Then arr输出(classIndex, 19) = Round(arr全区合计(9) / arr全区合计(14), 2) Else arr输出(classIndex, 19) = 0 '道法
        
        ' 总分平均 (V列，即arr输出的第22列) - 使用W列数据
        If arr全区合计(4) > 0 Then ' arr全区合计(4) 是W列人数
            arr输出(classIndex, 22) = Round(arr全区合计(15) / arr全区合计(4), 2) ' W列总分 / W列人数
        Else
            arr输出(classIndex, 22) = 0
        End If
        arr输出(classIndex, 24) = "合计" ' 备注列标记为合计，不参与排名
    
        ' 填充各班级数据
        For i = 0 To UBound(classKeys)
            classIndex = classIndex + 1 ' 全区合计占了第一行，所以班级从第二行开始
            classData = dict班级(classKeys(i))
            
            arr输出(classIndex, 1) = classIndex - 1 '序号
            arr输出(classIndex, 2) = currentGrade   '年级
            arr输出(classIndex, 3) = classData(1)   '学校
            arr输出(classIndex, 4) = classData(2)   '校点
            arr输出(classIndex, 5) = classData(3)   '班级
            arr输出(classIndex, 6) = classData(4)   '参考人数 (基于W列)
            
            ' 各科平均分
            If classData(10) > 0 Then arr输出(classIndex, 7) = Round(classData(5) / classData(10), 2) Else arr输出(classIndex, 7) = 0
            If classData(11) > 0 Then arr输出(classIndex, 10) = Round(classData(6) / classData(11), 2) Else arr输出(classIndex, 10) = 0
            If classData(12) > 0 Then arr输出(classIndex, 13) = Round(classData(7) / classData(12), 2) Else arr输出(classIndex, 13) = 0
            If classData(13) > 0 Then arr输出(classIndex, 16) = Round(classData(8) / classData(13), 2) Else arr输出(classIndex, 16) = 0
            If classData(14) > 0 Then arr输出(classIndex, 19) = Round(classData(9) / classData(14), 2) Else arr输出(classIndex, 19) = 0
            
            ' 总分平均 (V列，即arr输出的第22列) - 使用W列数据
            If classData(4) > 0 Then ' classData(4) 是W列人数
                arr输出(classIndex, 22) = Round(classData(15) / classData(4), 2) ' W列总分 / W列人数
            Else
                arr输出(classIndex, 22) = 0
            End If
            
            ' 添加任课教师信息
            Dim teacherInfoKey As String
            teacherInfoKey = classData(1) & "|" & classData(2) & "|" & classData(3) '学校|校点|班级
            If dict教师.Exists(teacherInfoKey) Then
                Dim teacherData As Variant: teacherData = dict教师(teacherInfoKey)
                arr输出(classIndex, 9) = teacherData(1)  '语文教师
                arr输出(classIndex, 12) = teacherData(2) '数学教师
                arr输出(classIndex, 15) = teacherData(3) '英语教师
                arr输出(classIndex, 18) = teacherData(4) '科学教师
                arr输出(classIndex, 21) = teacherData(5) '道法教师
            End If

            ' 标记育英学校不参与排名 (如果需要)
            If InStr(classData(1), "育英学校") > 0 Then ' 假设学校名在 classData(1)
                 arr输出(classIndex, 24) = "不排名"
            Else
                 arr输出(classIndex, 24) = "" ' 清空备注，参与排名
            End If
        Next i
        
        ' 4. 计算排名 (arr输出, 平均分列号, 排名列号, 跳过标记1, 跳过标记2)
        ' 跳过标记现在统一使用 arr输出(i, 24) 的内容
        Call 计算排名(arr输出, 7, 8, "合计", "不排名")    ' 语文排名
        Call 计算排名(arr输出, 10, 11, "合计", "不排名")   ' 数学排名
        Call 计算排名(arr输出, 13, 14, "合计", "不排名")   ' 英语排名
        Call 计算排名(arr输出, 16, 17, "合计", "不排名")   ' 科学排名
        Call 计算排名(arr输出, 19, 20, "合计", "不排名")   ' 道法排名
        Call 计算排名(arr输出, 22, 23, "合计", "不排名")   ' 总分排名 (总分平均在22列，排名在23列)
        
        ' 5. 输出到统计工作表
        ws统计.Range("A" & currentOutputRow).Resize(UBound(arr输出, 1), UBound(arr输出, 2)).Value = arr输出
        
        ' 设置全区行格式
        With ws统计.Range("A" & currentOutputRow & ":" & Cells(currentOutputRow, UBound(arr输出, 2)).Address)
            .Interior.Color = RGB(255, 255, 0) ' 黄色背景
            .Font.Bold = True
        End With
        
        ' 设置表格边框
        With ws统计.Range("A" & currentOutputRow).Resize(UBound(arr输出, 1), UBound(arr输出, 2)).Borders
            .LineStyle = xlContinuous
            .Weight = xlThin
        End With
        
        currentOutputRow = currentOutputRow + UBound(arr输出, 1) + 1 ' 加1行空行分隔不同年级
        
NextGrade:
    Next currentGrade
    
    ws统计.Columns("A:X").AutoFit ' 自动调整列宽到X列 (24列)
    
CleanUp:
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    If Err.Number <> 0 Then
        MsgBox "处理过程中出现错误: " & Err.Description, vbCritical
    Else
        If allGrades.Count > 0 And currentOutputRow > 4 Then
             MsgBox "已完成所有年级的统计，共处理 " & allGrades.Count & " 个年级，耗时: " & Round(Timer - startTime, 2) & "秒", vbInformation
        ElseIf allGrades.Count = 0 Then
             ' MsgBox "未找到任何年级数据，无法统计。", vbExclamation ' 此消息已在前面处理
        Else
             MsgBox "未生成任何统计数据。", vbInformation
        End If
    End If
    
    Set dict班级 = Nothing
    Set dict教师 = Nothing
    Set allGrades = Nothing
    Set ws成绩 = Nothing
    Set ws教师 = Nothing
    Set ws统计 = Nothing
    Set wb = Nothing
End Sub

' 计算排名的辅助函数 (确保备注列是第24列)
Sub 计算排名(ByRef arr() As Variant, ByVal scoreCol As Integer, ByVal rankCol As Integer, ByVal skipTag1 As String, ByVal skipTag2 As String)
    Dim i As Long, j As Long
    Dim rowCount As Long
    
    rowCount = UBound(arr, 1)
    If rowCount = 0 Then Exit Sub ' 空数组则退出
    
    ' 临时存储分数和原始行号以进行排序和处理并列排名
    Dim sortableArray() As Variant
    ReDim sortableArray(1 To rowCount, 1 To 3) ' 1: Score, 2: Original Index in arr, 3: Rank
    Dim validCount As Long: validCount = 0

    ' 填充用于排序的数组
    For i = 1 To rowCount
        ' 检查是否跳过排名 (基于第24列的备注)
        If arr(i, 24) <> skipTag1 And arr(i, 24) <> skipTag2 And IsNumeric(arr(i, scoreCol)) Then
            validCount = validCount + 1
            sortableArray(validCount, 1) = CDbl(arr(i, scoreCol))
            sortableArray(validCount, 2) = i ' 存储原始arr中的行索引
        Else
            arr(i, rankCol) = "-" ' 不参与排名的直接标记
        End If
    Next i

    If validCount = 0 Then Exit Sub ' 没有可排序的数据

    ' 对有效数据进行降序排序 (冒泡排序)
    Dim tempScore As Double, tempIndex As Long
    For i = 1 To validCount - 1
        For j = i + 1 To validCount
            If sortableArray(i, 1) < sortableArray(j, 1) Then
                tempScore = sortableArray(i, 1)
                sortableArray(i, 1) = sortableArray(j, 1)
                sortableArray(j, 1) = tempScore
                
                tempIndex = sortableArray(i, 2)
                sortableArray(i, 2) = sortableArray(j, 2)
                sortableArray(j, 2) = tempIndex
            End If
        Next j
    Next i

    ' 计算排名 (处理并列)
    Dim currentRank As Long: currentRank = 0
    Dim lastScore As Double: lastScore = -1 ' 初始化为不可能的分数

    For i = 1 To validCount
        If sortableArray(i, 1) <> lastScore Then
            currentRank = i
            lastScore = sortableArray(i, 1)
        End If
        sortableArray(i, 3) = currentRank ' 在临时数组中记录排名
    Next i
    
    ' 将排名写回原始arr数组
    For i = 1 To validCount
        arr(sortableArray(i, 2), rankCol) = sortableArray(i, 3)
    Next i
End Sub

