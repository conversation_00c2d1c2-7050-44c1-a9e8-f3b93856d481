'------------------------------------------------------------------------------
' 辅助函数：计算科目排名
' 功能：根据班级平均分计算排名并填充到班级成绩报表中
' 参数：
'   - ws班级成绩报表：班级成绩报表工作表
'   - 班级得分数组：存储班级平均分和行号的数组
'   - 科目索引：当前处理的科目索引
'   - 班级数量：班级总数
'   - 排名列号：排名要填充的列号
'------------------------------------------------------------------------------
Sub 计算科目排名(ws班级成绩报表 As Worksheet, 班级得分数组 As Variant, 科目索引 As <PERSON>, 班级数量 As <PERSON>, 排名列号 As Long)
    Dim i As <PERSON>, j <PERSON> Long
    Dim 临时得分 As Double, 临时行号 As Long, 临时标识 As String
    Dim 排名数组() As Long
    
    ' 如果班级数量小于1，则无需排名
    If 班级数量 < 1 Then Exit Sub
    
    ' 初始化排名数组
    ReDim 排名数组(1 To 班级数量)
    
    ' 对班级得分数组按平均分降序排序（冒泡排序）
    On Error Resume Next ' 添加错误处理，防止数组访问越界
    For i = 1 To 班级数量 - 1
        For j = i + 1 To 班级数量
            If 班级得分数组(科目索引, i, 1) < 班级得分数组(科目索引, j, 1) Then
                ' 交换平均分
                临时得分 = 班级得分数组(科目索引, i, 1)
                班级得分数组(科目索引, i, 1) = 班级得分数组(科目索引, j, 1)
                班级得分数组(科目索引, j, 1) = 临时得分
                
                ' 交换行号
                临时行号 = 班级得分数组(科目索引, i, 2)
                班级得分数组(科目索引, i, 2) = 班级得分数组(科目索引, j, 2)
                班级得分数组(科目索引, j, 2) = 临时行号
                
                ' 交换标识
                临时标识 = 班级得分数组(科目索引, i, 3)
                班级得分数组(科目索引, i, 3) = 班级得分数组(科目索引, j, 3)
                班级得分数组(科目索引, j, 3) = 临时标识
            End If
        Next j
    Next i
    On Error GoTo 0 ' 恢复错误处理
    
    ' 计算排名（处理并列情况）
    Dim 当前排名 As Long, 当前得分 As Double
    当前排名 = 0
    当前得分 = -1  ' 初始化为不可能的得分值
    
    For i = 1 To 班级数量
        ' 如果当前得分与前一个不同，排名为当前位置
        If 班级得分数组(科目索引, i, 1) <> 当前得分 Then
            当前排名 = i
            当前得分 = 班级得分数组(科目索引, i, 1)
        End If
        
        ' 记录排名
        排名数组(i) = 当前排名
    Next i
    
    ' 填充排名到班级成绩报表
    On Error Resume Next ' 添加错误处理，防止单元格访问错误
    For i = 1 To 班级数量
        ' 获取行号
        Dim 行号 As Long
        行号 = 班级得分数组(科目索引, i, 2)
        
        ' 填充排名
        If 行号 > 0 Then
            ws班级成绩报表.Cells(行号, 排名列号).Value = 排名数组(i)
        End If
    Next i
    On Error GoTo 0 ' 恢复错误处理
End Sub