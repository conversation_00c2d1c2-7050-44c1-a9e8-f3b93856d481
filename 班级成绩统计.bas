
Option Explicit

Sub 班级成绩统计()
    Dim ws成绩表 As Worksheet
    Dim ws教师表 As Worksheet
    Dim ws统计表 As Worksheet
    Dim lastRow As Long
    Dim i <PERSON>, j <PERSON> Long
    Dim currentClass As String
    Dim dict As Object
    
    ' 设置引用工作表
    Set ws成绩表 = ThisWorkbook.Sheets("成绩汇总表")
    Set ws教师表 = ThisWorkbook.Sheets("任课教师")
    
    ' 创建或获取统计表
    On Error Resume Next
    Set ws统计表 = ThisWorkbook.Sheets("班级统计")
    On Error GoTo 0
    If ws统计表 Is Nothing Then
        Set ws统计表 = ThisWorkbook.Sheets.Add
        ws统计表.Name = "班级统计"
    End If
    
    ' 清空统计表
    ws统计表.Cells.Clear
    
    ' 设置表头
    With ws统计表
        .Cells(1, 1) = "序号"
        .Cells(1, 2) = "学校"
        .Cells(1, 3) = "校点"
        .Cells(1, 4) = "班级"
        .Cells(1, 5) = "实考人数"
        .Cells(1, 6) = "语文"
        .Cells(1, 7) = "语文年级名次"
        .Cells(1, 8) = "语文任课教师"
        .Cells(1, 9) = "数学"
        .Cells(1, 10) = "数学年级名次"
        .Cells(1, 11) = "数学任课教师"
        .Cells(1, 12) = "英语"
        .Cells(1, 13) = "英语年级名次"
        .Cells(1, 14) = "英语任课教师"
        .Cells(1, 15) = "科学"
        .Cells(1, 16) = "科学年级名次"
        .Cells(1, 17) = "科学任课教师"
        .Cells(1, 18) = "道法"
        .Cells(1, 19) = "道法年级名次"
        .Cells(1, 20) = "道法任课教师"
        .Cells(1, 21) = "总分"
        .Cells(1, 22) = "总分年级名次"
    End With
    
    ' 获取最后一行
    lastRow = ws成绩表.Cells(ws成绩表.Rows.Count, "A").End(xlUp).Row
    
    ' 创建字典对象用于存储班级数据
    Set dict = CreateObject("Scripting.Dictionary")
    
    ' 收集班级数据
    For i = 2 To lastRow
        currentClass = ws成绩表.Cells(i, "E").Value ' 假设班级在E列
        If Not dict.exists(currentClass) Then
            dict.Add currentClass, CreateObject("Scripting.Dictionary")
            With dict(currentClass)
                .Add "count", 0
                .Add "语文", 0
                .Add "数学", 0
                .Add "英语", 0
                .Add "科学", 0
                .Add "道法", 0
                .Add "总分", 0
                .Add "学校", ws成绩表.Cells(i, "B").Value
                .Add "校点", ws成绩表.Cells(i, "C").Value
            End With
        End If
        
        ' 累加各科成绩
        With dict(currentClass)
            .Item("count") = .Item("count") + 1
            .Item("语文") = .Item("语文") + Val(ws成绩表.Cells(i, "H").Value)
            .Item("数学") = .Item("数学") + Val(ws成绩表.Cells(i, "I").Value)
            .Item("英语") = .Item("英语") + Val(ws成绩表.Cells(i, "J").Value)
            .Item("科学") = .Item("科学") + Val(ws成绩表.Cells(i, "K").Value)
            .Item("道法") = .Item("道法") + Val(ws成绩表.Cells(i, "L").Value)
            .Item("总分") = .Item("总分") + Val(ws成绩表.Cells(i, "Q").Value)
        End With
    Next i
    
    ' 写入统计数据
    j = 2
    For Each currentClass In dict.keys
        With ws统计表
            .Cells(j, 1) = j - 1 ' 序号
            .Cells(j, 2) = dict(currentClass)("学校")
            .Cells(j, 3) = dict(currentClass)("校点")
            .Cells(j, 4) = currentClass
            .Cells(j, 5) = dict(currentClass)("count")
            
            ' 计算平均分
            .Cells(j, 6) = Round(dict(currentClass)("语文") / dict(currentClass)("count"), 2)
            .Cells(j, 9) = Round(dict(currentClass)("数学") / dict(currentClass)("count"), 2)
            .Cells(j, 12) = Round(dict(currentClass)("英语") / dict(currentClass)("count"), 2)
            .Cells(j, 15) = Round(dict(currentClass)("科学") / dict(currentClass)("count"), 2)
            .Cells(j, 18) = Round(dict(currentClass)("道法") / dict(currentClass)("count"), 2)
            .Cells(j, 21) = Round(dict(currentClass)("总分") / dict(currentClass)("count"), 2)
        End With
        
        ' 获取任课教师
        Dim teacherRow As Range
        Set teacherRow = 查找教师行(ws教师表, dict(currentClass)("学校"), dict(currentClass)("校点"), currentClass)
        
        If Not teacherRow Is Nothing Then
            ws统计表.Cells(j, 8) = teacherRow.Cells(1, 6)  ' 语文教师
            ws统计表.Cells(j, 11) = teacherRow.Cells(1, 7) ' 数学教师
            ws统计表.Cells(j, 14) = teacherRow.Cells(1, 8) ' 英语教师
            ws统计表.Cells(j, 17) = teacherRow.Cells(1, 9) ' 科学教师
            ws统计表.Cells(j, 20) = teacherRow.Cells(1, 10) ' 道法教师
        End If
        
        j = j + 1
    Next currentClass
    
    ' 计算年级名次
    计算年级名次 ws统计表, 6, 7  ' 语文
    计算年级名次 ws统计表, 9, 10 ' 数学
    计算年级名次 ws统计表, 12, 13 ' 英语
    计算年级名次 ws统计表, 15, 16 ' 科学
    计算年级名次 ws统计表, 18, 19 ' 道法
    计算年级名次 ws统计表, 21, 22 ' 总分
    
    ' 格式化表格
    With ws统计表.Range(ws统计表.Cells(1, 1), ws统计表.Cells(j - 1, 22))
        .Borders.LineStyle = xlContinuous
        .Font.Name = "宋体"
        .Font.Size = 10
        With .Rows(1)
            .Font.Bold = True
            .HorizontalAlignment = xlCenter
        End With
    End With
    
    ws统计表.Columns.AutoFit
    
    MsgBox "统计完成！"
End Sub

Function 查找教师行(ws As Worksheet, school As String, point As String, class As String) As Range
    Dim lastRow As Long
    Dim i As Long
    
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    
    For i = 2 To lastRow
        If ws.Cells(i, 1).Value = school And _
           ws.Cells(i, 2).Value = point And _
           ws.Cells(i, 4).Value = class Then
            Set 查找教师行 = ws.Rows(i)
            Exit Function
        End If
    Next i
    
    Set 查找教师行 = Nothing
End Function

Sub 计算年级名次(ws As Worksheet, scoreCol As Long, rankCol As Long)
    Dim lastRow As Long
    Dim scores() As Double
    Dim i As Long, n As Long
    
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    If lastRow <= 1 Then Exit Sub
    
    n = lastRow - 1
    ReDim scores(1 To n, 1 To 2)
    
    ' 收集成绩
    For i = 1 To n
        scores(i, 1) = ws.Cells(i + 1, scoreCol).Value
        scores(i, 2) = i + 1
    Next i
    
    ' 排序（冒泡排序）
    Dim j As Long
    Dim temp As Double
    For i = 1 To n - 1
        For j = 1 To n - i
            If scores(j, 1) < scores(j + 1, 1) Then
                temp = scores(j, 1)
                scores(j, 1) = scores(j + 1, 1)
                scores(j + 1, 1) = temp
                
                temp = scores(j, 2)
                scores(j, 2) = scores(j + 1, 2)
                scores(j + 1, 2) = temp
            End If
        Next j
    Next i
    
    ' 写入名次
    For i = 1 To n
        ws.Cells(scores(i, 2), rankCol).Value = i
    Next i
End Sub