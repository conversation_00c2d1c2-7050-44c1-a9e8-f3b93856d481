<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>乘法口诀连线游戏</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 20px;
        }
        .container {
            display: flex;
            justify-content: space-between; /* 调整间距 */
            margin: 20px 0;
        }
        .questions, .answers {
            display: flex;
            flex-direction: column;
            gap: 5px; /* 减小间距 */
        }
        .item {
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            cursor: pointer;
            user-select: none;
            font-size: 18px; /* 增大字体 */
        }
        .item.selected {
            background-color: #e0e0e0;
        }
        .line {
            position: absolute;
            background-color: black;
            height: 2px;
            transform-origin: left center;
            z-index: -1;
        }
        .correct {
            background-color: #a0e0a0;
        }
        .incorrect {
            background-color: #e0a0a0;
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            margin: 10px;
            cursor: pointer;
        }
        .score {
            font-size: 20px;
            font-weight: bold;
            margin: 20px;
        }
        .timer {
            font-size: 20px;
            font-weight: bold;
            margin: 20px;
        }
    </style>
</head>
<body>
    <h1>乘法口诀连线游戏</h1>
    <button id="startBtn">开始游戏</button>
    <div class="score" id="score">得分: 0/100</div>
    <div class="timer" id="timer">时间: 35秒</div>
    <div class="container">
        <div class="questions" id="questions"></div>
        <div class="answers" id="answers"></div>
    </div>

    <script>
        const questionsDiv = document.getElementById('questions');
        const answersDiv = document.getElementById('answers');
        const startBtn = document.getElementById('startBtn');
        const scoreDiv = document.getElementById('score');
        const timerDiv = document.getElementById('timer');

        let questions = [];
        let answers = [];
        let selectedItem = null;
        let lines = [];
        let correctPairs = [];
        let score = 0;
        let timeLeft = 35;
        let timerInterval;
        let connectedPairs = [];

        function generateQuestions() {
            questions = [];
            answers = [];
            correctPairs = [];
            connectedPairs = [];

            // 确保生成的乘法式子不重复
            const usedPairs = new Set();

            while (correctPairs.length < 10) {
                const a = Math.floor(Math.random() * 9) + 1;
                const b = Math.floor(Math.random() * 9) + 1;
                const question = `${a} × ${b} = ?`;
                const answer = a * b;

                if (!usedPairs.has(question)) {
                    usedPairs.add(question);
                    questions.push(question);
                    answers.push(answer);
                    correctPairs.push({ question, answer });
                }
            }

            shuffleArray(questions);
            shuffleArray(answers);

            questionsDiv.innerHTML = '';
            answersDiv.innerHTML = '';
            document.querySelectorAll('.line').forEach(line => line.remove());
            lines = [];

            questions.forEach((q, index) => {
                const questionElement = document.createElement('div');
                questionElement.className = 'item question';
                questionElement.textContent = q;
                questionElement.dataset.index = index;
                questionElement.addEventListener('click', handleQuestionClick);
                questionsDiv.appendChild(questionElement);
            });

            answers.forEach((a, index) => {
                const answerElement = document.createElement('div');
                answerElement.className = 'item answer';
                answerElement.textContent = a;
                answerElement.dataset.index = index;
                answerElement.addEventListener('click', handleAnswerClick);
                answersDiv.appendChild(answerElement);
            });

            score = 0;
            timeLeft = 35;
            updateScore();
            updateTimer();
            startTimer();
        }

        function shuffleArray(array) {
            for (let i = array.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [array[i], array[j]] = [array[j], array[i]];
            }
        }

        function handleQuestionClick(e) {
            const clickedItem = e.target;

            if (selectedItem && selectedItem.classList.contains('question')) {
                selectedItem.classList.remove('selected');
            }

            clickedItem.classList.add('selected');
            selectedItem = clickedItem;
        }

        function handleAnswerClick(e) {
            if (!selectedItem || !selectedItem.classList.contains('question')) return;

            const clickedAnswer = e.target;
            const questionIndex = parseInt(selectedItem.dataset.index);
            const answerIndex = parseInt(clickedAnswer.dataset.index);

            const questionText = questions[questionIndex];
            const answerValue = answers[answerIndex];

            const isCorrect = correctPairs.some(pair =>
                pair.question === questionText && pair.answer === answerValue
            );

            drawLine(selectedItem, clickedAnswer, isCorrect);

            if (isCorrect) {
                score += 10;
                connectedPairs.push({ questionIndex, answerIndex });
            }

            updateScore();

            selectedItem.classList.remove('selected');
            selectedItem = null;

            // 检查是否所有连线已完成
            if (connectedPairs.length === 10) {
                autoScore();
            }
        }

        function drawLine(startElement, endElement, isCorrect) {
            const startRect = startElement.getBoundingClientRect();
            const endRect = endElement.getBoundingClientRect();

            const startX = startRect.right + window.scrollX;
            const startY = startRect.top + window.scrollY + startRect.height / 2;
            const endX = endRect.left + window.scrollX;
            const endY = endRect.top + window.scrollY + endRect.height / 2;

            const length = Math.sqrt(Math.pow(endX - startX, 2) + Math.pow(endY - startY, 2));
            const angle = Math.atan2(endY - startY, endX - startX) * 180 / Math.PI;

            const line = document.createElement('div');
            line.className = `line ${isCorrect ? 'correct' : 'incorrect'}`;
            line.style.left = `${startX}px`;
            line.style.top = `${startY}px`;
            line.style.width = `${length}px`;
            line.style.transform = `rotate(${angle}deg)`;

            document.body.appendChild(line);
            lines.push(line);
        }

        function updateScore() {
            scoreDiv.textContent = `得分: ${score}/100`;
        }

        function updateTimer() {
            timerDiv.textContent = `时间: ${timeLeft}秒`;
        }

        function startTimer() {
            clearInterval(timerInterval);
            timerInterval = setInterval(() => {
                if (timeLeft <= 0) {
                    clearInterval(timerInterval);
                    autoScore();
                } else {
                    timeLeft--;
                    updateTimer();
                }
            }, 1000);
        }

        function autoScore() {
            clearInterval(timerInterval);
            let totalScore = 0;
            connectedPairs.forEach(pair => {
                const questionText = questions[pair.questionIndex];
                const answerValue = answers[pair.answerIndex];
                const isCorrect = correctPairs.some(cp =>
                    cp.question === questionText && cp.answer === answerValue
                );
                if (isCorrect) {
                    totalScore += 10;
                }
            });
            score = totalScore;
            updateScore();
            alert(`时间到！您的得分是: ${score}`);
        }

        startBtn.addEventListener('click', generateQuestions);
    </script>
</body>
</html>