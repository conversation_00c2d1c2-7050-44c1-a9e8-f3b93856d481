Sub 学业分数段统计()
    ' Main procedure to orchestrate the entire process
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    Application.EnableEvents = False

    On Error GoTo ErrorHandler

    CalculateScores
    GenerateStatistics

    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Application.EnableEvents = True

    MsgBox "处理完成！", vbInformation, "成功"
    Exit Sub

ErrorHandler:
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Application.EnableEvents = True
    MsgBox "处理过程中发生错误: " & Err.Description, vbCritical, "错误"
End Sub

Sub CalculateScores()
    ' Calculate total scores and weighted scores in "成绩总表"
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim totalScore As Double
    Dim weightedScore As Double

    ' Get the "成绩总表" worksheet
    On Error Resume Next
    Set ws = ThisWorkbook.Worksheets("成绩总表")
    On Error GoTo 0

    If ws Is Nothing Then
        MsgBox "找不到'成绩总表'工作表！", vbExclamation, "错误"
        Exit Sub
    End If

    ' Find the last row with data
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

    ' Find column indices for subjects and scores
    Dim colYuwen As Integer, colShuxue As Integer, colYingyu As Integer
    Dim colDaofa As Integer, colShengwu As Integer, colDili As Integer
    Dim colXinxi As Integer, colLishi As Integer, colWuli As Integer
    Dim colHuaxue As Integer, colTotal As Integer, colWeighted As Integer

    colYuwen = 0: colShuxue = 0: colYingyu = 0: colDaofa = 0: colShengwu = 0
    colDili = 0: colXinxi = 0: colLishi = 0: colWuli = 0: colHuaxue = 0
    colTotal = 0: colWeighted = 0

    ' Find column indices (assuming headers are in row 1)
    For i = 1 To ws.Cells(1, ws.Columns.Count).End(xlToLeft).Column
        Select Case ws.Cells(1, i).value
            Case "语文": colYuwen = i
            Case "数学": colShuxue = i
            Case "英语": colYingyu = i
            Case "道法": colDaofa = i
            Case "生物": colShengwu = i
            Case "地理": colDili = i
            Case "信息": colXinxi = i
            Case "历史": colLishi = i
            Case "物理": colWuli = i
            Case "化学": colHuaxue = i
            Case "总分": colTotal = i
            Case "折分": colWeighted = i
        End Select
    Next i

    ' Variables for weighted score calculation
    Dim yuwen As Double, shuxue As Double, yingyu As Double
    Dim daofa As Double, shengwu As Double, lishi As Double
    Dim dili As Double, huaxue As Double, wuli As Double

    ' Calculate scores for each student
    For i = 2 To lastRow ' Assuming data starts from row 2
        ' Calculate total score
        totalScore = 0
        If Not IsEmpty(ws.Cells(i, colYuwen).value) Then totalScore = totalScore + ws.Cells(i, colYuwen).value
        If Not IsEmpty(ws.Cells(i, colShuxue).value) Then totalScore = totalScore + ws.Cells(i, colShuxue).value
        If Not IsEmpty(ws.Cells(i, colYingyu).value) Then totalScore = totalScore + ws.Cells(i, colYingyu).value
        If Not IsEmpty(ws.Cells(i, colDaofa).value) Then totalScore = totalScore + ws.Cells(i, colDaofa).value
        If Not IsEmpty(ws.Cells(i, colShengwu).value) Then totalScore = totalScore + ws.Cells(i, colShengwu).value
        If Not IsEmpty(ws.Cells(i, colDili).value) Then totalScore = totalScore + ws.Cells(i, colDili).value
        If Not IsEmpty(ws.Cells(i, colXinxi).value) Then totalScore = totalScore + ws.Cells(i, colXinxi).value
        If Not IsEmpty(ws.Cells(i, colLishi).value) Then totalScore = totalScore + ws.Cells(i, colLishi).value
        If Not IsEmpty(ws.Cells(i, colWuli).value) Then totalScore = totalScore + ws.Cells(i, colWuli).value
        If Not IsEmpty(ws.Cells(i, colHuaxue).value) Then totalScore = totalScore + ws.Cells(i, colHuaxue).value

        ' Calculate weighted score
        weightedScore = 0

        yuwen = IIf(IsEmpty(ws.Cells(i, colYuwen).value), 0, ws.Cells(i, colYuwen).value)
        shuxue = IIf(IsEmpty(ws.Cells(i, colShuxue).value), 0, ws.Cells(i, colShuxue).value)
        yingyu = IIf(IsEmpty(ws.Cells(i, colYingyu).value), 0, ws.Cells(i, colYingyu).value)
        daofa = IIf(IsEmpty(ws.Cells(i, colDaofa).value), 0, ws.Cells(i, colDaofa).value)
        shengwu = IIf(IsEmpty(ws.Cells(i, colShengwu).value), 0, ws.Cells(i, colShengwu).value)
        lishi = IIf(IsEmpty(ws.Cells(i, colLishi).value), 0, ws.Cells(i, colLishi).value)
        dili = IIf(IsEmpty(ws.Cells(i, colDili).value), 0, ws.Cells(i, colDili).value)
        huaxue = IIf(IsEmpty(ws.Cells(i, colHuaxue).value), 0, ws.Cells(i, colHuaxue).value)
        wuli = IIf(IsEmpty(ws.Cells(i, colWuli).value), 0, ws.Cells(i, colWuli).value)

        weightedScore = yuwen + shuxue + yingyu + (daofa + shengwu + lishi) * 0.4 + (dili + huaxue) * 0.3 + wuli * 0.5

        ' Update the cells
        If totalScore > 0 Then
            ws.Cells(i, colTotal).value = totalScore
            ws.Cells(i, colWeighted).value = Round(weightedScore, 2)
        Else
            ws.Cells(i, colTotal).value = ""
            ws.Cells(i, colWeighted).value = ""
        End If
    Next i
End Sub

Sub GenerateStatistics()
    ' Generate statistics in "学业分段统计" based on score ranges
    Dim wsScores As Worksheet
    Dim wsStats As Worksheet
    Dim lastRow As Long, lastRowStats As Long
    Dim i As Long, j As Long
    Dim schoolDict As Object
    Dim schoolList() As String
    Dim schoolCount As Integer
    Dim schoolRow As Long
    Dim targetGrade As String

    ' Create a dictionary to store school information
    Set schoolDict = CreateObject("Scripting.Dictionary")

    ' Get the worksheets
    On Error Resume Next
    Set wsScores = ThisWorkbook.Worksheets("成绩总表")
    Set wsStats = ThisWorkbook.Worksheets("学业分段统计")
    On Error GoTo 0

    If wsScores Is Nothing Then
        MsgBox "找不到'成绩总表'工作表！", vbExclamation, "错误"
        Exit Sub
    End If

    If wsStats Is Nothing Then
        MsgBox "找不到'学业分段统计'工作表！", vbExclamation, "错误"
        Exit Sub
    End If

    ' Find the last row with data in both sheets
    lastRow = wsScores.Cells(wsScores.Rows.Count, 1).End(xlUp).Row
    lastRowStats = 5 ' Statistics start from row 5

    ' Get target grade from cell D2 in the statistics sheet
    targetGrade = wsStats.Range("D2").value
    If Trim(targetGrade) = "" Then
        MsgBox "请在D2单元格输入要查询的年级！", vbExclamation, "错误"
        Exit Sub
    End If

    ' Find column indices in the scores sheet
    Dim colGrade As Integer, colSchoolId As Integer, colSchool As Integer, colClass As Integer, colWeighted As Integer, colName As Integer, colTotal As Integer
    colGrade = 0: colSchoolId = 0: colSchool = 0: colClass = 0: colWeighted = 0: colName = 0: colTotal = 0

    ' Find column indices in the scores sheet (assuming headers are in row 1)
    For i = 1 To wsScores.Cells(1, wsScores.Columns.Count).End(xlToLeft).Column
        Select Case wsScores.Cells(1, i).value
            Case "年级": colGrade = i
            Case "学校序号": colSchoolId = i
            Case "学校": colSchool = i
            Case "班级": colClass = i
            Case "折分": colWeighted = i
            Case "姓名": colName = i
            Case "总分": colTotal = i
        End Select
    Next i

    ' Clear existing data and formatting in the statistics sheet (from row 5)
    wsStats.Rows("5:" & wsStats.Rows.Count).ClearContents
    wsStats.Rows("5:" & wsStats.Rows.Count).ClearFormats

    ' Collect unique school and class combinations for the target grade
    Dim schoolId As String, school As String, class As String, grade As String
    Dim key As String

    For i = 2 To lastRow ' Assuming data starts from row 2
        grade = wsScores.Cells(i, colGrade).value

        ' Only process rows with matching grade
        If grade = targetGrade Then
            schoolId = wsScores.Cells(i, colSchoolId).value
            school = wsScores.Cells(i, colSchool).value
            class = wsScores.Cells(i, colClass).value

            key = schoolId & "|" & school & "|" & class

            If Not schoolDict.Exists(key) Then
                schoolDict.Add key, lastRowStats
                lastRowStats = lastRowStats + 1
            End If
        End If
    Next i

    ' Prepare the statistics sheet
    ReDim schoolList(1 To schoolDict.Count)
    j = 1
    Dim dictKey As Variant
    Dim parts As Variant
    For Each dictKey In schoolDict.Keys
        parts = Split(dictKey, "|")

        wsStats.Cells(schoolDict(dictKey), 1).value = parts(0) ' School ID
        wsStats.Cells(schoolDict(dictKey), 2).value = parts(1) ' School
        wsStats.Cells(schoolDict(dictKey), 3).value = parts(2) ' Class

        ' Format data cells (A-T) with center alignment and borders
        With wsStats.Cells(schoolDict(dictKey), 1).Resize(1, 21)
            .HorizontalAlignment = xlCenter ' Center text
            .Borders.LineStyle = xlContinuous ' Add borders
            .Borders.Color = RGB(0, 0, 0) ' Black borders
        End With

        schoolList(j) = parts(1) ' Store school names for summary rows
        j = j + 1
    Next dictKey

    ' First pass: Count total students (with names) for each class
    Dim studentKey As String
    Dim rowIndex As Long

    ' Initialize the actual student count (column 4) for each class
    For Each dictKey In schoolDict.Keys
        rowIndex = schoolDict(dictKey)
        wsStats.Cells(rowIndex, 4).value = 0 ' Initialize actual student count
        wsStats.Cells(rowIndex, 5).value = 0 ' Initialize participating student count

        ' Initialize average score and rank columns
        wsStats.Cells(rowIndex, 19).value = 0 ' Initialize weighted average score column
        wsStats.Cells(rowIndex, 20).value = 0 ' Initialize rank column
    Next dictKey

    ' Count students with names for actual student count
    For i = 2 To lastRow
        grade = wsScores.Cells(i, colGrade).value
        If grade = targetGrade And Not IsEmpty(wsScores.Cells(i, colName).value) Then
            studentKey = wsScores.Cells(i, colSchoolId).value & "|" & wsScores.Cells(i, colSchool).value & "|" & wsScores.Cells(i, colClass).value
            If schoolDict.Exists(studentKey) Then
                rowIndex = schoolDict(studentKey)
                wsStats.Cells(rowIndex, 4).value = wsStats.Cells(rowIndex, 4).value + 1 ' Increment actual student count
            End If
        End If
    Next i

    ' Count students in each score range
    Dim weightedScore As Double
    Dim rangeCol As Integer



    For i = 2 To lastRow ' Assuming data starts from row 2
        grade = wsScores.Cells(i, colGrade).value
        If grade = targetGrade And Not IsEmpty(wsScores.Cells(i, colWeighted).value) Then

            weightedScore = wsScores.Cells(i, colWeighted).value
            ' Get actual score for score range statistics only
            actualScore = IIf(IsEmpty(wsScores.Cells(i, colTotal).value), 0, wsScores.Cells(i, colTotal).value)
            studentKey = wsScores.Cells(i, colSchoolId).value & "|" & wsScores.Cells(i, colSchool).value & "|" & wsScores.Cells(i, colClass).value
            rowIndex = schoolDict(studentKey)

            ' Increment participating student count (with non-zero weighted scores)
            wsStats.Cells(rowIndex, 5).value = wsStats.Cells(rowIndex, 5).value + 1

            ' Increment score range count based on actual score (instead of weighted score)
            rangeCol = 0

            If actualScore < 300 Then
                rangeCol = 6 ' 300以下
            ElseIf actualScore < 320 Then
                rangeCol = 7 ' 300-320
            ElseIf actualScore < 340 Then
                rangeCol = 8 ' 320-340
            ElseIf actualScore < 360 Then
                rangeCol = 9 ' 340-360
            ElseIf actualScore < 380 Then
                rangeCol = 10 ' 360-380
            ElseIf actualScore < 400 Then
                rangeCol = 11 ' 380-400
            ElseIf actualScore < 420 Then
                rangeCol = 12 ' 400-420
            ElseIf actualScore < 440 Then
                rangeCol = 13 ' 420-440
            ElseIf actualScore < 460 Then
                rangeCol = 14 ' 440-460
            ElseIf actualScore < 480 Then
                rangeCol = 15 ' 460-480
            ElseIf actualScore < 500 Then
                rangeCol = 16 ' 480-500
            ElseIf actualScore < 520 Then
                rangeCol = 17 ' 500-520
            Else
                rangeCol = 18 ' 520及以上
            End If

            If IsEmpty(wsStats.Cells(rowIndex, rangeCol).value) Then
                wsStats.Cells(rowIndex, rangeCol).value = 1
            Else
                wsStats.Cells(rowIndex, rangeCol).value = wsStats.Cells(rowIndex, rangeCol).value + 1
            End If
        End If
    Next i

    ' Dictionary to store total weighted scores for each class
    Dim classWeightedScores As Object
    Set classWeightedScores = CreateObject("Scripting.Dictionary")

    ' Initialize the weighted scores dictionary
    For Each dictKey In schoolDict.Keys
        classWeightedScores.Add dictKey, 0
    Next dictKey

    ' Collect weighted scores for each class
    For i = 2 To lastRow ' Assuming data starts from row 2
        grade = wsScores.Cells(i, colGrade).value
        If grade = targetGrade And Not IsEmpty(wsScores.Cells(i, colWeighted).value) Then
            weightedScore = wsScores.Cells(i, colWeighted).value
            studentKey = wsScores.Cells(i, colSchoolId).value & "|" & wsScores.Cells(i, colSchool).value & "|" & wsScores.Cells(i, colClass).value

            ' Add weighted score to the class total
            classWeightedScores(studentKey) = classWeightedScores(studentKey) + weightedScore
        End If
    Next i

    ' Calculate average scores for each class
    For i = 5 To lastRowStats - 1
        If wsStats.Cells(i, 5).value > 0 Then ' Only calculate if there are participating students
            ' Calculate weighted average score
            Dim classKey As String
            classKey = wsStats.Cells(i, 1).value & "|" & wsStats.Cells(i, 2).value & "|" & wsStats.Cells(i, 3).value

            If classWeightedScores.Exists(classKey) Then
                wsStats.Cells(i, 19).value = Round(classWeightedScores(classKey) / wsStats.Cells(i, 5).value, 2)
            End If
        End If
    Next i

    ' Rank classes within each school
    Dim uniqueSchools As Object
    Set uniqueSchools = CreateObject("Scripting.Dictionary")

    ' Get unique schools
    For i = 5 To lastRowStats - 1
        Dim schoolName As String
        schoolName = wsStats.Cells(i, 2).value
        If Not uniqueSchools.Exists(schoolName) Then
            uniqueSchools.Add schoolName, Nothing
        End If
    Next i

    ' Rank classes within each school
    Dim rankSchoolKey As Variant
    For Each rankSchoolKey In uniqueSchools.Keys
        ' Create an array to store class indices and average scores
        Dim classCount As Integer
        classCount = 0

        ' Count classes in this school
        For i = 5 To lastRowStats - 1
            If wsStats.Cells(i, 2).value = rankSchoolKey Then
                classCount = classCount + 1
            End If
        Next i

        If classCount > 0 Then
            Dim classIndices() As Integer
            Dim classScores() As Double
            ReDim classIndices(1 To classCount)
            ReDim classScores(1 To classCount)

            ' Fill arrays with class indices and scores
            Dim idx As Integer
            idx = 1
            For i = 5 To lastRowStats - 1
                If wsStats.Cells(i, 2).value = rankSchoolKey Then
                    classIndices(idx) = i
                    classScores(idx) = wsStats.Cells(i, 19).value
                    idx = idx + 1
                End If
            Next i

            ' Sort classes by average score (bubble sort)
            Dim k As Integer
            For j = 1 To classCount - 1
                For k = 1 To classCount - j
                    If classScores(k) < classScores(k + 1) Then
                        ' Swap scores
                        Dim tempScore As Double
                        tempScore = classScores(k)
                        classScores(k) = classScores(k + 1)
                        classScores(k + 1) = tempScore

                        ' Swap indices
                        Dim tempIndex As Integer
                        tempIndex = classIndices(k)
                        classIndices(k) = classIndices(k + 1)
                        classIndices(k + 1) = tempIndex
                    End If
                Next k
            Next j

            ' Assign ranks
            For j = 1 To classCount
                wsStats.Cells(classIndices(j), 20).value = j
            Next j
        End If
    Next rankSchoolKey

    ' Add summary rows for each school
    Dim lastSchoolRow As Long
    Dim currentSchool As String
    Dim m As Long, n As Long ' Additional loop variables
    currentSchool = ""

    ' Sort the data by school to ensure school summary rows are placed at the end of each school's data
    For m = 5 To lastRowStats - 1
        ' Check if this is a new school and not a summary row
        If wsStats.Cells(m, 2).value <> currentSchool And Not wsStats.Cells(m, 3).MergeCells Then
            ' Get the pure school name (without any "学校汇总" text)
            currentSchool = wsStats.Cells(m, 2).value
            ' Remove any "学校汇总" text if present
            If InStr(currentSchool, "学校汇总") > 0 Then
                currentSchool = Left(currentSchool, InStr(currentSchool, "学校汇总") - 1)
            End If
            lastSchoolRow = m

            ' Find the last row for this school
            For n = m + 1 To lastRowStats - 1
                ' Only consider rows that are not summary rows and match the current school
                If Not wsStats.Cells(n, 3).MergeCells Then
                    Dim rowSchool As String
                    rowSchool = wsStats.Cells(n, 2).value
                    ' Remove any "学校汇总" text if present
                    If InStr(rowSchool, "学校汇总") > 0 Then
                        rowSchool = Left(rowSchool, InStr(rowSchool, "学校汇总") - 1)
                    End If

                    If rowSchool = currentSchool Then
                        lastSchoolRow = n
                    End If
                End If
            Next n

            ' Insert a summary row after the last row for this school
            wsStats.Rows(lastSchoolRow + 1).Insert

            ' Merge cells for school name (only B and C columns)
            wsStats.Range(wsStats.Cells(lastSchoolRow + 1, 2), wsStats.Cells(lastSchoolRow + 1, 3)).Merge
            ' Find the school ID for this school from the first class row
            Dim summarySchoolId As String
            summarySchoolId = ""
            For k = 5 To lastRowStats - 1
                If wsStats.Cells(k, 2).value = currentSchool Then
                    summarySchoolId = wsStats.Cells(k, 1).value
                    Exit For
                End If
            Next k
            wsStats.Cells(lastSchoolRow + 1, 1).value = summarySchoolId ' Keep school ID in column A
            wsStats.Cells(lastSchoolRow + 1, 2).value = currentSchool & "学校汇总" ' School name + summary text

            ' Calculate totals for this school
            Dim l As Long ' Loop variable for rows
            Dim totalWeightedScoreForSchool As Double, totalActualScoreForSchool As Double
            totalWeightedScoreForSchool = 0
            totalActualScoreForSchool = 0

            For j = 4 To 18 ' Columns for counts (including actual and participating student counts)
                Dim schoolTotal As Long
                schoolTotal = 0

                For l = 5 To lastRowStats - 1
                    ' Only count rows that are not summary rows and match the current school
                    If Not wsStats.Cells(l, 3).MergeCells Then
                        Dim rowSchoolForTotal As String
                        rowSchoolForTotal = wsStats.Cells(l, 2).value
                        ' Remove any "学校汇总" text if present
                        If InStr(rowSchoolForTotal, "学校汇总") > 0 Then
                            rowSchoolForTotal = Left(rowSchoolForTotal, InStr(rowSchoolForTotal, "学校汇总") - 1)
                        End If

                        If rowSchoolForTotal = currentSchool Then
                            If Not IsEmpty(wsStats.Cells(l, j).value) Then
                                schoolTotal = schoolTotal + wsStats.Cells(l, j).value
                            End If
                        End If
                    End If
                Next l

                wsStats.Cells(lastSchoolRow + 1, j).value = schoolTotal
            Next j

            ' Calculate school average scores
            If wsStats.Cells(lastSchoolRow + 1, 5).value > 0 Then ' If there are participating students
                ' Calculate weighted average score from class weighted averages
                Dim totalWeightedStudentScores As Double, totalStudents As Long
                totalWeightedStudentScores = 0
                totalStudents = wsStats.Cells(lastSchoolRow + 1, 5).value

                For l = 5 To lastRowStats - 1
                    ' Only count rows that are not summary rows and match the current school
                    If Not wsStats.Cells(l, 3).MergeCells Then
                        Dim rowSchoolForWeighted As String
                        rowSchoolForWeighted = wsStats.Cells(l, 2).value
                        ' Remove any "学校汇总" text if present
                        If InStr(rowSchoolForWeighted, "学校汇总") > 0 Then
                            rowSchoolForWeighted = Left(rowSchoolForWeighted, InStr(rowSchoolForWeighted, "学校汇总") - 1)
                        End If

                        If rowSchoolForWeighted = currentSchool And Not IsEmpty(wsStats.Cells(l, 19).value) Then
                            Dim classStudentsWeighted As Long
                            classStudentsWeighted = wsStats.Cells(l, 5).value
                            If classStudentsWeighted > 0 Then
                                totalWeightedStudentScores = totalWeightedStudentScores + (wsStats.Cells(l, 19).value * classStudentsWeighted)
                            End If
                        End If
                    End If
                Next l

                ' Weighted average score
                wsStats.Cells(lastSchoolRow + 1, 19).value = Round(totalWeightedStudentScores / totalStudents, 2)


            Else
                wsStats.Cells(lastSchoolRow + 1, 19).value = 0
            End If

            ' Format the summary row
            With wsStats.Rows(lastSchoolRow + 1)
                .Font.Bold = True
                .Font.Color = RGB(255, 0, 0) ' Red text
                ' Only apply formatting to data cells (columns A-T)
                With .Cells(1, 1).Resize(1, 20)
                    .Interior.Color = RGB(255, 255, 0) ' Yellow background
                    .Borders.LineStyle = xlContinuous ' Black border
                    .Borders.Color = RGB(0, 0, 0) ' Black color
                    .HorizontalAlignment = xlCenter ' Center text
                End With
            End With

            lastRowStats = lastRowStats + 1
        End If
    Next m

    ' Rank schools by average score
    Dim schoolSummaryRows() As Long
    Dim schoolAvgScores() As Double
    schoolCount = 0

    ' Count school summary rows
    For i = 5 To lastRowStats - 1
        If wsStats.Cells(i, 3).MergeCells Then
            schoolCount = schoolCount + 1
        End If
    Next i

    If schoolCount > 0 Then
        ReDim schoolSummaryRows(1 To schoolCount)
        ReDim schoolAvgScores(1 To schoolCount)

        ' Collect school summary rows and average scores
        Dim schoolIdx As Integer
        schoolIdx = 1
        For i = 5 To lastRowStats - 1
            If wsStats.Cells(i, 3).MergeCells Then
                schoolSummaryRows(schoolIdx) = i
                schoolAvgScores(schoolIdx) = wsStats.Cells(i, 19).value
                schoolIdx = schoolIdx + 1
            End If
        Next i

        ' Sort schools by average score (bubble sort)
        For i = 1 To schoolCount - 1
            For j = 1 To schoolCount - i
                If schoolAvgScores(j) < schoolAvgScores(j + 1) Then
                    ' Swap scores
                    tempScore = schoolAvgScores(j)
                    schoolAvgScores(j) = schoolAvgScores(j + 1)
                    schoolAvgScores(j + 1) = tempScore

                    ' Swap row indices
                    Dim tempRow As Long
                    tempRow = schoolSummaryRows(j)
                    schoolSummaryRows(j) = schoolSummaryRows(j + 1)
                    schoolSummaryRows(j + 1) = tempRow
                End If
            Next j
        Next i

        ' Assign ranks to schools
        For i = 1 To schoolCount
            wsStats.Cells(schoolSummaryRows(i), 20).value = i
        Next i
    End If

    ' Add district summary row
    wsStats.Rows(lastRowStats).Insert

    ' Merge cells for district summary (only B and C columns)
    wsStats.Range(wsStats.Cells(lastRowStats, 2), wsStats.Cells(lastRowStats, 3)).Merge
    wsStats.Cells(lastRowStats, 1).value = "" ' Leave school ID empty for district summary
    wsStats.Cells(lastRowStats, 2).value = "全区汇总"

    ' Calculate district totals and average score
    For j = 4 To 18 ' Columns for counts (including actual and participating student counts)
        Dim districtTotal As Long
        districtTotal = 0

        For i = 5 To lastRowStats - 1
            ' Only count rows that are not summary rows (don't have merged cells in B-C)
            If Not IsEmpty(wsStats.Cells(i, j).value) And Not wsStats.Cells(i, 3).MergeCells Then
                districtTotal = districtTotal + wsStats.Cells(i, j).value
            End If
        Next i

        wsStats.Cells(lastRowStats, j).value = districtTotal
    Next j

    ' Calculate district average scores
    If wsStats.Cells(lastRowStats, 5).value > 0 Then ' If there are participating students
        ' Calculate weighted average score from class weighted averages
        Dim totalDistrictWeightedScore As Double, totalDistrictStudents As Long
        totalDistrictWeightedScore = 0
        totalDistrictStudents = wsStats.Cells(lastRowStats, 5).value

        For i = 5 To lastRowStats - 1
            ' Only count rows that are not summary rows and not school summary rows
            If Not wsStats.Cells(i, 3).MergeCells Then
                If Not IsEmpty(wsStats.Cells(i, 19).value) Then
                    Dim classStudentsForDistrict As Long
                    classStudentsForDistrict = wsStats.Cells(i, 5).value
                    If classStudentsForDistrict > 0 Then
                        totalDistrictWeightedScore = totalDistrictWeightedScore + (wsStats.Cells(i, 19).value * classStudentsForDistrict)
                    End If
                End If
            End If
        Next i

        ' Weighted average score
        wsStats.Cells(lastRowStats, 19).value = Round(totalDistrictWeightedScore / totalDistrictStudents, 2)


    Else
        wsStats.Cells(lastRowStats, 19).value = 0
    End If

    ' Format the district summary row
    With wsStats.Rows(lastRowStats)
        .Font.Bold = True
        .Font.Color = RGB(255, 0, 0) ' Red text
        ' Only apply formatting to data cells (columns A-T)
        With .Cells(1, 1).Resize(1, 20)
            .Interior.Color = RGB(255, 255, 0) ' Yellow background
            .Borders.LineStyle = xlContinuous ' Black border
            .Borders.Color = RGB(0, 0, 0) ' Black color
            .HorizontalAlignment = xlCenter ' Center text
        End With
    End With
End Sub