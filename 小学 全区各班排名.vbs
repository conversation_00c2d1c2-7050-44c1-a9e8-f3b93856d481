Sub 统计班级平均分及排名()
    Dim wb As Workbook
    Dim ws成绩 As Worksheet, ws教师 As Worksheet, ws班级 As Worksheet
    Dim lastRow As Long, i <PERSON>, outputRow As Long
    Dim 年级 As String
    Dim dict班级 As Object, dict教师 As Object
    Dim arr成绩(), arr教师(), arr输出()
    Dim startTime As Double
    Dim j As Integer
    Dim classKeys() As Variant

    ' 记录开始时间
    startTime = Timer
    
    ' 初始化字典对象
    Set dict班级 = CreateObject("Scripting.Dictionary")
    Set dict教师 = CreateObject("Scripting.Dictionary")
    
    ' 设置工作表对象
    Set wb = ThisWorkbook
    On Error Resume Next
    Set ws成绩 = wb.Sheets("成绩汇总表")
    Set ws教师 = wb.Sheets("任课教师")
    Set ws班级 = wb.Sheets("班级统计")
    On Error GoTo 0
    
    ' 检查工作表是否存在
    If ws成绩 Is Nothing Then
        MsgBox "找不到工作表:成绩汇总表", vbExclamation
        Exit Sub
    End If
    If ws教师 Is Nothing Then
        MsgBox "找不到工作表:任课教师", vbExclamation
        Exit Sub
    End If
    If ws班级 Is Nothing Then
        MsgBox "找不到工作表:班级统计", vbExclamation
        Exit Sub
    End If
    
    ' 获取年级
    年级 = Trim(ws班级.Range("C1").Value)
    If 年级 = "" Then
        MsgBox "请在班级表的C1单元格输入要统计的年级", vbExclamation
        Exit Sub
    End If
    
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    
    ' 1. 读取任课教师数据
    lastRow = ws教师.Cells(ws教师.Rows.Count, 1).End(xlUp).Row
    If lastRow > 1 Then
        arr教师 = ws教师.Range("A1:R" & lastRow).Value
        
        For i = 2 To UBound(arr教师, 1)
            If Trim(arr教师(i, 3)) = 年级 Then
                Dim teacherKey As String
                teacherKey = arr教师(i, 1) & "|" & arr教师(i, 2) & "|" & arr教师(i, 4)
                
                If Not dict教师.Exists(teacherKey) Then
                    Dim teacherInfo(1 To 5) As String
                    teacherInfo(1) = arr教师(i, 6)   '语文教师
                    teacherInfo(2) = arr教师(i, 7)   '数学教师
                    teacherInfo(3) = arr教师(i, 8)   '英语教师
                    teacherInfo(4) = arr教师(i, 9)   '科学教师
                    teacherInfo(5) = arr教师(i, 10)  '道法教师
                    
                    dict教师.Add teacherKey, teacherInfo
                End If
            End If
        Next i
    End If
    
    ' 2. 统计成绩数据
    lastRow = ws成绩.Cells(ws成绩.Rows.Count, 1).End(xlUp).Row
    If lastRow < 2 Then
        MsgBox "成绩汇总表没有数据", vbInformation
        GoTo CleanUp
    End If
    
    arr成绩 = ws成绩.Range("A1:X" & lastRow).Value
    
    For i = 2 To UBound(arr成绩, 1)
        If Trim(arr成绩(i, 4)) = 年级 Then
            Dim classKey As String
            classKey = arr成绩(i, 2) & "|" & arr成绩(i, 3) & "|" & arr成绩(i, 5)
            
            If Not dict班级.Exists(classKey) Then
                Dim classInfo(1 To 14) As Variant
                ' 初始化统计项
                classInfo(1) = arr成绩(i, 2)   '学校
                classInfo(2) = arr成绩(i, 3)   '校点
                classInfo(3) = arr成绩(i, 5)   '班级
                classInfo(4) = 0               '参考人数
                classInfo(5) = 0               '语文总分
                classInfo(6) = 0               '数学总分
                classInfo(7) = 0               '英语总分
                classInfo(8) = 0               '科学总分
                classInfo(9) = 0               '道法总分
                classInfo(10) = 0              '语文参考人数
                classInfo(11) = 0              '数学参考人数
                classInfo(12) = 0              '英语参考人数
                classInfo(13) = 0              '科学参考人数
                classInfo(14) = 0              '道法参考人数
                
                dict班级.Add classKey, classInfo
            End If
            
            ' 统计各科成绩
            If dict班级.Exists(classKey) Then
                Dim classData As Variant
                classData = dict班级(classKey)
                
                ' 统计参考人数(有总分的人数)
                If Val(arr成绩(i, 24)) > 0 Then
                    classData(4) = classData(4) + 1
                    dict班级(classKey) = classData
                End If
                
                ' 统计各科成绩
                If Val(arr成绩(i, 9)) > 0 Then  '语文
                    classData(5) = classData(5) + Val(arr成绩(i, 9))
                    classData(10) = classData(10) + 1
                End If
                
                If Val(arr成绩(i, 10)) > 0 Then '数学
                    classData(6) = classData(6) + Val(arr成绩(i, 10))
                    classData(11) = classData(11) + 1
                End If
                
                If Val(arr成绩(i, 11)) > 0 Then '英语
                    classData(7) = classData(7) + Val(arr成绩(i, 11))
                    classData(12) = classData(12) + 1
                End If
                
                If Val(arr成绩(i, 12)) > 0 Then '科学
                    classData(8) = classData(8) + Val(arr成绩(i, 12))
                    classData(13) = classData(13) + 1
                End If
                
                If Val(arr成绩(i, 13)) > 0 Then '道法
                    classData(9) = classData(9) + Val(arr成绩(i, 13))
                    classData(14) = classData(14) + 1
                End If
                
                dict班级(classKey) = classData
            End If
        End If
    Next i
    
    ' 获取班级键数组
    classKeys = dict班级.Keys
    
    ' 3. 计算全区统计数据
    ' 全区统计数组结构:1-学校,2-校点,3-班级,4-参考人数,5-语文总分,6-数学总分,7-英语总分,8-科学总分,9-道法总分,
    ' 10-语文参考人数,11-数学参考人数,12-英语参考人数,13-科学参考人数,14-道法参考人数
    Dim arr全区合计(1 To 14) As Variant
    For i = 0 To UBound(classKeys)
        If dict班级.Exists(classKeys(i)) Then
            classData = dict班级(classKeys(i))
            ' 育英学校参与全区统计但不参与排名
            For j = 4 To 14  ' 统计所有相关数据项
                arr全区合计(j) = arr全区合计(j) + classData(j)
            Next
        End If
    Next i


    ReDim arr输出(1 To dict班级.Count + 1, 1 To 23) ' 增加一行给全区统计
    Dim classIndex As Long
    classIndex = 0

    ' 添加全区统计行(显示在第一行)
    classIndex = classIndex + 1
    arr输出(classIndex, 1) = ""  '序号
    arr输出(classIndex, 2) = ""  '学校
    arr输出(classIndex, 3) = "全区"
    arr输出(classIndex, 4) = "合计："     '班级
    arr输出(classIndex, 5) = arr全区合计(4) '全区参考人数
    
    ' 计算各科平均分(使用各科实际参考人数)
    arr输出(classIndex, 6) = Round(arr全区合计(5) / IIf(arr全区合计(10) > 0, arr全区合计(10), 1), 2) '语文平均
    arr输出(classIndex, 9) = Round(arr全区合计(6) / IIf(arr全区合计(11) > 0, arr全区合计(11), 1), 2) '数学平均
    arr输出(classIndex, 12) = Round(arr全区合计(7) / IIf(arr全区合计(12) > 0, arr全区合计(12), 1), 2) '英语平均
    arr输出(classIndex, 15) = Round(arr全区合计(8) / IIf(arr全区合计(13) > 0, arr全区合计(13), 1), 2) '科学平均
    arr输出(classIndex, 18) = Round(arr全区合计(9) / IIf(arr全区合计(14) > 0, arr全区合计(14), 1), 2) '道法平均
    
    ' 计算总分平均(使用各科都参考的学生人数)
    Dim totalScore As Double
    totalScore = arr全区合计(5) + arr全区合计(6) + arr全区合计(7) + arr全区合计(8) + arr全区合计(9)
    Dim totalCount As Long
    totalCount = Application.WorksheetFunction.Min(arr全区合计(10), arr全区合计(11), arr全区合计(12), arr全区合计(13), arr全区合计(14))
    arr输出(classIndex, 21) = Round(totalScore / IIf(totalCount > 0, totalCount, 1), 2) '总分平均
    
    ' 标记为合计行不参与排名
    arr输出(classIndex, 23) = "合计"

    ' 计算平均分
    classKeys = dict班级.Keys
    
    ' 按班级顺序输出(包含育英学校但不参与排名)
    For i = 0 To UBound(classKeys)
        classIndex = classIndex + 1
            If dict班级.Exists(classKeys(i)) Then
                classData = dict班级(classKeys(i))
                
                ' 标记育英学校不参与排名
                If InStr(classKeys(i), "育英学校") > 0 Then
                    arr输出(classIndex, 23) = "不排名"
                End If
                    
                    ' 基本信息
                    arr输出(classIndex, 1) = classIndex               '序号
                    arr输出(classIndex, 2) = classData(1)            '学校
                    arr输出(classIndex, 3) = classData(2)            '校点
                    arr输出(classIndex, 4) = classData(3)            '班级
                    arr输出(classIndex, 5) = classData(4)            '参考人数
                    
                    ' 计算各科平均分
                    arr输出(classIndex, 6) = Round(classData(5) / IIf(classData(10) > 0, classData(10), 1), 2)  '语文平均分
                    arr输出(classIndex, 9) = Round(classData(6) / IIf(classData(11) > 0, classData(11), 1), 2)  '数学平均分
                    arr输出(classIndex, 12) = Round(classData(7) / IIf(classData(12) > 0, classData(12), 1), 2) '英语平均分
                    arr输出(classIndex, 15) = Round(classData(8) / IIf(classData(13) > 0, classData(13), 1), 2) '科学平均分
                    arr输出(classIndex, 18) = Round(classData(9) / IIf(classData(14) > 0, classData(14), 1), 2) '道法平均分
                    
                    ' 计算总分平均分
                    totalScore = classData(5) + classData(6) + classData(7) + classData(8) + classData(9)
                    totalCount = Application.WorksheetFunction.Min(classData(10), classData(11), classData(12), classData(13), classData(14))
                    arr输出(classIndex, 21) = Round(totalScore / IIf(totalCount > 0, totalCount, 1), 2) '总分平均
                    
                    ' 添加教师信息
                    teacherKey = classData(1) & "|" & classData(2) & "|" & classData(3)
                    
                        If dict教师.Exists(teacherKey) Then
                            Dim teachers() As String
                            teachers = dict教师(teacherKey)
                            
                            arr输出(classIndex, 8) = teachers(1)  '语文教师
                            arr输出(classIndex, 11) = teachers(2) '数学教师
                            arr输出(classIndex, 14) = teachers(3) '英语教师
                            arr输出(classIndex, 17) = teachers(4) '科学教师
                            arr输出(classIndex, 20) = teachers(5) '道法教师
                        End If
                    End If
    Next i
    
    ' 4. 计算排名
    Call 计算排名(arr输出)
    
    ' 5. 输出到班级表
    outputRow = 4
    If ws班级.Cells(ws班级.Rows.Count, 1).End(xlUp).Row >= outputRow Then
        ws班级.Range("A4:V" & ws班级.Rows.Count).ClearContents
    End If
    
    ' 确保输出范围与数组维度匹配
    On Error Resume Next
    ws班级.Range("A4").Resize(UBound(arr输出, 1), 22).Value = arr输出
    If Err.Number <> 0 Then
        MsgBox "输出数据时出错: " & Err.Description, vbExclamation
    End If
    On Error GoTo 0
    
CleanUp:
    Application.ScreenUpdating = True
    Application.Calculation = xlAutomatic
    
'    Dim excelApp, workbook, worksheet
    
    ' 设置全区行格式（第4行）
    With ws班级.Range(Cells(4, 1), Cells(4, 22))
        .Interior.Color = RGB(255, 255, 0) ' 黄色背景
        .Font.Bold = True                  ' 粗体
    End With
    
    ' 设置表格边框（A4到V列最后一行）
    lastRow = ws班级.Cells(ws班级.Rows.Count, "A").End(-4162).Row ' xlUp

    With ws班级.Range("A4:V" & lastRow).Borders
        .LineStyle = 1    ' xlContinuous
        .Weight = 2       ' xlThin
    End With
        Set dict班级 = Nothing
    Set dict教师 = Nothing
    Set ws成绩 = Nothing
    Set ws教师 = Nothing
    Set ws班级 = Nothing
    Set wb = Nothing
   
    MsgBox "统计完成，耗时: " & Round(Timer - startTime, 2) & "秒", vbInformation
End Sub

Private Sub 计算排名(ByRef arr数据() As Variant)
    Dim i As Long, j As Long
    Dim subjectCols(1 To 6) As Long
    Dim rankCols(1 To 6) As Long
    
    ' 设置科目列和排名列对应关系
    subjectCols(1) = 6   '语文平均分列
    rankCols(1) = 7      '语文排名列
    
    subjectCols(2) = 9   '数学平均分列
    rankCols(2) = 10     '数学排名列
    
    subjectCols(3) = 12  '英语平均分列
    rankCols(3) = 13     '英语排名列
    
    subjectCols(4) = 15  '科学平均分列
    rankCols(4) = 16     '科学排名列
    
    subjectCols(5) = 18  '道法平均分列
    rankCols(5) = 19     '道法排名列
    
    subjectCols(6) = 21  '总分平均列
    rankCols(6) = 22     '总分排名列
    
    ' 对每个科目计算排名(排除合计行和育英学校)
    For j = 1 To 6
        ' 创建排序数组
        Dim sortArr() As Variant
        Dim validRows As Long
        validRows = 0
        
        ' 计算有效行数(排除合计行和育英学校)
        For i = 1 To UBound(arr数据, 1)
            If arr数据(i, 23) <> "合计" And InStr(arr数据(i, 2), "育英学校") = 0 Then
                validRows = validRows + 1
            End If
        Next
        
        If validRows = 0 Then Exit For
        
        ReDim sortArr(1 To validRows, 1 To 2)
        Dim rowIdx As Long
        rowIdx = 1
        
        ' 填充排序数组(只包含有效行)
        For i = 1 To UBound(arr数据, 1)
            If arr数据(i, 23) <> "合计" And InStr(arr数据(i, 2), "育英学校") = 0 Then
                sortArr(rowIdx, 1) = i  '行索引
                sortArr(rowIdx, 2) = arr数据(i, subjectCols(j)) '分数
                rowIdx = rowIdx + 1
            End If
        Next
        
        ' 排序
        If validRows > 1 Then
            Call 快速排序(sortArr, 1, validRows, 2)
        End If
        
        ' 计算排名
        Dim currentRank As Long
        currentRank = 1
        
        For i = 1 To validRows
            If i > 1 Then
                ' 处理并列情况
                If Abs(sortArr(i, 2) - sortArr(i - 1, 2)) < 0.0001 Then
                    arr数据(sortArr(i, 1), rankCols(j)) = currentRank
                Else
                    currentRank = i
                    arr数据(sortArr(i, 1), rankCols(j)) = currentRank
                End If
            Else
                arr数据(sortArr(i, 1), rankCols(j)) = currentRank
            End If
        Next i
    Next j
End Sub

Private Sub 快速排序(ByRef arr() As Variant, ByVal first As Long, ByVal last As Long, ByVal col As Long)
    Dim pivot As Variant, temp As Variant
    Dim i As Long, j As Long, k As Long
    
    If first < last Then
        pivot = arr((first + last) \ 2, col)
        i = first
        j = last
        
        Do While i <= j
            Do While arr(i, col) > pivot
                i = i + 1
            Loop
            
            Do While arr(j, col) < pivot
                j = j - 1
            Loop
            
            If i <= j Then
                ' 交换两行
                For k = 1 To UBound(arr, 2)
                    temp = arr(i, k)
                    arr(i, k) = arr(j, k)
                    arr(j, k) = temp
                Next k
                
                i = i + 1
                j = j - 1
            End If
        Loop
        
        If first < j Then Call 快速排序(arr, first, j, col)
        If i < last Then Call 快速排序(arr, i, last, col)
    End If
End Sub
