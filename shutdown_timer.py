import tkinter as tk
from tkinter import messagebox
import subprocess
import datetime

class ShutdownTimer:
    def __init__(self, root):
        self.root = root
        self.root.title("定时关机")
        self.root.geometry("300x200")
        
        # 创建时间选择框架
        time_frame = tk.Frame(root)
        time_frame.pack(pady=20)
        
        # 小时选择
        tk.Label(time_frame, text="时:").pack(side=tk.LEFT)
        self.hour_var = tk.StringVar(value="00")
        self.hour_spinbox = tk.Spinbox(time_frame, from_=0, to=23, width=5, 
                                     format="%02.0f", textvariable=self.hour_var)
        self.hour_spinbox.pack(side=tk.LEFT, padx=5)
        
        # 分钟选择
        tk.Label(time_frame, text="分:").pack(side=tk.LEFT)
        self.minute_var = tk.StringVar(value="00")
        self.minute_spinbox = tk.Spinbox(time_frame, from_=0, to=59, width=5,
                                       format="%02.0f", textvariable=self.minute_var)
        self.minute_spinbox.pack(side=tk.LEFT, padx=5)
        
        # 按钮框架
        button_frame = tk.Frame(root)
        button_frame.pack(pady=20)
        
        # 设置定时关机按钮
        self.set_button = tk.Button(button_frame, text="设置定时关机", 
                                  command=self.set_shutdown)
        self.set_button.pack(side=tk.LEFT, padx=10)
        
        # 取消定时关机按钮
        self.cancel_button = tk.Button(button_frame, text="取消定时关机",
                                     command=self.cancel_shutdown)
        self.cancel_button.pack(side=tk.LEFT, padx=10)
        
        # 状态标签
        self.status_label = tk.Label(root, text="当前没有定时关机任务", fg="gray")
        self.status_label.pack(pady=20)

    def set_shutdown(self):
        try:
            # 获取用户设置的时间
            hour = int(self.hour_var.get())
            minute = int(self.minute_var.get())
            
            # 计算关机时间
            now = datetime.datetime.now()
            shutdown_time = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            
            # 如果设置的时间早于当前时间，则设置为明天的这个时间
            if shutdown_time <= now:
                shutdown_time += datetime.timedelta(days=1)
            
            # 计算秒数差
            seconds = int((shutdown_time - now).total_seconds())
            
            # 设置关机命令
            subprocess.run(['shutdown', '/s', '/t', str(seconds)], check=True)
            
            # 更新状态标签
            shutdown_time_str = shutdown_time.strftime("%H:%M")
            self.status_label.config(
                text=f"计算机将在今天 {shutdown_time_str} 关机",
                fg="green"
            )
            messagebox.showinfo("成功", f"已设置在 {shutdown_time_str} 关机")
            
        except ValueError:
            messagebox.showerror("错误", "请输入有效的时间")
        except subprocess.CalledProcessError:
            messagebox.showerror("错误", "设置关机失败，请检查是否有管理员权限")

    def cancel_shutdown(self):
        try:
            # 取消关机命令
            subprocess.run(['shutdown', '/a'], check=True)
            self.status_label.config(text="已取消定时关机任务", fg="gray")
            messagebox.showinfo("成功", "已取消定时关机任务")
        except subprocess.CalledProcessError:
            messagebox.showerror("错误", "取消关机失败，请检查是否有管理员权限")

if __name__ == "__main__":
    root = tk.Tk()
    app = ShutdownTimer(root)
    root.mainloop()