'------------------------------------------------------------------------------
' Module: Academic Score Range Statistics
' Function: Based on data in "Score Table", filter by grade, and calculate score ranges for each class, school, and district
' Uses arrays and dictionaries for efficient processing, and formats output as required
'------------------------------------------------------------------------------
Sub AcademicScoreRangeStatistics()
    ' Declare variables
    Dim wsScores As Worksheet, wsStats As Worksheet
    Dim dataArray As Variant
    Dim lastRow As Long, i <PERSON>, j <PERSON> Long
    Dim grade As String
    Dim schoolId As String, school As String, class As String
    Dim weightedScore As Double, totalScore As Double
    Dim classDict As Object, schoolDict As Object, schoolClassDict As Object
    Dim currentRow As Long
    Dim classKey As Variant
    Dim scoreRangeBoundaries As Variant
    Dim classStatsArray() As Variant, schoolStatsArray() As Variant, districtStatsArray As Variant
    Dim classAvgDict As Object, schoolAvgDict As Object
    Dim classRankArray() As Variant, schoolRankArray() As Variant
    Dim classCount As Long, schoolCount As Long
    Dim currentSchool As String, prevSchool As String
    Dim schoolStartRow As Long, schoolEndRow As Long
    Dim infoScore As Double, infoBonus As Double

    ' Set worksheet references
    Set wsScores = ThisWorkbook.Worksheets("成绩总表")
    Set wsStats = ThisWorkbook.Worksheets("学业分段统计")

    ' Turn off screen updating and automatic calculation to improve performance
    With Application
        .ScreenUpdating = False
        .Calculation = xlCalculationManual
        .EnableEvents = False
    End With

    ' Get grade value (from cell D2)
    grade = wsStats.Range("D2").value
    If grade = "" Then
        MsgBox "请在D2单元格输入要分析的年级!", vbExclamation
        GoTo CleanExit
    End If
    
    ' Debug: Show grade being filtered
    MsgBox "当前筛选年级为: " & grade, vbInformation, "调试信息"
    
    ' Verify "成绩总表" exists and has data
    On Error Resume Next
    Set wsScores = ThisWorkbook.Worksheets("成绩总表")
    If wsScores Is Nothing Then
        MsgBox "找不到'成绩总表'工作表!", vbCritical
        GoTo CleanExit
    End If
    On Error GoTo 0
    
    ' Check if any data exists for the selected grade
    Dim gradeDataExists As Boolean
    gradeDataExists = False
    For i = 1 To UBound(dataArray, 1)
        If dataArray(i, 1) = grade Then
            gradeDataExists = True
            Exit For
        End If
    Next i
    
    If Not gradeDataExists Then
        MsgBox "在'成绩总表'中找不到年级 '" & grade & "' 的数据!", vbExclamation
        GoTo CleanExit
    End If
    
    ' Verify "成绩总表" exists and has data
    On Error Resume Next
    Set wsScores = ThisWorkbook.Worksheets("成绩总表")
    If wsScores Is Nothing Then
        MsgBox "找不到'成绩总表'工作表!", vbCritical
        GoTo CleanExit
    End If
    On Error GoTo 0
    
    ' Check if any data exists for the selected grade
    Dim gradeDataExists As Boolean
    gradeDataExists = False
    For i = 1 To UBound(dataArray, 1)
        If dataArray(i, 1) = grade Then
            gradeDataExists = True
            Exit For
        End If
    Next i
    
    If Not gradeDataExists Then
        MsgBox "在'成绩总表'中找不到年级 '" & grade & "' 的数据!", vbExclamation
        GoTo CleanExit
    End If
    
    ' Verify "成绩总表" exists and has data
    On Error Resume Next
    Set wsScores = ThisWorkbook.Worksheets("成绩总表")
    If wsScores Is Nothing Then
        MsgBox "找不到'成绩总表'工作表!", vbCritical
        GoTo CleanExit
    End If
    On Error GoTo 0
    
    ' Check if any data exists for the selected grade
    Dim gradeDataExists As Boolean
    gradeDataExists = False
    For i = 1 To UBound(dataArray, 1)
        If dataArray(i, 1) = grade Then
            gradeDataExists = True
            Exit For
        End If
    Next i
    
    If Not gradeDataExists Then
        MsgBox "在'成绩总表'中找不到年级 '" & grade & "' 的数据!", vbExclamation
        GoTo CleanExit
    End If
    
    ' Verify "成绩总表" exists and has data
    On Error Resume Next
    Set wsScores = ThisWorkbook.Worksheets("成绩总表")
    If wsScores Is Nothing Then
        MsgBox "找不到'成绩总表'工作表!", vbCritical
        GoTo CleanExit
    End If
    On Error GoTo 0
    
    ' Check if any data exists for the selected grade
    Dim gradeDataExists As Boolean
    gradeDataExists = False
    For i = 1 To UBound(dataArray, 1)
        If dataArray(i, 1) = grade Then
            gradeDataExists = True
            Exit For
        End If
    Next i
    
    If Not gradeDataExists Then
        MsgBox "在'成绩总表'中找不到年级 '" & grade & "' 的数据!", vbExclamation
        GoTo CleanExit
    End If

    ' Get the last row in the score table
    lastRow = wsScores.Cells(wsScores.Rows.Count, 1).End(xlUp).Row
    If lastRow < 2 Then
        MsgBox "No data in the score table!", vbExclamation
        GoTo CleanExit
    End If

    ' Sort the score table (by grade, school ID, school name, class)
    With wsScores.Sort
        .SortFields.Clear
        .SortFields.Add key:=wsScores.Range("A2:A" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending ' Grade
        .SortFields.Add key:=wsScores.Range("B2:B" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending ' School ID
        .SortFields.Add key:=wsScores.Range("C2:C" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending ' School
        .SortFields.Add key:=wsScores.Range("D2:D" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending ' Class
        .SetRange wsScores.Range("A1:T" & lastRow) ' Include header row
        .Header = xlYes
        .MatchCase = False
        .Orientation = xlTopToBottom
        .Apply
    End With

    ' Read score table data into array for better performance
    dataArray = wsScores.Range("A2:T" & lastRow).value

    ' Define score range boundaries
    scoreRangeBoundaries = Array(0, 300, 320, 340, 360, 380, 400, 420, 440, 460, 480, 500, 520, 9999)

    ' Create dictionary objects
    Set classDict = CreateObject("Scripting.Dictionary")
    Set schoolDict = CreateObject("Scripting.Dictionary")
    Set schoolClassDict = CreateObject("Scripting.Dictionary")
    Set classAvgDict = CreateObject("Scripting.Dictionary")
    Set schoolAvgDict = CreateObject("Scripting.Dictionary")

    ' Initialize district statistics array
    ReDim districtStatsArray(1 To 15)
    For i = 1 To 15
        districtStatsArray(i) = 0
    Next i

    ' Step 1: Collect all school and class information
    For i = 1 To UBound(dataArray, 1)
        If dataArray(i, 1) = grade Then
            schoolId = dataArray(i, 2)
            school = dataArray(i, 3)
            class = dataArray(i, 4)

            ' Add school to school dictionary
            If Not schoolDict.Exists(school) Then
                schoolDict.Add school, schoolId
            End If

            ' Add class to school-class dictionary
            If Not schoolClassDict.Exists(school) Then
                schoolClassDict.Add school, CreateObject("Scripting.Dictionary")
            End If

            If Not schoolClassDict(school).Exists(class) Then
                schoolClassDict(school).Add class, True
            End If
        End If
    Next i

    ' If no data, exit
    If schoolDict.Count = 0 Then
        MsgBox "No data found for grade " & grade & "!", vbExclamation
        GoTo CleanExit
    End If

    ' Clear data in the statistics table (keep first 4 rows)
    wsStats.Rows("5:" & wsStats.Rows.Count).Delete

    ' Initialize class statistics array
    classCount = 0
    Dim schoolKey As Variant
    For Each schoolKey In schoolClassDict.Keys
        classCount = classCount + schoolClassDict(schoolKey).Count
    Next schoolKey

    ReDim classStatsArray(1 To classCount, 1 To 21)
    ReDim classRankArray(1 To classCount, 1 To 3)

    ' Initialize school statistics array
    schoolCount = schoolDict.Count
    ReDim schoolStatsArray(1 To schoolCount, 1 To 21)
    ReDim schoolRankArray(1 To schoolCount, 1 To 3)

    ' Step 2: Calculate statistics for each class
    currentRow = 5 ' Start filling data from row 5
    Dim classCounter As Long
    classCounter = 0
    Dim schoolCounter As Long
    schoolCounter = 0

    ' Loop through each school
    For Each schoolKey In schoolClassDict.Keys
        schoolCounter = schoolCounter + 1
        schoolStartRow = currentRow
        prevSchool = schoolKey

        ' Loop through each class in the school
        For Each classKey In schoolClassDict(schoolKey).Keys
            classCounter = classCounter + 1

            ' Initialize class statistics data
            For j = 1 To 21
                classStatsArray(classCounter, j) = 0
            Next j

            ' Fill school ID, school name, and class
            classStatsArray(classCounter, 1) = schoolDict(schoolKey) ' School ID
            classStatsArray(classCounter, 2) = schoolKey ' School name
            classStatsArray(classCounter, 3) = classKey ' Class

            ' Loop through score table, calculate statistics for current class
            For i = 1 To UBound(dataArray, 1)
                If dataArray(i, 1) = grade And _
                   dataArray(i, 3) = schoolKey And _
                   dataArray(i, 4) = classKey Then

                    ' Increment actual student count (students with names)
                    If dataArray(i, 6) <> "" Then
                        classStatsArray(classCounter, 4) = classStatsArray(classCounter, 4) + 1
                    End If

                    ' Get weighted score and total score
                    weightedScore = Val(dataArray(i, 18))
                    totalScore = Val(dataArray(i, 17))

                    ' Get information score, if passing (>=60) add 10 points
                    infoScore = Val(dataArray(i, 13))
                    infoBonus = IIf(infoScore >= 60, 10, 0)

                    ' Add information bonus to weighted score for statistics
                    weightedScore = weightedScore + infoBonus

                    ' If there's a weighted score, calculate statistics
                    If weightedScore > 0 Then
                        ' Debug: Show student data being processed
                        Debug.Print "Processing student: " & dataArray(i, 6) & _
                                    ", Class: " & classKey & _
                                    ", Weighted Score: " & weightedScore
                        
                        ' Increment participating student count
                        classStatsArray(classCounter, 5) = classStatsArray(classCounter, 5) + 1

                        ' Determine score range and count - using weighted score
                        For j = 1 To UBound(scoreRangeBoundaries) - 1
                            If weightedScore >= scoreRangeBoundaries(j - 1) And weightedScore < scoreRangeBoundaries(j) Then
                                classStatsArray(classCounter, j + 5) = classStatsArray(classCounter, j + 5) + 1
                                ' Debug: Show score range assignment
                                Debug.Print "Score " & weightedScore & " assigned to range " & _
                                            scoreRangeBoundaries(j - 1) & "-" & scoreRangeBoundaries(j)
                                Exit For
                            End If
                        Next j

                        ' Add weighted score for average calculation (weighted score already includes info bonus)
                        classStatsArray(classCounter, 19) = classStatsArray(classCounter, 19) + weightedScore
                        ' Debug: Show current total score
                        Debug.Print "Current total score for class: " & classStatsArray(classCounter, 19)
                    End If
                End If
            Next i

            ' Calculate average score
            If classStatsArray(classCounter, 5) > 0 Then
                classStatsArray(classCounter, 19) = Round(classStatsArray(classCounter, 19) / classStatsArray(classCounter, 5), 2)
            Else
                classStatsArray(classCounter, 19) = 0
            End If

            ' Store class average score for ranking
            classRankArray(classCounter, 1) = classStatsArray(classCounter, 19) ' Average score
            classRankArray(classCounter, 2) = classCounter ' Class index
            classRankArray(classCounter, 3) = schoolCounter ' School index

            ' Add to school statistics
            For j = 4 To 18
                schoolStatsArray(schoolCounter, j) = schoolStatsArray(schoolCounter, j) + classStatsArray(classCounter, j)
            Next j
            ' Add school total score (class average * participating students)
            schoolStatsArray(schoolCounter, 19) = schoolStatsArray(schoolCounter, 19) + (classStatsArray(classCounter, 19) * classStatsArray(classCounter, 5))

            ' Fill data to statistics table
            wsStats.Cells(currentRow, 1).value = classStatsArray(classCounter, 1) ' School ID
            wsStats.Cells(currentRow, 2).value = classStatsArray(classCounter, 2) ' School name
            wsStats.Cells(currentRow, 3).value = classStatsArray(classCounter, 3) ' Class

            For j = 4 To 18
                wsStats.Cells(currentRow, j).value = classStatsArray(classCounter, j)
            Next j

            wsStats.Cells(currentRow, 19).value = classStatsArray(classCounter, 19) ' Average score

            ' Move to next row
            currentRow = currentRow + 1
        Next classKey

        ' School summary row
        schoolEndRow = currentRow - 1

        ' Fill school ID and school name
        schoolStatsArray(schoolCounter, 1) = schoolDict(schoolKey) ' School ID
        schoolStatsArray(schoolCounter, 2) = schoolKey ' School name
        schoolStatsArray(schoolCounter, 3) = schoolKey & "汇总" ' Summary identifier

        ' Calculate school average score
        If schoolStatsArray(schoolCounter, 5) > 0 Then
            schoolStatsArray(schoolCounter, 19) = Round(schoolStatsArray(schoolCounter, 19) / schoolStatsArray(schoolCounter, 5), 2)
        Else
            schoolStatsArray(schoolCounter, 19) = 0
        End If

        ' Store school average score for ranking
        schoolRankArray(schoolCounter, 1) = schoolStatsArray(schoolCounter, 19) ' Average score
        schoolRankArray(schoolCounter, 2) = schoolCounter ' School index

        ' Fill data to statistics table
        wsStats.Cells(currentRow, 1).value = schoolStatsArray(schoolCounter, 1) ' School ID
        wsStats.Cells(currentRow, 2).value = schoolStatsArray(schoolCounter, 2) ' School name

        For j = 4 To 19
            wsStats.Cells(currentRow, j).value = schoolStatsArray(schoolCounter, j)
        Next j

        ' Merge cells B and C
        wsStats.Range(wsStats.Cells(currentRow, 2), wsStats.Cells(currentRow, 3)).Merge
        wsStats.Cells(currentRow, 2).value = schoolKey & "汇总"

        ' Format summary row
        With wsStats.Range(wsStats.Cells(currentRow, 1), wsStats.Cells(currentRow, 20))
            .Interior.Color = RGB(255, 255, 0) ' Yellow background
            .Font.Bold = True ' Bold
            .Font.Color = RGB(255, 0, 0) ' Red text
        End With

        ' Add to district statistics
        For j = 4 To 18
            districtStatsArray(j - 3) = districtStatsArray(j - 3) + schoolStatsArray(schoolCounter, j)
        Next j

        ' Move to next row
        currentRow = currentRow + 1
    Next schoolKey

    ' District summary row
    ' Calculate district average score
    Dim districtTotalScore As Double, districtParticipatingStudents As Long
    districtParticipatingStudents = districtStatsArray(2) ' Participating students

    ' Recalculate district total score (using class averages and participating students)
    districtTotalScore = 0
    For i = 1 To classCounter
        ' Add class total score (class average * participating students)
        districtTotalScore = districtTotalScore + (classStatsArray(i, 19) * classStatsArray(i, 5))
    Next i

    Dim districtAvgScore As Double
    If districtParticipatingStudents > 0 Then
        districtAvgScore = Round(districtTotalScore / districtParticipatingStudents, 2)
    Else
        districtAvgScore = 0
    End If

    ' Fill data to statistics table
    wsStats.Cells(currentRow, 1).value = "" ' Leave school ID empty

    ' Merge cells B and C
    wsStats.Range(wsStats.Cells(currentRow, 2), wsStats.Cells(currentRow, 3)).Merge
    wsStats.Cells(currentRow, 2).value = "全区汇总"

    ' Fill statistics data
    wsStats.Cells(currentRow, 4).value = districtStatsArray(1) ' Actual students
    wsStats.Cells(currentRow, 5).value = districtStatsArray(2) ' Participating students

    For j = 6 To 18
        wsStats.Cells(currentRow, j).value = districtStatsArray(j - 3)
    Next j

    wsStats.Cells(currentRow, 19).value = districtAvgScore ' Average score

    ' Format district summary row
    With wsStats.Range(wsStats.Cells(currentRow, 1), wsStats.Cells(currentRow, 20))
        .Interior.Color = RGB(255, 255, 0) ' Yellow background
        .Font.Bold = True ' Bold
        .Font.Color = RGB(255, 0, 0) ' Red text
    End With

    ' Step 3: Calculate rankings
    ' Class ranking (sort by average score in descending order)
    Call BubbleSort(classRankArray, classCounter)

    ' School ranking (sort by average score in descending order)
    Call BubbleSort(schoolRankArray, schoolCounter)

    ' Fill class rankings
    For i = 1 To classCounter
        Dim classIndex As Long
        classIndex = classRankArray(i, 2)
        Dim classRowNum As Long
        classRowNum = 4 + classIndex

        ' Find corresponding row
        For j = 5 To currentRow - 1
            If wsStats.Cells(j, 1).value = classStatsArray(classIndex, 1) And _
               wsStats.Cells(j, 2).value = classStatsArray(classIndex, 2) And _
               wsStats.Cells(j, 3).value = classStatsArray(classIndex, 3) Then
                wsStats.Cells(j, 20).value = i
                Exit For
            End If
        Next j
    Next i

    ' Fill school rankings
    For i = 1 To schoolCounter
        Dim schoolIndex As Long
        schoolIndex = schoolRankArray(i, 2)

        ' Find corresponding row
        For j = 5 To currentRow - 1
            If Not wsStats.Cells(j, 3).MergeCells And _
               wsStats.Cells(j, 1).value = schoolStatsArray(schoolIndex, 1) And _
               wsStats.Cells(j, 2).value = schoolStatsArray(schoolIndex, 2) And _
               InStr(wsStats.Cells(j, 2).value & wsStats.Cells(j, 3).value, "汇总") > 0 Then
                wsStats.Cells(j, 20).value = i
                Exit For
            End If
        Next j
    Next i

    ' Step 4: Format cells
    ' Format data area
    With wsStats.Range(wsStats.Cells(5, 1), wsStats.Cells(currentRow, 20))
        .HorizontalAlignment = xlCenter ' Center horizontally
        .VerticalAlignment = xlCenter ' Center vertically
        .Borders.LineStyle = xlContinuous ' Add borders
        .Borders.Weight = xlThin ' Thin borders
    End With

    ' Set background color for columns A-T
    wsStats.Range(wsStats.Cells(5, 1), wsStats.Cells(currentRow, 20)).Interior.Color = RGB(255, 255, 204) ' Light yellow background

    ' Auto-fit column widths
    wsStats.Columns("A:T").AutoFit

CleanExit:
    ' Restore Excel settings
    With Application
        .ScreenUpdating = True
        .Calculation = xlCalculationAutomatic
        .EnableEvents = True
    End With
End Sub

' Bubble sort (sort by first column in descending order)
Sub BubbleSort(ByRef arr As Variant, ByVal count As Long)
    Dim i As Long, j As Long
    Dim tempVal As Variant

    For i = 1 To count - 1
        For j = i + 1 To count
            If arr(i, 1) < arr(j, 1) Then
                ' Swap first column (average score)
                tempVal = arr(i, 1)
                arr(i, 1) = arr(j, 1)
                arr(j, 1) = tempVal

                ' Swap second column (index)
                tempVal = arr(i, 2)
                arr(i, 2) = arr(j, 2)
                arr(j, 2) = tempVal

                ' Swap third column (if exists)
                If UBound(arr, 2) >= 3 Then
                    tempVal = arr(i, 3)
                    arr(i, 3) = arr(j, 3)
                    arr(j, 3) = tempVal
                End If
            End If
        Next j
    Next i
End Sub