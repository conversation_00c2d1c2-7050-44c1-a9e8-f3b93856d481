Attribute VB_Name = "考场编排"
Option Explicit

' 主程序入口
Sub 启动考场编排()
    ' 显示用户窗体
    frmExamRoomArrangement.Show
End Sub

' 执行考场编排
Sub 执行考场编排(ByVal desksPerRoom As Integer)
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long, j <PERSON> Long
    Dim currentGrade As String
    Dim currentClass As String
    Dim roomNumber As Integer
    Dim seatNumber As Integer
    Dim studentArray() As Variant
    Dim sortedArray() As Variant
    Dim gradeClassDict As Object
    Dim gradeClassKey As Variant
    Dim sequenceNumber As Long
    
    ' 设置工作表
    Set ws = ActiveSheet
    
    ' 获取最后一行
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    
    ' 检查数据是否存在
    If lastRow <= 1 Then
        MsgBox "没有找到学生数据，请检查表格！", vbExclamation
        Exit Sub
    End If
    
    ' 在H列生成随机数
    Application.ScreenUpdating = False
    For i = 2 To lastRow
        ws.Cells(i, "H").Value = Rnd()
    Next i
    
    ' 创建字典对象，按年级和班级分组
    Set gradeClassDict = CreateObject("Scripting.Dictionary")
    
    ' 将数据读入数组
    ReDim studentArray(1 To lastRow - 1, 1 To 8)
    For i = 2 To lastRow
        For j = 1 To 8
            studentArray(i - 1, j) = ws.Cells(i, j).Value
        Next j
    Next i
    
    ' 按年级和班级分组，并按随机数排序
    For i = 1 To UBound(studentArray)
        currentGrade = studentArray(i, 2) ' 年级
        currentClass = studentArray(i, 3) ' 班级
        
        ' 创建年级-班级组合键
        Dim key As String
        key = currentGrade & "-" & currentClass
        
        ' 如果字典中没有这个键，创建一个新数组
        If Not gradeClassDict.Exists(key) Then
            Dim newArray As New Collection
            gradeClassDict.Add key, newArray
        End If
        
        ' 将学生添加到相应的集合中
        gradeClassDict(key).Add i
    Next i
    
    ' 对每个年级-班级组内的学生按随机数排序
    For Each gradeClassKey In gradeClassDict.Keys
        Dim indices As Collection
        Set indices = gradeClassDict(gradeClassKey)
        
        ' 将集合转换为数组以便排序
        Dim indicesArray() As Long
        ReDim indicesArray(1 To indices.Count)
        For i = 1 To indices.Count
            indicesArray(i) = indices(i)
        Next i
        
        ' 按随机数排序
        Dim temp As Long
        For i = 1 To UBound(indicesArray)
            For j = i + 1 To UBound(indicesArray)
                If studentArray(indicesArray(i), 8) > studentArray(indicesArray(j), 8) Then
                    temp = indicesArray(i)
                    indicesArray(i) = indicesArray(j)
                    indicesArray(j) = temp
                End If
            Next j
        Next i
        
        ' 更新集合
        Set indices = New Collection
        For i = 1 To UBound(indicesArray)
            indices.Add indicesArray(i)
        Next i
        
        gradeClassDict(gradeClassKey) = indices
    Next gradeClassKey
    
    ' 初始化考场和座位
    roomNumber = 1
    seatNumber = 1
    sequenceNumber = 1
    
    ' 创建结果数组
    ReDim sortedArray(1 To UBound(studentArray), 1 To 8)
    
    ' 按年级顺序处理
    Dim grades As Collection
    Set grades = GetUniqueValues(studentArray, 2)
    
    For Each currentGrade In grades
        ' 获取当前年级的所有班级
        Dim classes As Collection
        Set classes = GetClassesForGrade(studentArray, currentGrade)
        
        ' 按班级顺序处理
        For Each currentClass In classes
            key = currentGrade & "-" & currentClass
            
            ' 如果存在这个年级-班级组合
            If gradeClassDict.Exists(key) Then
                Dim studentIndices As Collection
                Set studentIndices = gradeClassDict(key)
                
                ' 按排序后的顺序分配考场和座位
                For i = 1 To studentIndices.Count
                    Dim studentIndex As Long
                    studentIndex = studentIndices(i)
                    
                    ' 分配考场和座位
                    studentArray(studentIndex, 6) = roomNumber
                    studentArray(studentIndex, 7) = seatNumber
                    
                    ' 更新座位号
                    seatNumber = seatNumber + 1
                    
                    ' 如果达到考场容量，切换到下一个考场
                    If seatNumber > desksPerRoom Then
                        roomNumber = roomNumber + 1
                        seatNumber = 1
                    End If
                    
                    ' 将学生添加到排序后的数组
                    For j = 1 To 8
                        sortedArray(sequenceNumber, j) = studentArray(studentIndex, j)
                    Next j
                    
                    sequenceNumber = sequenceNumber + 1
                Next i
            End If
        Next currentClass
    Next currentGrade
    
    ' 将排序后的数据写回工作表
    For i = 1 To UBound(sortedArray)
        For j = 1 To 8
            ws.Cells(i + 1, j).Value = sortedArray(i, j)
        Next j
    Next i
    
    ' 按考场和座位号排序
    ws.Range("A1:H" & lastRow).Sort _
        Key1:=ws.Range("F2"), Order1:=xlAscending, _
        Key2:=ws.Range("G2"), Order2:=xlAscending, _
        Header:=xlYes
    
    Application.ScreenUpdating = True
    
    MsgBox "考场编排完成！共使用 " & roomNumber & " 个考场。", vbInformation
End Sub

' 获取数组中指定列的唯一值
Function GetUniqueValues(arr As Variant, colIndex As Integer) As Collection
    Dim dict As Object
    Dim i As Long
    Dim result As New Collection
    
    Set dict = CreateObject("Scripting.Dictionary")
    
    ' 收集唯一值
    For i = 1 To UBound(arr)
        If Not IsEmpty(arr(i, colIndex)) And Not dict.Exists(arr(i, colIndex)) Then
            dict.Add arr(i, colIndex), 1
            result.Add arr(i, colIndex)
        End If
    Next i
    
    Set GetUniqueValues = result
End Function

' 获取指定年级的所有班级
Function GetClassesForGrade(arr As Variant, grade As String) As Collection
    Dim dict As Object
    Dim i As Long
    Dim result As New Collection
    
    Set dict = CreateObject("Scripting.Dictionary")
    
    ' 收集指定年级的所有班级
    For i = 1 To UBound(arr)
        If arr(i, 2) = grade And Not IsEmpty(arr(i, 3)) And Not dict.Exists(arr(i, 3)) Then
            dict.Add arr(i, 3), 1
            result.Add arr(i, 3)
        End If
    Next i
    
    Set GetClassesForGrade = result
End Function
