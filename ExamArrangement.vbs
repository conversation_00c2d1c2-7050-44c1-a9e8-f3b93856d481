Option Explicit

' 常量定义
Const COL_SCHOOL As Integer = 1     ' A列：学校
Const COL_GRADE As Integer = 2      ' B列：年级
Const COL_CLASS As Integer = 3      ' C列：班级
Const COL_EXAM_NO As Integer = 4    ' D列：准考号
Const COL_NAME As Integer = 5       ' E列：姓名
Const COL_ROOM As Integer = 6       ' F列：考场号
Const COL_SEAT As Integer = 7       ' G列：座位号
Const COL_RANDOM As Integer = 8     ' H列：随机数
Const COL_CLASS_ORDER As Integer = 9 ' I列：班内序号

Sub ArrangeExamRooms()
    Dim ws As Worksheet
    Dim lastRow As Long, i <PERSON>, j As Long
    Dim roomSize As Integer
    Dim dataArr() As Variant
    Dim currentRoom As Integer, currentSeat As Integer
    Dim currentGrade As String, prevGrade As String
    
    ' 设置工作表
    Set ws = ActiveSheet
    
    ' 获取用户输入的考场规格
    roomSize = Application.InputBox("请输入考场规格（25或30人）:", "考场编排", Type:=1)
    If roomSize <> 25 And roomSize <> 30 Then
        MsgBox "请输入有效的考场规格（25或30）!", vbExclamation
        Exit Sub
    End If
    
    ' 获取数据范围
    lastRow = ws.Cells(ws.Rows.Count, COL_SCHOOL).End(xlUp).Row
    If lastRow < 2 Then
        MsgBox "没有找到学生数据!", vbExclamation
        Exit Sub
    End If
    
    ' 将数据加载到数组
    ReDim dataArr(1 To lastRow - 1, 1 To COL_CLASS_ORDER)
    For i = 2 To lastRow
        For j = 1 To COL_CLASS_ORDER
            If j <= 5 Then
                dataArr(i - 1, j) = ws.Cells(i, j).Value
            ElseIf j = COL_RANDOM Then
                ' 生成0-1之间的随机数
                dataArr(i - 1, j) = Rnd()
            End If
        Next j
    Next i
    
    ' 第一次排序：年级升序、班级升序
    QuickSortMulti dataArr, COL_GRADE, COL_CLASS
    
    ' 第二次排序：年级升序、班级升序、随机数升序，生成班内序号
    QuickSortMulti dataArr, COL_GRADE, COL_CLASS, COL_RANDOM
    
    ' 生成班内序号
    Dim classCount As Integer
    Dim currentClass As String
    classCount = 1
    currentGrade = dataArr(1, COL_GRADE)
    currentClass = dataArr(1, COL_CLASS)
    
    For i = 1 To UBound(dataArr, 1)
        If dataArr(i, COL_GRADE) <> currentGrade Or dataArr(i, COL_CLASS) <> currentClass Then
            currentGrade = dataArr(i, COL_GRADE)
            currentClass = dataArr(i, COL_CLASS)
            classCount = 1
        End If
        dataArr(i, COL_CLASS_ORDER) = classCount
        classCount = classCount + 1
    Next i
    
    ' 第三次排序：年级升序、班内序号升序
    QuickSortMulti dataArr, COL_GRADE, COL_CLASS_ORDER
    
    ' 分配考场号和座位号
    currentRoom = 1
    currentSeat = 1
    prevGrade = dataArr(1, COL_GRADE)
    
    For i = 1 To UBound(dataArr, 1)
        If dataArr(i, COL_GRADE) <> prevGrade Then
            currentRoom = currentRoom + 1
            currentSeat = 1
            prevGrade = dataArr(i, COL_GRADE)
        End If
        
        dataArr(i, COL_ROOM) = currentRoom
        dataArr(i, COL_SEAT) = currentSeat
        
        currentSeat = currentSeat + 1
        If currentSeat > roomSize Then
            currentRoom = currentRoom + 1
            currentSeat = 1
        End If
    Next i
    
    ' 将结果写回工作表
    For i = 1 To UBound(dataArr, 1)
        For j = 1 To COL_CLASS_ORDER
            ws.Cells(i + 1, j).Value = dataArr(i, j)
        Next j
    Next i
    
    MsgBox "考场编排完成!", vbInformation
End Sub

' 快速排序算法（支持多列排序）
Sub QuickSortMulti(arr() As Variant, col1 As Integer, Optional col2 As Integer = -1, Optional col3 As Integer = -1)
    Dim cols(1 To 3) As Integer
    cols(1) = col1
    cols(2) = col2
    cols(3) = col3
    QuickSortMultiInternal arr, LBound(arr), UBound(arr), cols
End Sub

Sub QuickSortMultiInternal(arr() As Variant, ByVal low As Long, ByVal high As Long, cols() As Integer)
    If low < high Then
        Dim pivot As Long
        pivot = PartitionMulti(arr, low, high, cols)
        QuickSortMultiInternal arr, low, pivot - 1, cols
        QuickSortMultiInternal arr, pivot + 1, high, cols
    End If
End Sub

Function PartitionMulti(arr() As Variant, ByVal low As Long, ByVal high As Long, cols() As Integer) As Long
    Dim pivot As Variant
    pivot = arr(high, cols(1))
    
    Dim i As Long
    i = low - 1
    
    Dim j As Long
    For j = low To high - 1
        If CompareRows(arr, j, high, cols) <= 0 Then
            i = i + 1
            SwapRows arr, i, j
        End If
    Next j
    
    SwapRows arr, i + 1, high
    PartitionMulti = i + 1
End Function

Function CompareRows(arr() As Variant, row1 As Long, row2 As Long, cols() As Integer) As Integer
    Dim i As Integer
    For i = 1 To UBound(cols)
        If cols(i) = -1 Then Exit For  ' 跳过无效排序列
        If arr(row1, cols(i)) < arr(row2, cols(i)) Then
            CompareRows = -1
            Exit Function
        ElseIf arr(row1, cols(i)) > arr(row2, cols(i)) Then
            CompareRows = 1
            Exit Function
        End If
    Next i
    CompareRows = 0
End Function

Sub SwapRows(arr() As Variant, row1 As Long, row2 As Long)
    If row1 = row2 Then Exit Sub
    
    Dim temp As Variant
    Dim i As Integer
    For i = LBound(arr, 2) To UBound(arr, 2)
        temp = arr(row1, i)
        arr(row1, i) = arr(row2, i)
        arr(row2, i) = temp
    Next i
End Sub