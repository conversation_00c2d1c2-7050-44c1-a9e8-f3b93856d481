<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>乘法连线游戏</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin: 20px;
        }
        .container {
            display: flex;
            justify-content: space-around;
            max-width: 800px;
            margin: 0 auto;
        }
        .column {
            display: flex;
            flex-direction: column;
            align-items: center; /* 使内容居中 */
            width: 45%;
        }
        .problem, .answer {
            padding: 10px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .problem {
            background-color: #f0f8ff;
        }
        .answer {
            background-color: #fff0f5;
        }
        .selected {
            background-color: #ffeb3b !important;
        }
        .matched {
            background-color: #a5d6a7 !important;
        }
    </style>
</head>
<body>
    <h1>乘法连线游戏</h1>
    <p>点击左边的乘法问题，然后点击右边的正确答案进行连线</p>
    
    <div class="container">
        <div class="column" id="problems">
            <!-- 乘法问题将在这里生成 -->
        </div>
        
        <div class="column" id="answers">
            <!-- 答案将在这里生成 -->
        </div>
    </div>

    <script>
        // 生成10个随机的乘法问题
        function generateProblems() {
            const problems = [];
            
            for (let i = 0; i < 10; i++) {
                const num1 = Math.floor(Math.random() * 9) + 1; // 1-9
                const num2 = Math.floor(Math.random() * 9) + 1; // 1-9
                problems.push({
                    text: `${num1} × ${num2}`,
                    answer: num1 * num2
                });
                
                // Shuffle problems array for variety
                problems.sort(() => Math.random() - 0.5);
                
                return problems;
                
                
                
                
                
                
                
                
                

        
// Create problem and answer elements

function createGameElements(problems) {

const problemsContainer = document.getElementById('problems');

const answersContainer = document.getElementById('answers');

// Shuffle answers separately

const shuffledAnswers = [...new Set(problems.map(p => p.answer))].sort(() => Math.random() - 






0.5);

// Create problem elements

problems.forEach((problem, index) => {

const problemElement = document.createElement('div');

problemElement.className = 'problem';

problemElement.textContent = problem.text;

problemElement.dataset.index = index;

problemsContainer.appendChild(problemElement);

});

// Create answer elements

shuffledAnswers.forEach((answer, index) => {

const answerElement = document.createElement('div');

answerElement.className = 'answer';

answerElement.textContent = answer;

answersContainer.appendChild(answerElement);

});

}








// Game logic

let selectedProblemIndex = null;

function setupGameLogic() {

const problemsElements = document.querySelectorAll('.problem');

const answersElements = document.querySelectorAll('.answer');








problemsElements.forEach(problem => {

problem.addEventListener('click', () => {

// Remove previous selection

document.querySelectorAll('.selected').forEach(el => el.classList.remove('selected'));






// Select current problem

problem.classList.add('selected');

selectedProblemIndex =

parseInt(problem.dataset.index);

});

});








answersElements.forEach(answer => {

answer.addEventListener('click', () => {

if (selectedProblemIndex !== null) {

const problems =

JSON.parse(sessionStorage.getItem('multiplicationProblems'));

const correctAnswer =

problems[selectedProblemIndex].answer;

if (parseInt(answer.textContent) === correctAnswer) {

// Correct match





document.querySelector(`.problem[data-index="${selectedProblemIndex}"]`).classList.add('matched');






answer.classList.add('matched');

} else {

// Incorrect match - flash red briefly

answer.style.backgroundColor =

'#ffcdd2';

setTimeout(() => {


if (!answer.classList.contains('matched')) {


answer.style.backgroundColor =

'';


}


}, 

500);


}




}

});

});

}








// Initialize the game

window.onload =

function() {


const problems =

generateProblems();


sessionStorage.setItem('multiplicationProblems',

JSON.stringify(problems));

createGameElements(problems);


setupGameLogic();

};

</script>

</body>

</html>


