Attribute VB_Name = "ExamRoomArrangement"
Option Explicit

' 常量定义
Const COL_SCHOOL = 1       ' 学校 (A列)
Const COL_GRADE = 2        ' 年级 (B列)
Const COL_CLASS = 3        ' 班级 (C列)
Const COL_EXAM_NO = 4      ' 准考号 (D列)
Const COL_NAME = 5         ' 姓名 (E列)
Const COL_ROOM_NO = 6      ' 考场号 (F列)
Const COL_SEAT_NO = 7      ' 座位号 (G列)
Const COL_RANDOM = 8       ' 随机数 (H列)

' 主程序入口
Sub 启动考场编排()
    ' 显示用户窗体
    frmExamRoomSettings.Show
End Sub

' 执行考场编排
Sub ArrangeExamRooms(ByVal desksPerRoom As Integer)
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long, j As Long
    Dim studentArray() As Variant
    Dim currentGrade As String
    Dim prevGrade As String
    Dim roomNumber As Integer
    Dim seatNumber As Integer
    
    ' 设置工作表
    Set ws = ActiveSheet
    
    ' 获取最后一行
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row
    
    ' 检查数据是否存在
    If lastRow <= 1 Then
        MsgBox "没有找到学生数据，请检查表格！", vbExclamation
        Exit Sub
    End If
    
    ' 禁用屏幕更新以提高性能
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    
    ' 将数据读入数组
    ReDim studentArray(1 To lastRow - 1, 1 To 8)
    For i = 2 To lastRow
        For j = 1 To 7
            studentArray(i - 1, j) = ws.Cells(i, j).Value
        Next j
        ' 在H列生成随机数 (0-1之间)
        studentArray(i - 1, 8) = Rnd()
    Next i
    
    ' 第一次排序：按年级和班级排序
    studentArray = SortByGradeAndClass(studentArray)
    
    ' 第二次排序：在每个年级和班级内按随机数排序
    studentArray = SortByRandomWithinGradeClass(studentArray)
    
    ' 第三次排序：按年级和班内序号排序
    studentArray = SortByGradeAndSequence(studentArray)
    
    ' 分配考场和座位
    roomNumber = 1
    seatNumber = 1
    prevGrade = ""
    
    For i = 1 To UBound(studentArray)
        currentGrade = studentArray(i, COL_GRADE)
        
        ' 当年级变化时，考场号加1，座位号从1开始重新编排
        If currentGrade <> prevGrade And prevGrade <> "" Then
            roomNumber = roomNumber + 1
            seatNumber = 1
        End If
        
        ' 分配考场和座位
        studentArray(i, COL_ROOM_NO) = roomNumber
        studentArray(i, COL_SEAT_NO) = seatNumber
        
        ' 更新座位号
        seatNumber = seatNumber + 1
        
        ' 如果达到考场容量，切换到下一个考场
        If seatNumber > desksPerRoom Then
            roomNumber = roomNumber + 1
            seatNumber = 1
        End If
        
        prevGrade = currentGrade
    Next i
    
    ' 将结果写回工作表
    For i = 1 To UBound(studentArray)
        For j = 1 To 7  ' 只写回A-G列
            ws.Cells(i + 1, j).Value = studentArray(i, j)
        Next j
    Next i
    
    ' 恢复屏幕更新
    Application.Calculation = xlCalculationAutomatic
    Application.ScreenUpdating = True
    
    MsgBox "考场编排完成！共使用 " & roomNumber & " 个考场。", vbInformation
End Sub

' 按年级和班级排序
Function SortByGradeAndClass(arr As Variant) As Variant
    Dim i As Long, j As Long
    Dim temp As Variant
    Dim n As Long
    
    n = UBound(arr)
    
    ' 冒泡排序
    For i = 1 To n - 1
        For j = 1 To n - i
            ' 比较年级
            If CompareValues(arr(j, COL_GRADE), arr(j + 1, COL_GRADE)) > 0 Then
                SwapRows arr, j, j + 1
            ' 如果年级相同，比较班级
            ElseIf arr(j, COL_GRADE) = arr(j + 1, COL_GRADE) And _
                   CompareValues(arr(j, COL_CLASS), arr(j + 1, COL_CLASS)) > 0 Then
                SwapRows arr, j, j + 1
            End If
        Next j
    Next i
    
    SortByGradeAndClass = arr
End Function

' 在每个年级和班级内按随机数排序
Function SortByRandomWithinGradeClass(arr As Variant) As Variant
    Dim i As Long, j As Long
    Dim startIdx As Long, endIdx As Long
    Dim currentGrade As String, currentClass As String
    Dim n As Long
    
    n = UBound(arr)
    startIdx = 1
    
    Do While startIdx <= n
        currentGrade = arr(startIdx, COL_GRADE)
        currentClass = arr(startIdx, COL_CLASS)
        endIdx = startIdx
        
        ' 找到当前年级和班级的结束索引
        Do While endIdx < n And arr(endIdx + 1, COL_GRADE) = currentGrade And arr(endIdx + 1, COL_CLASS) = currentClass
            endIdx = endIdx + 1
        Loop
        
        ' 在当前年级和班级内按随机数排序
        For i = startIdx To endIdx - 1
            For j = i + 1 To endIdx
                If arr(i, COL_RANDOM) > arr(j, COL_RANDOM) Then
                    SwapRows arr, i, j
                End If
            Next j
        Next i
        
        ' 移动到下一个年级和班级
        startIdx = endIdx + 1
    Loop
    
    SortByRandomWithinGradeClass = arr
End Function

' 按年级和班内序号排序
Function SortByGradeAndSequence(arr As Variant) As Variant
    Dim i As Long, j As Long
    Dim startIdx As Long, endIdx As Long
    Dim currentGrade As String
    Dim n As Long
    Dim sequenceDict As Object
    Dim key As String
    Dim sequence As Long
    
    n = UBound(arr)
    Set sequenceDict = CreateObject("Scripting.Dictionary")
    
    ' 为每个学生分配班内序号
    For i = 1 To n
        key = arr(i, COL_GRADE) & "-" & arr(i, COL_CLASS)
        
        If Not sequenceDict.Exists(key) Then
            sequenceDict.Add key, 1
        End If
        
        ' 分配班内序号
        sequence = sequenceDict(key)
        arr(i, COL_RANDOM) = sequence  ' 临时使用随机数列存储班内序号
        sequenceDict(key) = sequence + 1
    Next i
    
    ' 按年级和班内序号排序
    startIdx = 1
    
    Do While startIdx <= n
        currentGrade = arr(startIdx, COL_GRADE)
        endIdx = startIdx
        
        ' 找到当前年级的结束索引
        Do While endIdx < n And arr(endIdx + 1, COL_GRADE) = currentGrade
            endIdx = endIdx + 1
        Loop
        
        ' 在当前年级内按班内序号排序
        For i = startIdx To endIdx - 1
            For j = i + 1 To endIdx
                If arr(i, COL_RANDOM) > arr(j, COL_RANDOM) Then
                    SwapRows arr, i, j
                End If
            Next j
        Next i
        
        ' 移动到下一个年级
        startIdx = endIdx + 1
    Loop
    
    SortByGradeAndSequence = arr
End Function

' 交换数组中的两行
Sub SwapRows(arr As Variant, row1 As Long, row2 As Long)
    Dim j As Long
    Dim temp As Variant
    
    For j = LBound(arr, 2) To UBound(arr, 2)
        temp = arr(row1, j)
        arr(row1, j) = arr(row2, j)
        arr(row2, j) = temp
    Next j
End Sub

' 比较两个值（处理数字和文本）
Function CompareValues(val1 As Variant, val2 As Variant) As Integer
    ' 如果两个值都是数字，则进行数值比较
    If IsNumeric(val1) And IsNumeric(val2) Then
        If CDbl(val1) < CDbl(val2) Then
            CompareValues = -1
        ElseIf CDbl(val1) > CDbl(val2) Then
            CompareValues = 1
        Else
            CompareValues = 0
        End If
    ' 否则进行文本比较
    Else
        If CStr(val1) < CStr(val2) Then
            CompareValues = -1
        ElseIf CStr(val1) > CStr(val2) Then
            CompareValues = 1
        Else
            CompareValues = 0
        End If
    End If
End Function
