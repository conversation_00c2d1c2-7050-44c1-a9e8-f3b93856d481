'------------------------------------------------------------------------------
' 模块：班级成绩报表
' 功能：生成按年级统计的班级成绩报表
' 说明：从成绩总表中提取指定年级的成绩数据，从任课教师表中获取任课教师信息，
'      并自动计算各班级的各科目平均分和名次，填充到班级成绩报表中。
'------------------------------------------------------------------------------

'------------------------------------------------------------------------------
' 主过程：生成班级成绩报表
'------------------------------------------------------------------------------
Sub 生成班级成绩报表()
    ' 工作表变量
    Dim ws成绩总表 As Worksheet, ws任课教师 As Worksheet, ws班级成绩报表 As Worksheet
    
    ' 基本参数
    Dim 年级 As String
    Dim 当前行 As Long
    Dim 科目列表() As String
    Dim 科目数量 As Integer
    Dim 科目列号() As Integer
    
    ' 数据存储
    Dim 成绩数据() As Variant  ' 用于存储成绩总表数据
    Dim 任课教师数据() As Variant  ' 用于存储任课教师表数据
    Dim 学校班级字典 As Object  ' 存储学校和班级信息
    
    ' 排名相关变量
    Dim 班级得分数组() As Variant  ' 用于存储班级得分和行号，以便排序
    Dim 班级数量 As Long  ' 用于记录班级总数
    
    ' 循环变量
    Dim i As Long, j As Long, k As Long, m As Long
    Dim 学校Key As Variant, 班级Key As Variant
    Dim 学校信息 As Variant
    
    ' 数据变量
    Dim 当前学校序号 As String, 当前学校 As String, 当前校点 As String, 当前班级 As String
    Dim 当前学校序号值 As String, 当前学校值 As String, 当前校点值 As String
    Dim 学校键值 As String
    Dim 任课教师 As String
    
    ' 统计变量
    Dim 成绩 As Double
    Dim 实考人数 As Long
    Dim 总分() As Double
    Dim 平均分() As Double
    
    ' 使用结构化错误处理
    On Error GoTo ErrorHandler
    
    ' 获取工作表
    Set ws成绩总表 = ThisWorkbook.Worksheets("成绩总表")
    
    ' 检查任课教师表是否存在
    Dim 任课教师表存在 As Boolean
    任课教师表存在 = False
    For Each ws In ThisWorkbook.Worksheets
        If ws.Name = "任课教师" Then
            任课教师表存在 = True
            Set ws任课教师 = ws
            Exit For
        End If
    Next ws
    
    ' 如果任课教师表不存在，提示用户
    If Not 任课教师表存在 Then
        MsgBox "未找到'任课教师'工作表，将无法获取任课教师信息。", vbExclamation
    End If
    
    ' 检查班级成绩报表表是否存在，如果不存在则创建
    Dim 班级成绩报表存在 As Boolean
    班级成绩报表存在 = False
    For Each ws In ThisWorkbook.Worksheets
        If ws.Name = "班级成绩报表" Then
            班级成绩报表存在 = True
            Set ws班级成绩报表 = ws
            Exit For
        End If
    Next ws
    
    ' 如果班级成绩报表不存在，则创建
    If Not 班级成绩报表存在 Then
        Set ws班级成绩报表 = ThisWorkbook.Worksheets.Add(After:=ThisWorkbook.Worksheets(ThisWorkbook.Worksheets.Count))
        ws班级成绩报表.Name = "班级成绩报表"
    End If
    
    ' 应用屏幕更新关闭，提高性能
    Application.ScreenUpdating = False
    
    ' 获取年级
    年级 = InputBox("请输入要统计的年级:", "年级选择", "七年级")
    
    ' 检查年级是否有效
    If 年级 = "" Then
        MsgBox "未输入年级，操作取消!", vbExclamation
        GoTo CleanExit
    End If
    
    ' 将数据加载到数组中，减少工作表访问，提高性能
    成绩数据 = ws成绩总表.UsedRange.Value
    If 任课教师表存在 Then
        任课教师数据 = ws任课教师.UsedRange.Value
    End If
    
    ' 获取科目列表
    Dim 科目列表集合 As New Collection
    On Error Resume Next ' 忽略重复项错误
    For i = 1 To UBound(成绩数据, 2)
        If 成绩数据(1, i) <> "" And _
           成绩数据(1, i) <> "年级" And _
           成绩数据(1, i) <> "学校序号" And _
           成绩数据(1, i) <> "学校" And _
           成绩数据(1, i) <> "校点" And _
           成绩数据(1, i) <> "班级" And _
           成绩数据(1, i) <> "姓名" And _
           成绩数据(1, i) <> "学号" And _
           成绩数据(1, i) <> "性别" And _
           成绩数据(1, i) <> "备注" Then
            科目列表集合.Add 成绩数据(1, i), CStr(成绩数据(1, i))
        End If
    Next i
    On Error GoTo ErrorHandler ' 恢复错误处理
    
    ' 将科目列表集合转换为数组
    科目数量 = 科目列表集合.Count
    ReDim 科目列表(1 To 科目数量)
    ReDim 科目列号(1 To 科目数量)
    
    For i = 1 To 科目数量
        科目列表(i) = 科目列表集合(i)
    Next i
    
    ' 查找科目在成绩总表中的列号
    For i = 1 To 科目数量
        科目列号(i) = 查找列号(成绩数据, 科目列表(i))
        
        ' 如果找不到科目，则退出
        If 科目列号(i) = 0 Then
            MsgBox "在成绩总表中找不到科目 """ & 科目列表(i) & """!", vbExclamation
            GoTo CleanExit
        End If
    Next i
    
    ' 统计学校和班级数量
    Set 学校班级字典 = CreateObject("Scripting.Dictionary")
    
    ' 开始收集学校和班级信息
    ' 遍历成绩总表数据，收集所有符合年级条件的学校和班级
    For i = 2 To UBound(成绩数据, 1)
        ' 检查年级是否匹配
        If 成绩数据(i, 1) = 年级 Then
            当前学校序号 = CStr(成绩数据(i, 2))
            当前学校 = CStr(成绩数据(i, 3))
            当前校点 = CStr(成绩数据(i, 4))
            当前班级 = CStr(成绩数据(i, 5))
            
            ' 将学校和班级添加到字典中
            学校键值 = 当前学校序号 & "|" & 当前学校 & "|" & 当前校点
            
            If Not 学校班级字典.Exists(学校键值) Then
                学校班级字典.Add 学校键值, CreateObject("Scripting.Dictionary")
            End If
            
            If Not 学校班级字典(学校键值).Exists(当前班级) Then
                学校班级字典(学校键值).Add 当前班级, True
            End If
        End If
    Next i
    
    ' 清空班级成绩报表表
    ws班级成绩报表.Cells.Clear
    
    ' 创建表头
    ws班级成绩报表.Cells(1, 1).Value = "年级：" & 年级
    ws班级成绩报表.Cells(1, 1).Font.Bold = True
    ws班级成绩报表.Cells(1, 1).Font.Size = 14
    
    ' 添加表头
    ws班级成绩报表.Cells(3, 1).Value = "序号"
    ws班级成绩报表.Cells(3, 2).Value = "学校"
    ws班级成绩报表.Cells(3, 3).Value = "校点"
    ws班级成绩报表.Cells(3, 4).Value = "班级"
    ws班级成绩报表.Cells(3, 5).Value = "实考人数"
    
    ' 添加科目表头
    For i = 1 To 科目数量
        ws班级成绩报表.Cells(3, 5 + i).Value = 科目列表(i)
        ws班级成绩报表.Cells(3, 5 + 科目数量 + i).Value = 科目列表(i) & "名次"
        ws班级成绩报表.Cells(3, 5 + 科目数量 * 2 + i).Value = 科目列表(i) & "任课教师"
    Next i
    
    ' 设置表头格式
    With ws班级成绩报表.Range(ws班级成绩报表.Cells(3, 1), ws班级成绩报表.Cells(3, 5 + 科目数量 * 3))
        .Font.Bold = True
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
        .Interior.Color = RGB(217, 217, 217) ' 浅灰色背景
        .Borders.LineStyle = xlContinuous
        .Borders.Weight = xlThin
        .Borders.ColorIndex = xlAutomatic
    End With
    
    ' 首先计算班级数量，以便初始化数组
    班级数量 = 0
    For Each 学校Key In 学校班级字典.Keys
        For Each 班级Key In 学校班级字典(学校Key).Keys
            班级数量 = 班级数量 + 1
        Next 班级Key
    Next 学校Key
    
    ' 初始化班级得分数组（每个科目一个）
    ReDim 班级得分数组(1 To 科目数量, 1 To 班级数量, 1 To 3)  ' 维度：科目、班级、属性（1=平均分，2=行号，3=学校班级标识）
    
    ' 当前行从第4行开始（表头在第3行）
    当前行 = 4
    Dim 序号 As Long
    序号 = 1
    
    ' 遍历每个学校和班级进行统计
    For Each 学校Key In 学校班级字典.Keys
        学校信息 = Split(学校Key, "|")
        当前学校序号值 = 学校信息(0)
        当前学校值 = 学校信息(1)
        当前校点值 = 学校信息(2)
        
        ' 遍历该学校的每个班级
        For Each 班级Key In 学校班级字典(学校Key).Keys
            当前班级 = 班级Key
            
            ' 初始化统计数据
            实考人数 = 0
            ReDim 总分(1 To 科目数量)
            ReDim 平均分(1 To 科目数量)
            
            For j = 1 To 科目数量
                总分(j) = 0
                平均分(j) = 0
            Next j
            
            ' 统计实考人数和各科目总分
            For i = 2 To UBound(成绩数据, 1)
                If 成绩数据(i, 1) = 年级 And _
                   成绩数据(i, 2) = 当前学校序号值 And _
                   成绩数据(i, 3) = 当前学校值 And _
                   成绩数据(i, 4) = 当前校点值 And _
                   成绩数据(i, 5) = 当前班级 Then
                    
                    ' 实考人数加1
                    实考人数 = 实考人数 + 1
                    
                    ' 统计各科目总分
                    For j = 1 To 科目数量
                        成绩 = Val(成绩数据(i, 科目列号(j)))
                        If 成绩 > 0 Then
                            总分(j) = 总分(j) + 成绩
                        End If
                    Next j
                End If
            Next i
            
            ' 计算各科目平均分
            For j = 1 To 科目数量
                If 实考人数 > 0 Then
                    平均分(j) = 总分(j) / 实考人数
                Else
                    平均分(j) = 0
                End If
            Next j
            
            ' 填充班级基本信息
            ws班级成绩报表.Cells(当前行, 1).Value = 序号
            ws班级成绩报表.Cells(当前行, 2).Value = 当前学校值
            ws班级成绩报表.Cells(当前行, 3).Value = 当前校点值
            ws班级成绩报表.Cells(当前行, 4).Value = 当前班级
            ws班级成绩报表.Cells(当前行, 5).Value = 实考人数
            
            ' 填充各科目平均分
            For j = 1 To 科目数量
                ws班级成绩报表.Cells(当前行, 5 + j).Value = 平均分(j)
                ws班级成绩报表.Cells(当前行, 5 + j).NumberFormat = "0.00"
            Next j
            
            ' 记录班级平均分和行号，以便后续排名
            For j = 1 To 科目数量
                Static 班级计数() As Long
                If j = 1 Then
                    ReDim 班级计数(1 To 科目数量)
                End If
                班级计数(j) = 班级计数(j) + 1
                
                ' 存储平均分、行号和学校班级标识
                班级得分数组(j, 班级计数(j), 1) = 平均分(j)  ' 平均分
                班级得分数组(j, 班级计数(j), 2) = 当前行  ' 行号
                班级得分数组(j, 班级计数(j), 3) = 当前学校值 & "-" & 当前班级  ' 学校班级标识
            Next j
            
            ' 获取任课教师信息
            If 任课教师表存在 Then
                For j = 1 To 科目数量
                    任课教师 = ""
                    For k = 2 To UBound(任课教师数据, 1)
                        If 任课教师数据(k, 1) = 当前学校值 And _
                           任课教师数据(k, 2) = 当前班级 And _
                           任课教师数据(k, 3) = 年级 Then
                            ' 查找科目在任课教师表中的列号
                            For m = 4 To UBound(任课教师数据, 2)
                                If 任课教师数据(1, m) = 科目列表(j) Then
                                    任课教师 = 任课教师数据(k, m)
                                    Exit For
                                End If
                            Next m
                            Exit For
                        End If
                    Next k
                    
                    ' 填充任课教师
                    ws班级成绩报表.Cells(当前行, 5 + 科目数量 * 2 + j).Value = 任课教师
                Next j
            End If
            
            ' 移动到下一行
            当前行 = 当前行 + 1
            序号 = 序号 + 1
        Next 班级Key
    Next 学校Key
    
    ' 计算并填充各科目排名
    For j = 1 To 科目数量
        Call 计算科目排名(ws班级成绩报表, 班级得分数组, j, 班级数量, 5 + 科目数量 + j)
    Next j
    
    ' 添加边框
    Dim 数据区域 As Range
    Set 数据区域 = ws班级成绩报表.Range(ws班级成绩报表.Cells(3, 1), ws班级成绩报表.Cells(当前行 - 1, 5 + 科目数量 * 3))
    With 数据区域.Borders
        .LineStyle = xlContinuous
        .Weight = xlThin
        .ColorIndex = xlAutomatic
    End With
    
    ' 自动调整列宽
    ws班级成绩报表.UsedRange.Columns.AutoFit
    
    ' 冻结窗格（前5列和前3行）
    ws班级成绩报表.Activate
    ActiveWindow.FreezePanes = False
    ws班级成绩报表.Cells(4, 6).Select
    ActiveWindow.FreezePanes = True
    
    ' 提示完成
    MsgBox "班级成绩报表生成完成！共统计了 " & 当前行 - 4 & " 个班级。", vbInformation
    
    ' 清除错误处理
    On Error GoTo 0
    
    ' 正常退出点
CleanExit:
    ' 恢复屏幕更新
    Application.ScreenUpdating = True
    Exit Sub
    
    ' 错误处理
ErrorHandler:
    MsgBox "程序运行时发生错误：" & Err.Description, vbCritical
    Resume CleanExit
End Sub

'------------------------------------------------------------------------------
' 辅助函数：查找列号
' 功能：在数组的第一行中查找指定值的列号
' 参数：
'   - 数据数组：要搜索的二维数组
'   - 查找值：要查找的值
' 返回：如果找到返回列号，否则返回0
'------------------------------------------------------------------------------
Function 查找列号(数据数组 As Variant, 查找值 As String) As Long
    Dim i As Long
    查找列号 = 0
    
    ' 添加数组边界检查
    If Not IsArray(数据数组) Then Exit Function
    If UBound(数据数组, 1) < 1 Then Exit Function
    
    For i = 1 To UBound(数据数组, 2)
        If CStr(数据数组(1, i)) = 查找值 Then
            查找列号 = i
            Exit Function
        End If
    Next i
End Function

'------------------------------------------------------------------------------
' 辅助函数：获取数值
' 功能：将变量转换为数值，处理非数值情况
' 参数：
'   - value：要转换的值
' 返回：数值结果，如果非数值则返回0
'------------------------------------------------------------------------------
Function Val(value As Variant) As Double
    If IsNumeric(value) Then
        Val = CDbl(value)
    Else
        Val = 0
    End If
End Function

'------------------------------------------------------------------------------
' 辅助函数：计算科目排名
' 功能：根据班级平均分计算排名并填充到班级成绩报表中
' 参数：
'   - ws班级成绩报表：班级成绩报表工作表
'   - 班级得分数组：存储班级平均分和行号的数组
'   - 科目索引：当前处理的科目索引
'   - 班级数量：班级总数
'   - 排名列号：排名要填充的列号
'------------------------------------------------------------------------------
Sub 计算科目排名(ws班级成绩报表 As Worksheet, 班级得分数组 As Variant, 科目索引 As Long, 班级数量 As Long, 排名列号 As Long)
    Dim i As Long, j As Long
    Dim 临时得分 As Double, 临时行号 As Long, 临时标识 As String
    Dim 排名数组() As Long
    
    ' 如果班级数量小于1，则无需排名
    If 班级数量 < 1 Then Exit Sub
    
    ' 初始化排名数组
    ReDim 排名数组(1 To 班级数量)
    
    ' 对班级得分数组按平均分降序排序（冒泡排序）
    For i = 1 To 班级数量 - 1
        For j = i + 1 To 班级数量
            If 班级得分数组(科目索引, i, 1) < 班级得分数组(科目索引, j, 1) Then
                ' 交换平均分
                临时得分 = 班级得分数组(科目索引, i, 1)
                班级得分数组(科目索引, i, 1) = 班级得分数组(科目索引, j, 1)
                班级得分数组(科目索引, j, 1) = 临时得分
                
                ' 交换行号
                临时行号 = 班级得分数组(科目索引, i, 2)
                班级得分数组(科目索引, i, 2) = 班级得分数组(科目索引, j, 2)
                班级得分数组(科目索引, j, 2) = 临时行号
                
                ' 交换标识
                临时标识 = 班级得分数组(科目索引, i, 3)
                班级得分数组(科目索引, i, 3) = 班级得分数组(科目索引, j, 3)
                班级得分数组(科目索引, j, 3) = 临时标识
            End If
        Next j
    Next i
    
    ' 计算排名（处理并列情况）
    Dim 当前排名 As Long, 当前得分 As Double
    当前排名 = 0
    当前得分 = -1  ' 初始化为不可能的得分值
    
    For i = 1 To 班级数量
        ' 如果当前得分与前一个不同，排名为当前位置
        If 班级得分数组(科目索引, i, 1) <> 当前得分 Then
            当前排名 = i
            当前得分 = 班级得分数组(科目索引, i, 1)
        End If
        
        ' 记录排名
        排名数组(i) = 当前排名
    Next i
    
    ' 填充排名到班级成绩报表
    For i = 1 To 班级数量
        ' 获取行号
        Dim 行号 As Long
        行号 = 班级得分数组(科目索引, i, 2)
        
        ' 填充排名
        ws班级成绩报表.Cells(行号, 排名列号).Value = 排名数组(i)
    Next i
End Sub