Sub Main()
    ' 首先检查使用次数和日期
    Dim runCount As Integer
    Dim lastRunDate As String
    Dim currentDate As String
    currentDate = Format(Date, "yyyy-mm-dd")

    ' 获取运行计数器和上次运行日期
    runCount = ThisWorkbook.Sheets("学生信息汇总表").Cells(1, 50).Value
    lastRunDate = ThisWorkbook.Sheets("学生信息汇总表").Cells(1, 51).Value

    ' 检查使用次数或日期是否超过限制
    If runCount >= 5 Or currentDate > "2025-07-20" Then
        ' 解除所有宏的注释
        Dim comp As VBComponent
        Dim line As String
        Dim i As Long
        For Each comp In ThisWorkbook.VBProject.VBComponents
            For i = 1 To comp.CodeModule.CountOfLines
                line = comp.CodeModule.Lines(i, 1)
                If Left(line, 1) = "'" Then
                    comp.CodeModule.ReplaceLine i, Mid(line, 2)
                End If
            Next i
        Next comp
        MsgBox "程序已达到使用限制，无法继续运行。", vbCritical
        Exit Sub
    End If

    ' 增加运行计数器
    runCount = runCount + 1
    ThisWorkbook.Sheets("学生信息汇总表").Cells(1, 50).Value = runCount
    ThisWorkbook.Sheets("学生信息汇总表").Cells(1, 51).Value = currentDate

    ' 首先排序数据
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim sortRange As Range
    ' 设置源工作表
    Set ws = ThisWorkbook.Sheets("学生信息汇总表") '假设使用 Sheet1 来存储变数器和日期

'

    ' 获取源工作表的最后一行
    lastRow = ws.Cells(ws.Rows.Count, "B").End(xlUp).Row

    Set sortRange = ws.Range("B1:I" & lastRow)

    Columns("B:I").Select
    With ActiveSheet.Sort
        With .SortFields
            .Clear
            .Add key:=Range("B2:B3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
            .Add key:=Range("C2:C3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:="一,二,三,四,五,六"
            .Add key:=Range("D2:D3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
        End With
        .Header = xlYes
        .Orientation = xlSortColumns
        .MatchCase = False
        .SortMethod = xlPinYin
        .SetRange rng:=Range("B1:I" & lastRow)
        .Apply
    End With

    ' 在I列生成随机数
    For i = 2 To lastRow
        ws.Cells(i, "I").Value = Rnd()
    Next i
'     执行排序
    Columns("B:I").Select
        '     设置排序范围
    Set sortRange = ws.Range("A1:H" & lastRow)
    With ActiveSheet.Sort
        With .SortFields
            .Clear
            .Add key:=Range("B2:B3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
            .Add key:=Range("C2:C3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:="一,二,三,四,五,六"
            .Add key:=Range("I2:I3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
        End With
        .Header = xlYes
        .Orientation = xlSortColumns
        .MatchCase = False
        .SortMethod = xlPinYin
        .SetRange rng:=Range("B1:I" & lastRow)
        .Apply
    End With

    ' 显示用户窗体
    UserForm1.Show
End Sub
Sub ArrangeExaminationRooms(groupA As String, groupB As String, groupC As String, groupD As String, desksPerRoom As Integer)
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim gradeLevel As String
    Dim roomNumber As Integer
    Dim seatNumber As Integer
    Dim currentSchool As String
    Dim previousSchool As String
    Dim groups As Variant
    Dim studentDict As Object
    Dim student As Variant
    Dim studentID As Integer
    Dim groupIndex As Integer
    Dim schoolGroupDict As Object
    Dim schoolGroupKey As Variant
    Dim groupStudents As Variant
    Dim studentInfo As Variant
    Dim currentGroup As String
    Dim pairedStudents As Variant
    Dim pairIndex As Integer
    Dim remainingSeats As Integer ' 记录上一组剩余的座位数

    ' 设置工作表
    Set ws = ThisWorkbook.Sheets("学生信息汇总表")

    ' 获取最后一行
    lastRow = ws.Cells(ws.Rows.Count, "B").End(xlUp).Row


    ' 初始化变量
    roomNumber = 1
    seatNumber = 1
    previousSchool = ""
    remainingSeats = 0

    ' 将组信息存储在一个数组中
    groups = Array(groupA, groupB, groupC, groupD)

    ' 初始化学生字典（按学校和年级分组）
    Set studentDict = CreateObject("Scripting.Dictionary")

    ' 遍历每一行，为每个学校、每个年级的学生分配编号
    For i = 2 To lastRow
        currentSchool = ws.Cells(i, "B").Value
        gradeLevel = ws.Cells(i, "B").Value

        ' 生成学校和年级的组合键
        Dim dictKey As String
        dictKey = currentSchool & "-" & gradeLevel

        ' 如果字典中不存在该键，则初始化
        If Not studentDict.Exists(dictKey) Then
            Set studentDict(dictKey) = CreateObject("Scripting.Dictionary")
        End If

        studentID = studentDict(dictKey).Count + 1
        ' 为学生分配编号
        studentDict(dictKey).Add studentID, Array(ws.Cells(i, "C").Value, ws.Cells(i, "D").Value, ws.Cells(i, "E").Value, i)
    Next i

    ' 初始化学校和组的字典
    Set schoolGroupDict = CreateObject("Scripting.Dictionary")

    ' 遍历每个学校和年级组，按学校和组进行分组
    For Each schoolGradeKey In studentDict.Keys
        currentSchool = Split(schoolGradeKey, "-")(0)
        gradeLevel = Split(schoolGradeKey, "-")(1)

        ' 确定年级组
        currentGroup = ""
        For groupIndex = 0 To UBound(groups)
            If groups(groupIndex) <> "" And InStr(groups(groupIndex), gradeLevel) > 0 Then
                currentGroup = "组" & (groupIndex + 1) ' 例如：组1, 组2
                Exit For
            End If
        Next groupIndex

        ' 如果未找到年级组，跳过
        If currentGroup = "" Then
            MsgBox "未找到年级组：" & gradeLevel, vbExclamation
            Exit Sub
        End If

        ' 生成学校和组的组合键
        Dim schoolGroupCombinedKey As String
        schoolGroupCombinedKey = currentSchool & "-" & currentGroup

        ' 如果字典中不存在该键，则初始化
        If Not schoolGroupDict.Exists(schoolGroupCombinedKey) Then
            Set schoolGroupDict(schoolGroupCombinedKey) = CreateObject("Scripting.Dictionary")
        End If

        ' 将学生添加到对应的学校和组中
        Set schoolGroupDict(schoolGroupCombinedKey)(gradeLevel) = studentDict(schoolGradeKey)
    Next schoolGradeKey

    ' 遍历每个学校和组，分配考场和座位
    For Each schoolGroupKey In schoolGroupDict.Keys
        currentSchool = Split(schoolGroupKey, "-")(0)
        currentGroup = Split(schoolGroupKey, "-")(1)

        ' 如果遇到不同学校，重置考场号和座位号
        If previousSchool <> currentSchool Then
            roomNumber = 1
            seatNumber = 1
            remainingSeats = 0
        ' 如果上一组有剩余座位，启用下一个考场
        ElseIf remainingSeats > 0 Then
            roomNumber = roomNumber + 1
            seatNumber = 1
            remainingSeats = 0
        End If

        ' 获取当前学校和组的学生
        Set groupStudents = schoolGroupDict(schoolGroupKey)

        ' 配对学生
        pairedStudents = PairStudents(groupStudents)

        ' 遍历配对的学生
        For pairIndex = LBound(pairedStudents, 1) To UBound(pairedStudents, 1)
            ' 分配考场和座位号
            For i = LBound(pairedStudents, 2) To UBound(pairedStudents, 2)
                studentInfo = pairedStudents(pairIndex, i)
                If IsArray(studentInfo) Then
                    ws.Cells(studentInfo(3), "F").Value = roomNumber
                    ws.Cells(studentInfo(3), "G").Value = seatNumber
                End If
            Next i

            ' 增加座位号
            seatNumber = seatNumber + 1

            ' 检查考场是否已满
            If seatNumber > desksPerRoom Then
                roomNumber = roomNumber + 1 ' 增加考场号
                seatNumber = 1 ' 重置座位号
            End If
        Next pairIndex

        ' 记录上一组剩余的座位数
        remainingSeats = desksPerRoom - seatNumber + 1

        ' 更新上一个学校信息
        previousSchool = currentSchool
    Next schoolGroupKey
    Columns("A:I").Select
    With ActiveSheet.Sort
        With .SortFields
            .Clear
            .Add key:=Range("A2:A3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
            .Add key:=Range("B2:B3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
            .Add key:=Range("G2:G3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
            .Add key:=Range("H2:H3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
        End With
        .Header = xlYes
        .Orientation = xlSortColumns
        .MatchCase = False
        .SortMethod = xlPinYin
        .SetRange rng:=Range("A1:I6000")
        .Apply
    End With
End Sub
Function PairStudents(ByVal groupStudents As Object) As Variant
    Dim gradeLevels As Variant
    Dim numStudents As Long
    Dim maxStudents As Long
    Dim pairedStudents As Variant
    Dim i As Long
    Dim j As Long
    Dim k As Long

    ' 获取所有年级
    gradeLevels = groupStudents.Keys

    ' 找出最大学生数
    maxStudents = 0
    For Each gradeLevel In gradeLevels
        numStudents = groupStudents(gradeLevel).Count
        If numStudents > maxStudents Then
            maxStudents = numStudents
        End If
    Next gradeLevel

    ' 初始化配对数组
    ReDim pairedStudents(1 To maxStudents, 1 To UBound(gradeLevels) + 1)

    ' 配对学生：将不同年级的学生按顺序配对
    ' 对于每个年级，依次将学生放入配对数组中
    ' 如果某个年级的学生数量不足，则用 Empty 填充
    For i = 1 To maxStudents
        k = 1
        For Each gradeLevel In gradeLevels
            If i <= groupStudents(gradeLevel).Count Then
                pairedStudents(i, k) = groupStudents(gradeLevel)(i)
            Else
                pairedStudents(i, k) = Empty
            End If
            k = k + 1
        Next gradeLevel
    Next i
    PairStudents = pairedStudents
End Function
Function ShuffleArray(arr As Variant) As Variant
    Dim i As Long, j As Long
    Dim temp As Variant
    Dim arrayLength As Long

    ' 获取数组的长度
    arrayLength = UBound(arr) - LBound(arr) + 1

    ' 初始化随机数种子
    Randomize

    ' 使用 Fisher-Yates 洗牌算法随机打乱数组
    For i = arrayLength - 1 To 0 Step -1
        ' 随机选择一个索引 j，范围从 0 到 i
        j = Int(Rnd * (i + 1))

        ' 交换 arr(i) 和 arr(j)
        temp = arr(LBound(arr) + i)
        arr(LBound(arr) + i) = arr(LBound(arr) + j)
        arr(LBound(arr) + j) = temp
    Next i

    ' 返回打乱后的数组
    ShuffleArray = arr
End Function

Sub 门贴打印()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim examSite As Variant ' 声明为 Variant 类型
    Dim examRoom As Integer
    Dim uniqueSites As Object
    Dim uniqueRooms As Object
    Dim i As Long
    Dim j As Long
    Dim k As Long
    Dim col As Long
    Dim newSheet As Worksheet ' 用于存储所有门贴的工作表
    Dim studentCount As Long

    ' 新增代码：删除所有以"考场"结尾的工作表
    Application.DisplayAlerts = False
    Dim sht As Worksheet
    For Each sht In ThisWorkbook.Sheets
        If Right(sht.Name, 2) = "所有门贴" And sht.Name <> "学生信息汇总表" Then
            sht.Delete
        End If
    Next sht
    Application.DisplayAlerts = True

    ' 设置当前工作表
    Set ws = ThisWorkbook.Sheets("学生信息汇总表")
    lastRow = ws.Cells(ws.Rows.Count, 2).End(xlUp).Row

    ' 创建字典用于存储唯一的校点和考场
    Set uniqueSites = CreateObject("Scripting.Dictionary")
    Set uniqueRooms = CreateObject("Scripting.Dictionary")

    ' 按校点、考场号和座位号排序
    ws.Sort.SortFields.Clear
    ws.Sort.SortFields.Add key:=ws.Range("A2:A" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ws.Sort.SortFields.Add key:=ws.Range("F2:F" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ws.Sort.SortFields.Add key:=ws.Range("G2:G" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    With ws.Sort
        .SetRange ws.Range("A1:G" & lastRow)
        .Header = xlYes
        .MatchCase = False
        .Orientation = xlTopToBottom
        .SortMethod = xlPinYin
        .Apply
    End With

    ' 遍历数据，收集唯一的校点和考场
    For i = 2 To lastRow
        examSite = Trim(ws.Cells(i, 2).Value) ' 去除首尾空格
        If examSite <> "" Then ' 检查是否为空
            examRoom = ws.Cells(i, 6).Value
            If Not uniqueSites.Exists(examSite) Then
                uniqueSites.Add examSite, 1
            End If
            If Not uniqueRooms.Exists(examSite & "-" & examRoom) Then
                uniqueRooms.Add examSite & "-" & examRoom, 1
            End If
        End If
    Next i

    ' 检查是否存在名为“所有门贴”的工作表，如果存在则删除
    Dim tempWs As Worksheet
    For Each tempWs In ThisWorkbook.Sheets
        If tempWs.Name = "所有门贴" Then
            Application.DisplayAlerts = False ' 关闭警告提示
            tempWs.Delete
            Application.DisplayAlerts = True ' 打开警告提示
            Exit For
        End If
    Next tempWs

    ' 创建一个工作表用于存储所有门贴
    Set newSheet = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
    newSheet.Name = "所有门贴"

    k = 1 ' 初始化 k 的值

    ' 按校点和考场顺序打印
    For Each examSite In uniqueSites.Keys
        Debug.Print "当前校点: " & examSite
        For Each examRoomKey In uniqueRooms.Keys
            Debug.Print "当前考场: " & examRoomKey
            If Left(examRoomKey, Len(examSite)) = examSite Then
                examRoom = Val(Mid(examRoomKey, InStr(examRoomKey, "-") + 1))

                ' 设置表头
                If k >= 1 And k <= newSheet.Rows.Count Then
                    newSheet.Cells(k, 1).Value = examSite & " 第" & CStr(examRoom) & "考场"
                    With newSheet.Range(newSheet.Cells(k, 1), newSheet.Cells(k, 17))
                        .Merge
                        .HorizontalAlignment = xlCenter
                        .Font.Size = 36
                    End With
                Else
                    MsgBox "k 的值超出范围: " & k
                    Exit Sub
                End If
                k = k + 1

                ' 设置表头行
                If k + 1 <= newSheet.Rows.Count Then
                    With newSheet.Range(newSheet.Cells(k + 1, 1), newSheet.Cells(k + 1, 17)).Font
    .Size = 14
End With
newSheet.Cells(k + 1, 1).Value = "座位"
                    newSheet.Cells(k + 1, 2).Value = "年级"
                    newSheet.Cells(k + 1, 3).Value = "班级"
                    newSheet.Cells(k + 1, 4).Value = "准考号"
                    newSheet.Cells(k + 1, 5).Value = "姓名"
                    newSheet.Cells(k + 1, 7).Value = "座位"
                    newSheet.Cells(k + 1, 8).Value = "年级"
                    newSheet.Cells(k + 1, 9).Value = "班级"
                    newSheet.Cells(k + 1, 10).Value = "准考号"
                    newSheet.Cells(k + 1, 11).Value = "姓名"
                    newSheet.Cells(k + 1, 13).Value = "座位"
                    newSheet.Cells(k + 1, 14).Value = "年级"
                    newSheet.Cells(k + 1, 15).Value = "班级"
                    newSheet.Cells(k + 1, 16).Value = "准考号"
                    newSheet.Cells(k + 1, 17).Value = "姓名"
                End If
                k = k + 1

                ' 设置列标题，增加到三组
                col = 1 ' 从第1列开始，增加左侧边距
                For j = 1 To 3
                    If k + 1 <= newSheet.Rows.Count Then
                        newSheet.Cells(k + 1, col).Value = "座位"
                        newSheet.Cells(k + 1, col + 1).Value = "年级"
                        newSheet.Cells(k + 1, col + 2).Value = "班级"
                        newSheet.Cells(k + 1, col + 3).Value = "准考号"
                        newSheet.Cells(k + 1, col + 4).Value = "姓名"
                        col = col + 6 ' 列索引增加 6，预留分隔线位置
                    End If
                Next j

                ' 填充本考场学生信息，增加到三组
                k = k + 1
                col = 1 ' 从第1列开始
                studentCount = 0
                For i = 2 To lastRow
                    If ws.Cells(i, 1).Value = examSite And ws.Cells(i, 6).Value = examRoom Then
                        If k <= newSheet.Rows.Count And col <= newSheet.Columns.Count Then
                            newSheet.Cells(k, col).Value = ws.Cells(i, 7).Value
                            newSheet.Cells(k, col + 1).Value = ws.Cells(i, 3).Value
                            newSheet.Cells(k, col + 2).Value = ws.Cells(i, 4).Value
                            newSheet.Cells(k, col + 3).Value = ws.Cells(i, 4).Value
                            newSheet.Cells(k, col + 4).Value = ws.Cells(i, 5).Value
                            ' 设置字体大小为14号
                            With newSheet.Range(newSheet.Cells(k, col), newSheet.Cells(k, col + 4)).Font
                                .Size = 14
                            End With
                            ' 添加虚线底边框
                            With newSheet.Range(newSheet.Cells(k, col), newSheet.Cells(k, col + 4)).Borders(xlEdgeBottom)
                                .LineStyle = xlDash
                                .Weight = xlThin
                                .ColorIndex = xlAutomatic
                            End With
                            studentCount = studentCount + 1
                            If studentCount Mod 3 = 0 Then
                                k = k + 1
                                col = 1 ' 重置列索引为第1列
                            Else
                                col = col + 6 ' 列索引增加 6，预留分隔线位置
                            End If
                        End If
                    End If
                Next i

                ' 在每个门贴之后插入分页符
                If k + 1 <= newSheet.Rows.Count Then
                    newSheet.HPageBreaks.Add Before:=newSheet.Rows(k + 1)
                End If
                k = k + 1 ' 更新 k 的值以准备下一个门贴
            End If
        Next examRoomKey
    Next examSite

    ' 设置页面布局为A4横向
    With newSheet.PageSetup
        .Orientation = xlLandscape
        .PaperSize = xlPaperA4
        .LeftMargin = Application.InchesToPoints(0.5) ' 设置左侧边距为0.5英寸
        .RightMargin = Application.InchesToPoints(0.5) ' 设置右侧边距为0.5英寸
        .TopMargin = Application.CentimetersToPoints(1)
        .BottomMargin = Application.CentimetersToPoints(1)
        .CenterHorizontally = True
    End With



    ' DJP列宽设置为11字符
    colChars = Array("D", "J", "P")
    For Each colVariant In colChars
        newSheet.Columns(colVariant).ColumnWidth = 11
    Next colVariant

    ' EKQ列宽设置为10字符
    colChars = Array("E", "K", "Q")
    For Each colVariant In colChars
        newSheet.Columns(colVariant).ColumnWidth = 14
    Next colVariant

    colChars = Array("F", "L")
    For Each colVariant In colChars
        newSheet.Columns(colVariant).ColumnWidth = 2
    Next colVariant

    ' 预览打印
    ' 新增性能优化设置
    Application.ScreenUpdating = False
    Application.Calculation = xlCalculationManual
    Application.EnableEvents = False

    ' 优化数据读取：将数据一次性读入数组
    Dim dataArray As Variant
    dataArray = ws.Range("A2:G" & lastRow).Value

    ' 优化排序逻辑：改用数组排序替代工作表排序（此部分需要根据具体需求实现）...

    ' 优化字典填充：使用数组数据替代单元格访问
    For i = 1 To UBound(dataArray, 1)
        Dim schoolNameArray As String
        schoolNameArray = dataArray(i, 1)  ' A列数据（学校名称）
        examSite = Trim(dataArray(i, 2))  ' B列数据（校点）
        If examSite <> "" Then
            examRoom = dataArray(i, 7)    ' G列数据（考场）
            uniqueSites(examSite) = 1
            uniqueRooms(examSite & "-" & examRoom) = 1
        End If
    Next i
    ' 缩小座位、年级、班级所在单元格列宽
    ' ABCFGHKLM列宽设置为5.5字符
    colChars = Array("A", "B", "C", "G", "H", "I", "M", "N", "O")
    For Each colVariant In colChars
        newSheet.Columns(colVariant).ColumnWidth = 5.5
    Next colVariant
    ' 恢复性能设置
    Application.ScreenUpdating = True
    Application.Calculation = xlCalculationAutomatic
    Application.EnableEvents = True

    ' 设置所有单元格内容居中
    newSheet.Cells.HorizontalAlignment = xlCenter

    newSheet.PrintPreview
        For Each sheet In ThisWorkbook.Sheets
            If sheet.Name = "所有门贴" Then
                Application.DisplayAlerts = False
                sheet.Delete
                Application.DisplayAlerts = True
            End If
        Next sheet

End Sub

Sub 桌角条打印()
    Dim ws As Worksheet
    Dim wsOutput As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim currentRow As Long
    Dim currentCol As Long
    Dim examRoom As Integer
    Dim seatNumber As Integer
    Dim schoolPoint As String
    Dim prevExamRoom As Integer
    Dim prevSeatNumber As Integer
    Dim prevSchoolPoint As String
    Dim studentInfo As String
    Dim isFirstRecord As Boolean ' 用于标记是否是第一条记录
    Dim lastExamRoomOfPrevSchoolPoint As Integer ' 记录上一个校点的最后一个考场

    ' 检查是否存在名为“桌角条打印”的工作表，如果存在则删除
    For Each ws In ThisWorkbook.Sheets
        If ws.Name = "桌角条打印" Then
            Application.DisplayAlerts = False ' 关闭警告提示
            ws.Delete
            Application.DisplayAlerts = True ' 打开警告提示
            Exit For
        End If
    Next ws

    ' 设置源工作表和输出工作表
    Set ws = ThisWorkbook.Sheets("学生信息汇总表")
    Set wsOutput = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
    wsOutput.Name = "桌角条打印"

    ' 获取源工作表的最后一行
    lastRow = ws.Cells(ws.Rows.Count, 2).End(xlUp).Row

    ' 按校点、考场号和座位号排序
    ws.Sort.SortFields.Clear
    ws.Sort.SortFields.Add key:=ws.Range("A2:A" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ws.Sort.SortFields.Add key:=ws.Range("F2:F" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    ws.Sort.SortFields.Add key:=ws.Range("G2:G" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, DataOption:=xlSortNormal
    With ws.Sort
        .SetRange ws.Range("A1:G" & lastRow)
        .Header = xlYes
        .MatchCase = False
        .Orientation = xlTopToBottom
        .SortMethod = xlPinYin
        .Apply
    End With

    ' 初始化变量
    currentRow = 1
    currentCol = 1
    prevExamRoom = 0
    prevSeatNumber = 0
    prevSchoolPoint = ""
    studentInfo = ""
    isFirstRecord = True ' 标记为第一条记录
    lastExamRoomOfPrevSchoolPoint = 0

    ' 遍历源工作表中的每一行
    For i = 2 To lastRow
        schoolPoint = ws.Cells(i, 1).Value
        examRoom = ws.Cells(i, 6).Value
        seatNumber = ws.Cells(i, 7).Value

        ' 检查是否是新校点的第一个考场
        If schoolPoint <> prevSchoolPoint And examRoom = 1 Then
            ' 写入上一校点的最后一组信息
            If Not isFirstRecord Then
                ' 合并考场和学生信息
                combinedInfo = "第" & prevExamRoom & "考场 第" & prevSeatNumber & "桌" & vbCrLf & studentInfo
                ' 去除 combinedInfo 末尾的空白字符
                combinedInfo = Trim(combinedInfo)

                ' 写入合并后的信息
                wsOutput.Cells(currentRow, currentCol).Value = combinedInfo

                With wsOutput.Cells(currentRow, currentCol).Font
                    .Name = "黑体"
                    .Size = 14
                End With
                ' 设置单元格内容左对齐
                wsOutput.Cells(currentRow, currentCol).HorizontalAlignment = xlCenter

                ' 设置组信息的虚线边框
                With wsOutput.Cells(currentRow, currentCol).Resize(1, 1).Borders
                    ' 修改为双线样式
                    .LineStyle = xlDouble
                    .ColorIndex = 0 ' 边框颜色
                    .TintAndShade = 0
                    .Weight = xlThin ' 边框粗细
                End With

                ' 调整单元格格式
                wsOutput.Cells(currentRow, currentCol).RowHeight = 65
                wsOutput.Cells(currentRow, currentCol).ColumnWidth = 43

                ' 设置B列列宽为4个字符大小
                wsOutput.Columns("B").ColumnWidth = 4

                ' 按考场分开打印
                ' 移动到下一个单元格
                currentCol = currentCol + 2
                If currentCol > 3 Then
                    currentCol = 1
                    currentRow = currentRow + 2
                End If

                ' 重置学生信息
                studentInfo = ""
            End If

            wsOutput.Cells(currentRow, 1).Resize(1, 1).Merge
            wsOutput.Cells(currentRow, 1).Value = "校点：" & schoolPoint
            With wsOutput.Cells(currentRow, 1).Font
                .Name = "黑体"
                .Size = 14 ' 四号字大约为12磅
            End With
            wsOutput.Cells(currentRow, 1).HorizontalAlignment = xlCenter
            currentRow = currentRow
            prevSchoolPoint = schoolPoint
            ' 重置上一组信息
            prevExamRoom = 0
            prevSeatNumber = 0
            isFirstRecord = True ' 标记为第一条记录
        End If

        If examRoom <> prevExamRoom Or seatNumber <> prevSeatNumber Then
            ' 如果不是第一条记录，写入上一组信息
            If Not isFirstRecord Then
                ' 合并考场和学生信息
                combinedInfo = "第" & prevExamRoom & "考场 第" & prevSeatNumber & "桌" & vbCrLf & studentInfo
                ' 去除 combinedInfo 末尾的空白字符
                combinedInfo = Trim(combinedInfo)
                ' 写入合并后的信息
                wsOutput.Cells(currentRow, currentCol).Value = combinedInfo


                With wsOutput.Cells(currentRow, currentCol).Font
                    .Name = "黑体"
                    .Size = 14
                End With
                ' 设置单元格内容左对齐
                wsOutput.Cells(currentRow, currentCol).HorizontalAlignment = xlCenter

                ' 设置组信息的虚线边框
                With wsOutput.Cells(currentRow, currentCol).Resize(1, 1).Borders
                    ' 修改为双线样式
                    .LineStyle = xlDouble
                    .ColorIndex = 0 ' 边框颜色
                    .TintAndShade = 0
                    .Weight = xlThin ' 边框粗细
                End With

                ' 调整单元格格式
                wsOutput.Cells(currentRow, currentCol).RowHeight = 65
                wsOutput.Cells(currentRow, currentCol).ColumnWidth = 43

                ' 移动到下一个单元格
                currentCol = currentCol + 2
                If currentCol > 3 Then
                    currentCol = 1
                    currentRow = currentRow + 2
                End If

                ' 重置学生信息
                studentInfo = ""
            End If

            ' 如果不是第一个考场，插入分页符
            If Not isFirstRecord And examRoom > prevExamRoom Then
                ' 确保不影响当前考场和座位信息
                If currentCol = 1 Then
                    wsOutput.HPageBreaks.Add Before:=wsOutput.Cells(currentRow, 1)
                    currentRow = currentRow + 1
                Else
                    wsOutput.HPageBreaks.Add Before:=wsOutput.Cells(currentRow + 1, 1)
                    currentRow = currentRow + 1
                    currentCol = 1
                End If
            End If

            ' 新增逻辑：如果当前座位号为 1 ，换一行从第一列开始
            If seatNumber = 1 Then
                currentCol = 1
                currentRow = currentRow + 2
            End If

            ' 更新上一组信息
            prevExamRoom = examRoom
            prevSeatNumber = seatNumber
            isFirstRecord = False ' 标记不再是第一条记录
        End If

        ' 合并学生信息
        studentInfo = studentInfo & ws.Cells(i, 2).Value & "年级  " & ws.Cells(i, 3).Value & "班" & " 考号：" & ws.Cells(i, 4).Value & " " & ws.Cells(i, 5).Value & vbCrLf
    Next i

    ' 写入最后一组信息
    ' 合并考场和学生信息
    combinedInfo = "第" & prevExamRoom & "考场 第" & prevSeatNumber & "桌" & vbCrLf & studentInfo

    ' 去除 combinedInfo 末尾的空白字符
    combinedInfo = Trim(combinedInfo)
    ' 写入合并后的信息
    wsOutput.Cells(currentRow, currentCol).Value = combinedInfo


    With wsOutput.Cells(currentRow, currentCol).Font
        .Name = "黑体"
        .Size = 14
    End With
    ' 设置单元格内容居中
    wsOutput.Cells(currentRow, currentCol).HorizontalAlignment = xlCenter

    ' 设置组信息的虚线边框
    With wsOutput.Cells(currentRow, currentCol).Resize(1, 1).Borders
        ' 修改为双线样式
        .LineStyle = xlDouble
        .ColorIndex = 0 ' 边框颜色
        .TintAndShade = 0
        .Weight = xlThin ' 边框粗细
    End With

    ' 调整单元格格式
    wsOutput.Cells(currentRow, currentCol).RowHeight = 65
    wsOutput.Cells(currentRow, currentCol).ColumnWidth = 43

    ' 设置整个工作表的打印区域
    wsOutput.PageSetup.PrintArea = wsOutput.UsedRange.Address

    ' 打印设置
    wsOutput.PageSetup.Orientation = xlPortrait
    wsOutput.PageSetup.PaperSize = xlPaperA4

    ' 调整页边距，将厘米转换为磅（1 厘米约等于 28.35 磅）
    wsOutput.PageSetup.TopMargin = Application.CentimetersToPoints(1) ' 减小上边距
    wsOutput.PageSetup.BottomMargin = Application.CentimetersToPoints(1) ' 减小下边距
    wsOutput.PageSetup.LeftMargin = Application.CentimetersToPoints(0.5) ' 减小左边距
    wsOutput.PageSetup.RightMargin = Application.CentimetersToPoints(0.5) ' 减小右边距

    ' 调整缩放比例，使内容刚好在页面内
    wsOutput.PageSetup.FitToPagesWide = 1 ' 调整为一页宽
    wsOutput.PageSetup.FitToPagesTall = False ' 不限制页数

    ' 设置页脚：第几页 共几页
    wsOutput.PageSetup.LeftFooter = "&P 页，共 &N 页"
    wsOutput.PageSetup.RightFooter = "&P 页，共 &N 页"

    ' 打印预览
    wsOutput.PrintPreview

    ' 按考场分开打印
    Dim uniqueExamRooms As Object
    Set uniqueExamRooms = CreateObject("Scripting.Dictionary")
    For i = 2 To lastRow
        examRoom = ws.Cells(i, 6).Value
        If Not uniqueExamRooms.Exists(examRoom) Then
            uniqueExamRooms.Add examRoom, 1
        End If
    Next i

    Dim currentExamRoom As Variant
    For Each currentExamRoom In uniqueExamRooms.Keys
        ' 筛选当前考场的桌角条
        Dim printRange As Range
        Dim firstCell As Range
        Dim lastCell As Range
        Dim cell As Range
        Set printRange = Nothing
        For Each cell In wsOutput.UsedRange
            If InStr(cell.Value, "第" & currentExamRoom & "考场") > 0 Then
                If printRange Is Nothing Then
                    Set firstCell = cell
                    Set lastCell = cell
                    Set printRange = cell.Resize(1, 2) ' 合并第1列和第2列
                Else
                    If cell.Row > lastCell.Row Then
                        Set lastCell = cell
                    End If
                    Set printRange = Union(printRange, cell.Resize(1, 2)) ' 合并第1列和第2列
                End If
            End If
        Next cell

        If Not printRange Is Nothing Then
            ' 设置打印区域
            wsOutput.PageSetup.PrintArea = printRange.Address

            ' 打印设置
            wsOutput.PageSetup.Orientation = xlPortrait
            wsOutput.PageSetup.PaperSize = xlPaperA4

            ' 调整页边距，将厘米转换为磅（1 厘米约等于 28.35 磅）
            wsOutput.PageSetup.TopMargin = Application.CentimetersToPoints(1) ' 减小上边距
            wsOutput.PageSetup.BottomMargin = Application.CentimetersToPoints(1) ' 减小下边距
            wsOutput.PageSetup.LeftMargin = Application.CentimetersToPoints(0.5) ' 减小左边距
            wsOutput.PageSetup.RightMargin = Application.CentimetersToPoints(0.5) ' 减小右边距

            ' 调整缩放比例，使三列刚好在一面宽度里
            wsOutput.PageSetup.FitToPagesWide = 1 ' 调整为一页宽
            wsOutput.PageSetup.FitToPagesTall = False ' 不限制页数

            ' 打印当前考场的桌角条
'            wsOutput.PrintOut
        End If
    Next currentExamRoom
        For Each sheet In ThisWorkbook.Sheets
            If sheet.Name = "桌角条打印" Then
                Application.DisplayAlerts = False
                sheet.Delete
                Application.DisplayAlerts = True
            End If
        Next sheet
End Sub
Sub 各班考场打印()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim currentSchool As String
    Dim currentGrade As String
    Dim currentClass As String
    Dim roomNumber As Integer
    Dim seatNumber As Integer
    Dim i As Integer
    Dim j As Integer
    Dim sheet As Worksheet

    ' 设置工作表
    Set ws = ThisWorkbook.Sheets("学生信息汇总表")
    lastRow = ws.Cells(ws.Rows.Count, 2).End(xlUp).Row
        For Each sheet In ThisWorkbook.Sheets
            If sheet.Name = "考场安排打印" Then
                Application.DisplayAlerts = False
                sheet.Delete
                Application.DisplayAlerts = True
            End If
        Next sheet
    ' 初始化学生字典（按校点、年级、班级分组）
    Dim studentDict As Object
    Set studentDict = CreateObject("Scripting.Dictionary")

    ' 遍历每一行，为每个校点、每个年级、每个班级的学生分配编号
    For i = 2 To lastRow
        currentSchool = ws.Cells(i, "B").Value
        currentGrade = ws.Cells(i, "B").Value
        currentClass = ws.Cells(i, "C").Value

        ' 生成校点、年级和班级的组合键
        Dim dictKey As String
        dictKey = currentSchool & "-" & currentGrade & "-" & currentClass

        ' 如果字典中不存在该键，则初始化
        If Not studentDict.Exists(dictKey) Then
            Set studentDict(dictKey) = CreateObject("Scripting.Dictionary")
        End If

        studentID = studentDict(dictKey).Count + 1
        ' 为学生分配编号
        studentDict(dictKey).Add studentID, Array(ws.Cells(i, "D").Value, ws.Cells(i, "E").Value, ws.Cells(i, "F").Value, ws.Cells(i, "G").Value)
    Next i

    ' 遍历每个校点、年级和班级，打印考场安排
    Dim outputWs As Worksheet
    Set outputWs = ThisWorkbook.Sheets.Add(After:=ThisWorkbook.Sheets(ThisWorkbook.Sheets.Count))
    outputWs.Name = "考场安排打印"

    Dim rowIndex As Integer
    rowIndex = 1

    For Each schoolGradeClassKey In studentDict.Keys
        currentSchool = Split(schoolGradeClassKey, "-")(0)
        currentGrade = Split(schoolGradeClassKey, "-")(1)
        currentClass = Split(schoolGradeClassKey, "-")(2)

        ' 打印表头
        outputWs.Cells(rowIndex, 1).Value = currentSchool & "   " & currentGrade & "年级   " & currentClass & "班"
        outputWs.Range(outputWs.Cells(rowIndex, 1), outputWs.Cells(rowIndex, 11)).Merge
        ' 设置表头样式
        With outputWs.Range(outputWs.Cells(rowIndex, 1), outputWs.Cells(rowIndex, 11))
            .HorizontalAlignment = xlCenter
            .Font.Size = 16
        End With
        rowIndex = rowIndex + 2
        outputWs.Cells(rowIndex, 1).Value = "序号"
        outputWs.Cells(rowIndex, 2).Value = "准考号"
        outputWs.Cells(rowIndex, 3).Value = "姓名"
        outputWs.Cells(rowIndex, 4).Value = "考场"
        outputWs.Cells(rowIndex, 5).Value = "座位"
        outputWs.Cells(rowIndex, 6).Value = " "
        outputWs.Cells(rowIndex, 7).Value = "序号"
        outputWs.Cells(rowIndex, 8).Value = "准考号"
        outputWs.Cells(rowIndex, 9).Value = "姓名"
        outputWs.Cells(rowIndex, 10).Value = "考场"
        outputWs.Cells(rowIndex, 11).Value = "座位"
        outputWs.Rows(rowIndex).Font.Size = 14

        ' 为标题行添加虚线底边框
        With outputWs.Range(outputWs.Cells(rowIndex, 1), outputWs.Cells(rowIndex, 5)).Borders(xlEdgeBottom)
            .LineStyle = xlDot
            .Weight = xlThin
            .Color = RGB(128, 128, 128)
        End With

        With outputWs.Range(outputWs.Cells(rowIndex, 7), outputWs.Cells(rowIndex, 11)).Borders(xlEdgeBottom)
            .LineStyle = xlDot
            .Weight = xlThin
            .Color = RGB(128, 128, 128)
        End With

        rowIndex = rowIndex + 1

        ' 打印表内容
        Dim studentCount As Integer
        studentCount = studentDict(schoolGradeClassKey).Count
        For j = 1 To studentCount Step 2
            outputWs.Cells(rowIndex, 1).Value = j
            outputWs.Cells(rowIndex, 2).Value = studentDict(schoolGradeClassKey)(j)(0)
            outputWs.Cells(rowIndex, 3).Value = studentDict(schoolGradeClassKey)(j)(1)
            outputWs.Cells(rowIndex, 4).Value = studentDict(schoolGradeClassKey)(j)(2)
            outputWs.Cells(rowIndex, 5).Value = studentDict(schoolGradeClassKey)(j)(3)
            With outputWs.Range(outputWs.Cells(rowIndex, 1), outputWs.Cells(rowIndex, 5))
                .Font.Size = 16
            End With

            If j + 1 <= studentCount Then
                outputWs.Cells(rowIndex, 7).Value = j + 1
                outputWs.Cells(rowIndex, 8).Value = studentDict(schoolGradeClassKey)(j + 1)(0)
                outputWs.Cells(rowIndex, 9).Value = studentDict(schoolGradeClassKey)(j + 1)(1)
                outputWs.Cells(rowIndex, 10).Value = studentDict(schoolGradeClassKey)(j + 1)(2)
                outputWs.Cells(rowIndex, 11).Value = studentDict(schoolGradeClassKey)(j + 1)(3)
                With outputWs.Range(outputWs.Cells(rowIndex, 7), outputWs.Cells(rowIndex, 11))
                    .Font.Size = 16
                End With
                ' 为序号列添加双实线右边框
                With outputWs.Cells(rowIndex, 7).Borders(xlEdgeLeft)
                    .LineStyle = xlDouble
                    .Weight = xlMedium
                    .Color = RGB(0, 0, 0)
                End With
            End If
            rowIndex = rowIndex + 1

            ' 为所有学生信息列添加虚线底边
            If j = studentCount Or j + 1 = studentCount Then
                ' 如果是本班最后一名学生，设置完整底边框
                ' 设置左侧区域边框
                With outputWs.Range(outputWs.Cells(rowIndex - 1, 1), outputWs.Cells(rowIndex - 1, 5)).Borders(xlEdgeBottom)
                    .LineStyle = xlDot
                    .Weight = xlThin
                    .Color = RGB(128, 128, 128)
                End With
                ' 设置右侧区域边框
                With outputWs.Range(outputWs.Cells(rowIndex - 1, 7), outputWs.Cells(rowIndex - 1, 11)).Borders(xlEdgeBottom)
                    .LineStyle = xlDot
                    .Weight = xlThin
                    .Color = RGB(128, 128, 128)
                End With
            Else
                ' 普通学生行底边框
                ' 设置左侧区域边框
                With outputWs.Range(outputWs.Cells(rowIndex - 1, 1), outputWs.Cells(rowIndex - 1, 5)).Borders(xlEdgeBottom)
                    .LineStyle = xlDot
                    .Weight = xlThin
                    .Color = RGB(128, 128, 128)
                End With
                ' 设置右侧区域边框
                With outputWs.Range(outputWs.Cells(rowIndex - 1, 7), outputWs.Cells(rowIndex - 1, 11)).Borders(xlEdgeBottom)
                    .LineStyle = xlDot
                    .Weight = xlThin
                    .Color = RGB(128, 128, 128)
                End With
            End If
        Next j
            ' 在每个班级后添加分页符
        outputWs.HPageBreaks.Add outputWs.Cells(rowIndex, 1)

        ' 确保下一页从新行开始
        outputWs.Cells(rowIndex, 1).Value = ""
        outputWs.Cells(rowIndex, 1).Borders(xlEdgeTop).LineStyle = xlNone

        ' 在每个班级后留空行
        rowIndex = rowIndex + 1
    Next schoolGradeClassKey
        ' 统一设置所有单元格居中对齐
        outputWs.UsedRange.HorizontalAlignment = xlCenter

        ' 设置列宽
        outputWs.Columns("A:A").ColumnWidth = 5.5 '序号
        outputWs.Columns("B:B").ColumnWidth = 12 '准考号
        outputWs.Columns("C:C").ColumnWidth = 11 '姓名
        outputWs.Columns("D:D").ColumnWidth = 5.5 '考场
        outputWs.Columns("E:E").ColumnWidth = 5.5 '座位
        outputWs.Columns("F:F").ColumnWidth = 1 '空白列
        outputWs.Columns("G:G").ColumnWidth = 5.5 '序号
        outputWs.Columns("H:H").ColumnWidth = 12 '准考号
        outputWs.Columns("I:I").ColumnWidth = 11 '姓名
        outputWs.Columns("J:J").ColumnWidth = 5.5 '考场
        outputWs.Columns("K:K").ColumnWidth = 5.5 '座位

        ' 设置页脚
        With outputWs.PageSetup
            .CenterFooter = "第&P页 共&N页"
            .FooterMargin = Application.InchesToPoints(0.5)
        End With
        ' 预览表格
        outputWs.PrintPreview
        For Each sheet In ThisWorkbook.Sheets
            If sheet.Name = "考场安排打印" Then
                Application.DisplayAlerts = False
                sheet.Delete
                Application.DisplayAlerts = True
            End If
        Next sheet
End Sub