Sub Main()
    ' 首先检查使用次数和日期
    Dim runCount As Integer
    Dim lastRunDate As String
    Dim currentDate As String
    currentDate = Format(Date, "yyyy-mm-dd")

    ' 获取运行计数器和上次运行日期
    runCount = ThisWorkbook.Sheets("学生信息汇总表").Cells(1, 50).Value
    lastRunDate = ThisWorkbook.Sheets("学生信息汇总表").Cells(1, 51).Value

    ' 检查使用次数或日期是否超过限制
    If runCount >= 5 Or currentDate > "2025-07-20" Then
        ' 解除所有宏的注释
        Dim comp As VBComponent
        Dim line As String
        Dim i As Long
        For Each comp In ThisWorkbook.VBProject.VBComponents
            For i = 1 To comp.CodeModule.CountOfLines
                line = comp.CodeModule.Lines(i, 1)
                If Left(line, 1) = "'" Then
                    comp.CodeModule.ReplaceLine i, Mid(line, 2)
                End If
            Next i
        Next comp
        MsgBox "程序已达到使用限制，无法继续运行。", vbCritical
        Exit Sub
    End If

    ' 增加运行计数器
    runCount = runCount + 1
    ThisWorkbook.Sheets("学生信息汇总表").Cells(1, 50).Value = runCount
    ThisWorkbook.Sheets("学生信息汇总表").Cells(1, 51).Value = currentDate

    ' 首先排序数据
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim sortRange As Range
    ' 设置源工作表
    Set ws = ThisWorkbook.Sheets("学生信息汇总表") '假设使用 Sheet1 来存储变数器和日期



    ' 获取源工作表的最后一行
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row

    Set sortRange = ws.Range("A1:I" & lastRow)

    Columns("A:I").Select
    ' 更新排序逻辑
    With ActiveSheet.Sort
        With .SortFields
            .Clear
            .Add key:=Range("A2:A" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:="思茅一小,思茅二小,思茅三小,思茅四小,思茅五小,思茅六小,思茅六中,思茅七小,倚象小学,云仙小学,龙潭小学,思茅港小,六顺小学,育英学校,博雅公学"
            .Add key:=Range("B2:B" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
            .Add key:=Range("C2:C" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:="一,二,三,四,五,六"
            .Add key:=Range("D2:D" & lastRow), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
        End With
        .Header = xlYes
        .Orientation = xlSortColumns
        .MatchCase = False
        .SortMethod = xlPinYin
        .SetRange rng:=Range("A1:I" & lastRow)
        .Apply
    End With

    ' 在I列生成随机数
    For i = 2 To lastRow
        ws.Cells(i, "I").Value = Rnd()
    Next i
'     执行排序
    Columns("A:I").Select
        '     设置排序范围
    Set sortRange = ws.Range("A1:I" & lastRow)
    With ActiveSheet.Sort
        With .SortFields
            .Clear
            .Add key:=Range("A2:A3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:="思茅一小,思茅二小,思茅三小,思茅四小,思茅五小,思茅六小,思茅六中,思茅七小,倚象小学,云仙小学,龙潭小学,思茅港小,六顺小学,育英学校,博雅公学"
            .Add key:=Range("B2:B3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
            .Add key:=Range("C2:C3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:="一,二,三,四,五,六"
            .Add key:=Range("I2:I3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
        End With
        .Header = xlYes
        .Orientation = xlSortColumns
        .MatchCase = False
        .SortMethod = xlPinYin
        .SetRange rng:=Range("A1:I" & lastRow)
        .Apply
    End With

    ' 显示用户窗体
    UserForm1.Show
End Sub
Sub ArrangeExaminationRooms(groupA As String, groupB As String, groupC As String, groupD As String, desksPerRoom As Integer)
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim gradeLevel As String
    Dim roomNumber As Integer
    Dim seatNumber As Integer
    Dim currentSchool As String
    Dim previousSchool As String
    Dim currentPoint As String
    Dim previousPoint As String
    Dim groups As Variant
    Dim studentDict As Object
    Dim student As Variant
    Dim studentID As Integer
    Dim groupIndex As Integer
    Dim schoolGroupDict As Object
    Dim schoolGroupKey As Variant
    Dim groupStudents As Variant
    Dim studentInfo As Variant
    Dim currentGroup As String
    Dim pairedStudents As Variant
    Dim pairIndex As Integer
    Dim remainingSeats As Integer ' 记录上一组剩余的座位数
    Dim dictKey As String
    Dim schoolGroupCombinedKey As String

    ' 设置工作表
    Set ws = ThisWorkbook.Sheets("学生信息汇总表")

    ' 获取最后一行
    lastRow = ws.Cells(ws.Rows.Count, "A").End(xlUp).Row


    ' 初始化变量
    roomNumber = 1
    seatNumber = 1
    previousSchool = ""
    previousPoint = ""
    remainingSeats = 0

    ' 将组信息存储在一个数组中
    groups = Array(groupA, groupB, groupC, groupD)

    ' 初始化学生字典（按学校和年级分组）
    Set studentDict = CreateObject("Scripting.Dictionary")

    ' 修改分组键生成逻辑
    For i = 2 To lastRow
        currentSchool = ws.Cells(i, "A").Value
        currentPoint = ws.Cells(i, "B").Value
        gradeLevel = ws.Cells(i, "C").Value
        currentClass = ws.Cells(i, "D").Value

        ' 生成分组键：学校+校点+年级+班级
        dictKey = currentSchool & "-" & currentPoint & "-" & gradeLevel & "-" & currentClass

        ' 初始化字典
        If Not studentDict.Exists(dictKey) Then
            Set studentDict(dictKey) = CreateObject("Scripting.Dictionary")
        End If

        studentID = studentDict(dictKey).Count + 1
        studentDict(dictKey).Add studentID, Array(ws.Cells(i, "E").Value, ws.Cells(i, "F").Value, ws.Cells(i, "G").Value, i)
    Next i

    ' 初始化学校和组的字典
    Set schoolGroupDict = CreateObject("Scripting.Dictionary")

    ' 遍历每个学校和年级组，按学校和组进行分组
    For Each schoolGradeKey In studentDict.Keys
        currentSchool = Split(schoolGradeKey, "-")(0)
        currentPoint = Split(schoolGradeKey, "-")(1)
        gradeLevel = Split(schoolGradeKey, "-")(2)
        currentClass = Split(schoolGradeKey, "-")(3)

        ' 确定年级组
        currentGroup = ""
        For groupIndex = 0 To UBound(groups)
            If groups(groupIndex) <> "" And InStr(groups(groupIndex), gradeLevel) > 0 Then
                currentGroup = "组" & (groupIndex + 1) ' 例如：组1, 组2
                Exit For
            End If
        Next groupIndex

        ' 如果未找到年级组，跳过
        If currentGroup = "" Then
            MsgBox "未找到年级组：" & gradeLevel, vbExclamation
            Exit Sub
        End If

        ' 生成学校和组的组合键（包含学校和校点）
        schoolGroupCombinedKey = currentSchool & "-" & currentPoint & "-" & currentGroup

        ' 如果字典中不存在该键，则初始化
        If Not schoolGroupDict.Exists(schoolGroupCombinedKey) Then
            Set schoolGroupDict(schoolGroupCombinedKey) = CreateObject("Scripting.Dictionary")
        End If

        ' 将学生添加到对应的学校和组中
        Set schoolGroupDict(schoolGroupCombinedKey)(gradeLevel) = studentDict(schoolGradeKey)
    Next schoolGradeKey

    ' 遍历每个学校和组，分配考场和座位
    On Error Resume Next ' 添加错误处理

    ' 输出调试信息，显示所有学校和校点
    Debug.Print "Total school-point-group combinations: " & schoolGroupDict.Count

    ' 创建一个数组来存储所有的键
    Dim allKeys As Variant
    ReDim allKeys(0 To schoolGroupDict.Count - 1)

    Dim keyIndex As Integer
    keyIndex = 0

    ' 将所有键复制到数组中
    For Each schoolGroupKey In schoolGroupDict.Keys
        allKeys(keyIndex) = schoolGroupKey
        keyIndex = keyIndex + 1
    Next schoolGroupKey

    ' 遍历数组中的所有键
    For keyIndex = 0 To UBound(allKeys)
        schoolGroupKey = allKeys(keyIndex)

        ' 检查键是否有效
        If Len(schoolGroupKey) = 0 Then
            Debug.Print "Empty key found at index: " & keyIndex
            GoTo ContinueLoop
        End If

        ' 检查键是否包含足够的分隔符
        If UBound(Split(schoolGroupKey, "-")) < 2 Then
            Debug.Print "Invalid key format: " & schoolGroupKey
            GoTo ContinueLoop
        End If

        currentSchool = Split(schoolGroupKey, "-")(0)
        currentPoint = Split(schoolGroupKey, "-")(1)
        currentGroup = Split(schoolGroupKey, "-")(2)

        Debug.Print "Processing: School=" & currentSchool & ", Point=" & currentPoint & ", Group=" & currentGroup

        ' 如果遇到不同学校或校点，重置考场号和座位号
        If previousSchool <> currentSchool Or previousPoint <> currentPoint Then
            roomNumber = 1
            seatNumber = 1
            remainingSeats = 0
            Debug.Print "Reset room numbers for new school/point: " & currentSchool & "/" & currentPoint
        ' 如果上一组有剩余座位，启用下一个考场
        ElseIf remainingSeats > 0 Then
            roomNumber = roomNumber + 1
            seatNumber = 1
            remainingSeats = 0
            Debug.Print "New room for remaining seats: Room=" & roomNumber
        End If

        ' 检查字典中是否存在该键
        If Not schoolGroupDict.Exists(schoolGroupKey) Then
            Debug.Print "Key not found in dictionary: " & schoolGroupKey
            GoTo ContinueLoop
        End If

        ' 获取当前学校和组的学生
        Set groupStudents = schoolGroupDict(schoolGroupKey)

        ' 检查学生组是否为空
        If groupStudents Is Nothing Then
            Debug.Print "No students found for: " & schoolGroupKey
            GoTo ContinueLoop
        End If

        ' 配对学生
        pairedStudents = PairStudents(groupStudents)

        ' 检查配对结果是否有效
        If Not IsArray(pairedStudents) Then
            Debug.Print "Invalid paired students result for: " & schoolGroupKey
            GoTo ContinueLoop
        End If

        ' 遍历配对的学生
        On Error Resume Next ' 添加错误处理
        For pairIndex = LBound(pairedStudents, 1) To UBound(pairedStudents, 1)
            ' 分配考场和座位号
            For i = LBound(pairedStudents, 2) To UBound(pairedStudents, 2)
                studentInfo = pairedStudents(pairIndex, i)
                If IsArray(studentInfo) Then
                    ws.Cells(studentInfo(3), "G").Value = roomNumber
                    ws.Cells(studentInfo(3), "H").Value = seatNumber
                    Debug.Print "Assigned: Room=" & roomNumber & ", Seat=" & seatNumber & " to student at row " & studentInfo(3)
                End If
            Next i

            ' 增加座位号
            seatNumber = seatNumber + 1

            ' 检查考场是否已满
            If seatNumber > desksPerRoom Then
                roomNumber = roomNumber + 1 ' 增加考场号
                seatNumber = 1 ' 重置座位号
                Debug.Print "Room full, new room: " & roomNumber
            End If
        Next pairIndex

        ' 记录上一组剩余的座位数
        remainingSeats = desksPerRoom - seatNumber + 1
        Debug.Print "Remaining seats: " & remainingSeats

        ' 更新上一个学校和校点信息
        previousSchool = currentSchool
        previousPoint = currentPoint
        Debug.Print "Updated previous school/point to: " & previousSchool & "/" & previousPoint

ContinueLoop:
    Next keyIndex

    On Error GoTo 0 ' 关闭错误处理
    Columns("A:I").Select
    With ActiveSheet.Sort
        With .SortFields
            .Clear
            .Add key:=Range("A2:A3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
            .Add key:=Range("B2:B3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
            .Add key:=Range("G2:G3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
            .Add key:=Range("H2:H3300"), SortOn:=xlSortOnValues, Order:=xlAscending, CustomOrder:=""
        End With
        .Header = xlYes
        .Orientation = xlSortColumns
        .MatchCase = False
        .SortMethod = xlPinYin
        .SetRange rng:=Range("A1:I6000")
        .Apply
    End With
End Sub
Function PairStudents(ByVal groupStudents As Object) As Variant
    Dim gradeLevels As Variant
    Dim numStudents As Long
    Dim maxStudents As Long
    Dim pairedStudents As Variant
    Dim i As Long
    Dim j As Long
    Dim k As Long
    Dim gradeCount As Long

    ' 获取所有年级
    gradeLevels = groupStudents.Keys

    ' 检查是否有年级
    If groupStudents.Count = 0 Then
        ' 如果没有年级，返回空数组
        ReDim pairedStudents(1 To 1, 1 To 1)
        pairedStudents(1, 1) = Empty
        PairStudents = pairedStudents
        Exit Function
    End If

    ' 找出最大学生数
    maxStudents = 0
    For Each gradeLevel In gradeLevels
        numStudents = groupStudents(gradeLevel).Count
        If numStudents > maxStudents Then
            maxStudents = numStudents
        End If
    Next gradeLevel

    ' 如果没有学生，返回空数组
    If maxStudents = 0 Then
        ReDim pairedStudents(1 To 1, 1 To 1)
        pairedStudents(1, 1) = Empty
        PairStudents = pairedStudents
        Exit Function
    End If

    ' 计算年级数量
    gradeCount = groupStudents.Count

    ' 初始化配对数组
    ReDim pairedStudents(1 To maxStudents, 1 To gradeCount)

    ' 配对学生：将不同年级的学生按顺序配对
    ' 对于每个年级，依次将学生放入配对数组中
    ' 如果某个年级的学生数量不足，则用 Empty 填充
    For i = 1 To maxStudents
        k = 1
        For Each gradeLevel In gradeLevels
            If i <= groupStudents(gradeLevel).Count Then
                pairedStudents(i, k) = groupStudents(gradeLevel)(i)
            Else
                pairedStudents(i, k) = Empty
            End If
            k = k + 1
        Next gradeLevel
    Next i
    PairStudents = pairedStudents
End Function
Function ShuffleArray(arr As Variant) As Variant
    Dim i As Long, j As Long
    Dim temp As Variant
    Dim arrayLength As Long

    ' 获取数组的长度
    arrayLength = UBound(arr) - LBound(arr) + 1

    ' 初始化随机数种子
    Randomize

    ' 使用 Fisher-Yates 洗牌算法随机打乱数组
    For i = arrayLength - 1 To 0 Step -1
        ' 随机选择一个索引 j，范围从 0 到 i
        j = Int(Rnd * (i + 1))

        ' 交换 arr(i) 和 arr(j)
        temp = arr(LBound(arr) + i)
        arr(LBound(arr) + i) = arr(LBound(arr) + j)
        arr(LBound(arr) + j) = temp
    Next i

    ' 返回打乱后的数组
    ShuffleArray = arr
End Function
' 更新列引用，假设“学校”列为 A 列，其他列依次后移
Sub UpdateColumns()
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Sheets("学生信息汇总表")

    ' 插入“学校”列
    ws.Columns("A").Insert Shift:=xlToRight
    ws.Cells(1, "A").Value = "学校"

    ' 填充“学校”列的值（假设从其他数据源获取）
    Dim lastRow As Long
    lastRow = ws.Cells(ws.Rows.Count, "B").End(xlUp).Row
    For i = 2 To lastRow
        ws.Cells(i, "A").Value = "学校名称" ' 根据实际情况填写
    Next i
End Sub
